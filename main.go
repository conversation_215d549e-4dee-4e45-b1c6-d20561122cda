package main

import (
	"net/http"
	"strconv"
	"transwarp.io/mlops/mlops-std/stdlog"
	"transwarp.io/mlops/pipeline/conf"
	"transwarp.io/mlops/pipeline/core"
	"transwarp.io/mlops/pipeline/core/client"
	"transwarp.io/mlops/pipeline/dao"
	"transwarp.io/mlops/pipeline/rpc"
	"transwarp.io/mlops/pipeline/rpc/api"
	"transwarp.io/mlops/pipeline/service"
)

func init() {

	conf.Init()
	stdlog.Init(conf.PipeineConfig.MLOPS.LogConfig, "pipeline")
	client.Init()
	dao.Init()
	core.Init()
	api.Init()

}

func main() {
	c := conf.PipeineConfig
	go rpc.StartGrpcServer(c)
	service.MountCompletedAPIModule()
	e := http.ListenAndServe(":"+strconv.Itoa(c.MLOPS.HttpServer.Port), nil)
	stdlog.WithError(e).Errorf("fail to start web server")
}
