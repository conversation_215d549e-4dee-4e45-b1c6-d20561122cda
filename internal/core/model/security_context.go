package model

import (
	v1 "k8s.io/api/core/v1"
	"transwarp.io/aip/llmops-common/pb/common"
)

type SecurityContext struct {
	Privileged bool
	RunAsUser  int64
}

func (s *SecurityContext) FromPb(pb *common.SecurityContext) {
	if pb == nil {
		return
	}
	s.RunAsUser = pb.RunAsUser
	s.Privileged = pb.Privileged
}
func (s *SecurityContext) ToPb() *common.SecurityContext {
	if s == nil {
		return nil
	}
	return &common.SecurityContext{
		RunAsUser:  s.RunAsUser,
		Privileged: s.Privileged,
	}
}
func (s *SecurityContext) ToK8s() *v1.SecurityContext {
	if s == nil {
		return nil
	}
	return &v1.SecurityContext{
		Privileged: &s.Privileged,
		RunAsUser:  &s.Run<PERSON>User,
	}
}

func (s *SecurityContext) FromK8s(securityContext *v1.SecurityContext) *SecurityContext {
	s.RunAsUser = *securityContext.RunAsUser
	s.Privileged = *securityContext.Privileged
	return s
}