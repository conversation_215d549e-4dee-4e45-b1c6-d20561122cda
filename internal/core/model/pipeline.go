package model

import (
	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	"transwarp.io/mlops/pipeline/dao/model"
)

type Pipeline struct {
	Id         string
	Name       string
	CreateUser string
	CreateTime int64
	Desc       string
	ProjectId  string
}

func (pipeline *Pipeline) FromPb(pb *pb.Pipeline) *Pipeline {
	pipeline.Id = pb.Id
	pipeline.Name = pb.Name
	pipeline.CreateUser = pb.CreateUser
	pipeline.CreateTime = pb.CreateTime
	pipeline.Desc = pb.Desc
	pipeline.ProjectId = pb.ProjectId
	return pipeline
}

func (pipeline *Pipeline) ToPb() *pb.Pipeline {
	return &pb.Pipeline{
		Id:         pipeline.Id,
		Name:       pipeline.Name,
		CreateUser: pipeline.CreateUser,
		CreateTime: pipeline.CreateTime,
		Desc:       pipeline.Desc,
		ProjectId:  pipeline.ProjectId,
	}
}

func (pipeline *Pipeline) FromModel(model *model.Pipeline) *Pipeline {
	pipeline.Id = model.ID
	pipeline.Name = model.Name
	pipeline.CreateUser = model.CreateUser
	pipeline.CreateTime = model.CreateTime.Unix()
	pipeline.Desc = model.Desc
	pipeline.ProjectId = model.ProjectID
	return pipeline
}

func (pipeline *Pipeline) ToModel() *model.Pipeline {
	return &model.Pipeline{
		ID:         pipeline.Id,
		Name:       pipeline.Name,
		CreateUser: pipeline.CreateUser,
		Desc:       pipeline.Desc,
		ProjectID:  pipeline.ProjectId,
	}
}
