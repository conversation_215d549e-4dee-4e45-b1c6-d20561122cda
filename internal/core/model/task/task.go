package task

import (
	"time"

	"k8s.io/apimachinery/pkg/runtime/schema"
	kueuev1beta1 "sigs.k8s.io/kueue/apis/kueue/v1beta1"
)

type Task struct {
	ID string `json:"id"`

	Name        string       `json:"name"`
	Type        TaskType     `json:"type"`
	Priority    TaskPriority `json:"priority"`
	Description string       `json:"description"`
	Creator     string       `json:"creator"`
	CreateAt    time.Time    `json:"create_at"`
	UpdateAt    time.Time    `json:"update_at"`
	Status      TaskStatus   `json:"status"`
	StartAt     *time.Time   `json:"start_at"`
	EndAt       *time.Time   `json:"end_at"`

	TenantID    string `json:"tenant_id"`
	TenantName  string `json:"tenant_name"`
	ProjectID   string `json:"project_id"`
	ProjectName string `json:"project_name"`

	Resources    *Resources        `json:"resources"`
	RunningNodes []*Node           `json:"running_nodes"`
	Extra        map[string]string `json:"extra"`

	SubTasks []*SubTask `json:"sub_tasks"`

	QueueName        string `json:"queue_name"`
	ClusterQueueName string `json:"-"`

	Namespace   string                      `json:"-"`
	Labels      map[string]string           `json:"-"`
	Annotations map[string]string           `json:"-"`
	GVK         schema.GroupVersionKind     `json:"gvk"`
	GVR         schema.GroupVersionResource `json:"-"`
}

type GPUResource struct {
	Name      string `json:"name"`
	AliasName string `json:"alias_name"`
	Count     string `json:"count"`
	Memory    string `json:"memory"`
}

type ResourceSpec struct {
	CPU    string         `json:"cpu"`
	Memory string         `json:"memory"`
	GPU    []*GPUResource `json:"gpu"`
}

type Resources struct {
	Requests ResourceSpec  `json:"requests"`
	Limits   ResourceSpec  `json:"limits"`
	Used     *ResourceSpec `json:"used"`
}

type TaskType string

const (
	TaskTypePipeline    TaskType = "PIPELINE"
	TaskTypeTraining    TaskType = "TRAINING"
	TaskTypeInference   TaskType = "INFERENCE"
	TaskTypeEvaluation  TaskType = "EVALUATION"
	TaskTypeDataProcess TaskType = "DATA_PROCESS"
	TaskTypeCustom      TaskType = "CUSTOM"
)

type TaskPriority int32

const (
	PriorityLow    TaskPriority = 10
	PriorityMedium TaskPriority = 100
	PriorityHigh   TaskPriority = 1000
)

type TaskStatus string

const (
	StatusPending TaskStatus = "pending"
	// StatusScheduled TaskStatus = "scheduled"
	StatusRunning TaskStatus = "running"
	// StatusPaused    TaskStatus = "paused"
	StatusSucceeded TaskStatus = "succeeded"
	StatusFailed    TaskStatus = "failed"
	// StatusRetrying  TaskStatus = "retrying"
	StatusCancelled TaskStatus = "cancelled"
	// StatusTimeout   TaskStatus = "timeout"

	// StatusSuspended TaskStatus = "suspended"
)

type SubTask struct {
	ID           string                 `json:"id"`
	Name         string                 `json:"name"`
	ParentTaskID string                 `json:"parent_task_id"`
	WorkloadRef  *kueuev1beta1.Workload `json:"-"`
	Status       SubTaskStatus          `json:"status"`
	CreateAt     time.Time              `json:"create_at"`
	StartAt      *time.Time             `json:"start_at"`
	EndAt        *time.Time             `json:"end_at"`

	Resources *Resources `json:"resources"`

	Pods []*Pod `json:"pods"`
}

type WorkloadRef struct {
	Name      string `json:"name"`
	Namespace string `json:"namespace"`
	UID       string `json:"uid"`
}

type Pod struct {
	Name      string     `json:"name"`
	Namespace string     `json:"namespace"`
	NodeName  string     `json:"node_name"`
	Phase     string     `json:"phase"`
	IP        string     `json:"ip"`
	StartAt   *time.Time `json:"start_at"`
	EndAt     *time.Time `json:"end_at"`

	Resources *Resources `json:"resources"`

	// Events []*PodEvent `json:"events"`
	// Logs   []*PodLog   `json:"logs"`

	// Node *Node `json:"node"`
}

type PodEvent struct {
	Reason    string    `json:"reason"`
	Message   string    `json:"message"`
	Type      string    `json:"type"`
	Source    string    `json:"source"`
	FirstSeen time.Time `json:"first_seen"`
	LastSeen  time.Time `json:"last_seen"`
	Count     int       `json:"count"`
}

type PodLog struct {
	Timestamp time.Time `json:"timestamp"`
	Message   string    `json:"message"`
	Level     string    `json:"level"`
}

type SubTaskStatus string

const (
	SubTaskPending   SubTaskStatus = "pending"
	SubTaskRunning   SubTaskStatus = "running"
	SubTaskSucceeded SubTaskStatus = "succeeded"
	SubTaskFailed    SubTaskStatus = "failed"
)

type Node struct {
	ID     string
	Name   string
	IP     string
	Status string
	Labels map[string]string
}
