package task

import (
	"strings"
	"time"

	"k8s.io/apimachinery/pkg/runtime/schema"
	kueuev1beta1 "sigs.k8s.io/kueue/apis/kueue/v1beta1"
)

type Task struct {
	ID string `json:"id"`

	Name        string       `json:"name"`
	Type        TaskType     `json:"type"`
	Priority    TaskPriority `json:"priority"`
	Description string       `json:"description"`
	Creator     string       `json:"creator"`
	CreatedAt   time.Time    `json:"created_at"`
	UpdatedAt   time.Time    `json:"updated_at"`
	Status      TaskStatus   `json:"status"`
	StartedAt   *time.Time   `json:"started_at"`
	EndedAt     *time.Time   `json:"ended_at"`

	TenantID    string `json:"tenant_id"`
	TenantName  string `json:"tenant_name"`
	ProjectID   string `json:"project_id"`
	ProjectName string `json:"project_name"`

	Extra map[string]string `json:"extra"`

	Resources    *Resources `json:"resources"`
	RunningNodes []*Node    `json:"running_nodes"`
	SubTasks     []*SubTask `json:"sub_tasks"`
	Ranking      int        `json:"ranking"`

	QueueName        string `json:"queue_name"`
	ClusterQueueName string `json:"cluster_queue_name"`

	Namespace   string                      `json:"-"`
	Labels      map[string]string           `json:"-"`
	Annotations map[string]string           `json:"-"`
	GVK         schema.GroupVersionKind     `json:"gvk"`
	GVR         schema.GroupVersionResource `json:"-"`
}

type GPUResource struct {
	Name      string `json:"name"`
	AliasName string `json:"alias_name"`
	Count     string `json:"count"`
	Memory    string `json:"memory"`
}

type ResourceSpec struct {
	CPU    string         `json:"cpu"`
	Memory string         `json:"memory"`
	GPU    []*GPUResource `json:"gpu"`
}

type Resources struct {
	Requests ResourceSpec  `json:"requests"`
	Limits   ResourceSpec  `json:"limits"`
	Used     *ResourceSpec `json:"used"`
}

type TaskType string

const (
	TaskTypePipeline         TaskType = "PIPELINE"
	TaskTypeTraining         TaskType = "TRAINING"
	TaskTypeInference        TaskType = "INFERENCE"
	TaskTypeModelEvaluation  TaskType = "MODEL_EVALUATION"
	TaskTypeModelFineTuning  TaskType = "MODEL_FINE_TUNING"
	TaskTypeCorupsProcessing TaskType = "CORUPS_PROCESSING"
	TaskTypeCorupsEvaluation TaskType = "CORUPS_EVALUATION"
	TaskTypeCustom           TaskType = "CUSTOM"
)

func ToTaskType(t string) TaskType {
	switch strings.ToUpper(t) {
	case string(TaskTypePipeline):
		return TaskTypePipeline
	case string(TaskTypeTraining):
		return TaskTypeTraining
	case string(TaskTypeInference):
		return TaskTypeInference
	case string(TaskTypeModelEvaluation):
		return TaskTypeModelEvaluation
	case string(TaskTypeModelFineTuning):
		return TaskTypeModelFineTuning
	case string(TaskTypeCorupsProcessing):
		return TaskTypeCorupsProcessing
	case string(TaskTypeCorupsEvaluation):
		return TaskTypeCorupsEvaluation
	default:
		return TaskTypeCustom
	}
}

type TaskPriority string

const (
	TaskPriorityDefault TaskPriority = "0"

	TaskPriorityLow    TaskPriority = "10"
	TaskPriorityMedium TaskPriority = "100"
	TaskPriorityHigh   TaskPriority = "1000"
)

func ToTaskPriority(p *int32) TaskPriority {
	if p == nil {
		return TaskPriorityDefault
	} else if *p < 50 {
		return TaskPriorityLow
	} else if *p < 500 {
		return TaskPriorityMedium
	}
	return TaskPriorityHigh
}

type TaskStatus string

const (
	TaskStatusPending TaskStatus = "pending"
	// TaskStatusScheduled TaskStatus = "scheduled"
	TaskStatusRunning TaskStatus = "running"
	// StatusPaused    TaskStatus = "paused"
	TaskStatusSucceeded TaskStatus = "succeeded"
	TaskStatusFailed    TaskStatus = "failed"
	// StatusRetrying  TaskStatus = "retrying"
	TaskStatusCancelled TaskStatus = "cancelled"
	TaskStatusUnknown   TaskStatus = "unknown"
	// TaskStatusTimeout   TaskStatus = "timeout"

	// TaskStatusSuspended TaskStatus = "suspended"
)

// subTask == kueue workload
type SubTask struct {
	ID           string                 `json:"id"`
	Name         string                 `json:"name"`
	ParentTaskID string                 `json:"parent_task_id"`
	WorkloadRef  *kueuev1beta1.Workload `json:"-"`
	Status       SubTaskStatus          `json:"status"`
	CreatedAt    time.Time              `json:"created_at"`
	StartedAt    *time.Time             `json:"started_at"`
	EndedAt      *time.Time             `json:"ended_at"`

	Resources *Resources `json:"resources"`

	Pods []*Pod `json:"pods"`
}

type WorkloadRef struct {
	Name      string `json:"name"`
	Namespace string `json:"namespace"`
	UID       string `json:"uid"`
}

type Pod struct {
	Name      string     `json:"name"`
	Namespace string     `json:"namespace"`
	NodeName  string     `json:"node_name"`
	Phase     string     `json:"phase"`
	IP        string     `json:"ip"`
	StartAt   *time.Time `json:"start_at"`
	EndAt     *time.Time `json:"end_at"`

	Resources *Resources `json:"resources"`

	// Events []*PodEvent `json:"events"`
	// Logs   []*PodLog   `json:"logs"`

	// Node *Node `json:"node"`
}

type PodEvent struct {
	Reason    string    `json:"reason"`
	Message   string    `json:"message"`
	Type      string    `json:"type"`
	Source    string    `json:"source"`
	FirstSeen time.Time `json:"first_seen"`
	LastSeen  time.Time `json:"last_seen"`
	Count     int       `json:"count"`
}

type PodLog struct {
	Timestamp time.Time `json:"timestamp"`
	Message   string    `json:"message"`
	Level     string    `json:"level"`
}

type SubTaskStatus string

const (
	SubTaskAdmitted           = "Admitted"
	SubTaskQuotaReserved      = "QuotaReserved"
	SubTaskFinished           = "Finished"
	SubTaskPodsReady          = "PodsReady"
	SubTaskEvicted            = "Evicted"
	SubTaskPreempted          = "Preempted"
	SubTaskRequeued           = "Requeued"
	SubTaskDeactivationTarget = "DeactivationTarget"
)

type Node struct {
	ID     string
	Name   string
	IP     string
	Status string
	Labels map[string]string
}

func (t *Task) UpdateTaskStatus() *Task {
	allFinished := true
	anyRunning := false
	anyFailed := false
	anyPending := false
	for _, st := range t.SubTasks {
		switch st.Status {
		case SubTaskFinished:
			allFinished = false
		case SubTaskAdmitted, SubTaskPodsReady:
			// These indicate the task is running or about to run (already scheduled)
			anyRunning = true
			allFinished = false
		case SubTaskQuotaReserved, SubTaskEvicted, SubTaskPreempted, SubTaskRequeued, SubTaskDeactivationTarget:
			// These indicate states where the subtask is not actively running but waiting or paused
			anyPending = true
			allFinished = false
		default:
			// Handle any other unexpected or custom status
			// For robustness, you might treat unknown as `anyPending` or `anyRunning` based on context
			anyPending = true
			allFinished = false
		}
	}
	if anyFailed {
		t.Status = TaskStatusFailed
	} else if anyRunning {
		t.Status = TaskStatusRunning
	} else if allFinished { // Only if no running/failed and all are finished
		t.Status = TaskStatusSucceeded
	} else if anyPending { // If no running/failed/all-finished, and some are pending
		t.Status = TaskStatusPending
	} else {
		// Fallback for unexpected scenarios, or if all subtasks are in an unhandled state
		t.Status = TaskStatusUnknown
	}
	return t
}
func (t *Task) UpdateTaskStartedAt() *Task {
	for _, st := range t.SubTasks {
		if st.StartedAt != nil {
			if t.StartedAt == nil || st.StartedAt.Before(*t.StartedAt) {
				t.StartedAt = st.StartedAt
			}
		}
	}
	return t
}
func (t *Task) UpdateTaskEndedAt() *Task {
	for _, st := range t.SubTasks {
		if st.EndedAt != nil {
			if t.EndedAt == nil || st.EndedAt.After(*t.EndedAt) {
				t.EndedAt = st.EndedAt
			}
		} else {
			t.EndedAt = nil
			break
		}
	}
	return t
}
