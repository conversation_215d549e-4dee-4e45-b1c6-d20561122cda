package task

import (
	"sort"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"

	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/apimachinery/pkg/runtime/schema"
	kueuev1beta1 "sigs.k8s.io/kueue/apis/kueue/v1beta1"
)

type Task struct {
	ID string `json:"id"`

	Name        string       `json:"name"`
	Type        TaskType     `json:"type"`
	Priority    TaskPriority `json:"priority"`
	Description string       `json:"description"`
	Creator     string       `json:"creator"`
	CreatedAt   time.Time    `json:"created_at"`
	UpdatedAt   time.Time    `json:"updated_at"`
	Status      TaskStatus   `json:"status"`
	StartedAt   *time.Time   `json:"started_at"`
	EndedAt     *time.Time   `json:"ended_at"`

	TenantID    string `json:"tenant_id"`
	TenantName  string `json:"tenant_name"`
	ProjectID   string `json:"project_id"`
	ProjectName string `json:"project_name"`

	RefSourceID string `json:"ref_source_id"`

	Extra map[string]string `json:"extra"`

	Resources      *Resources    `json:"resources"`
	NodeNames      []string      `json:"node_names"`
	SubTasks       []*SubTask    `json:"sub_tasks"`
	Rank           int           `json:"rank"`
	PodCount       int           `json:"pod_count"`
	QueuedDuration time.Duration `json:"queued_duration"`

	QueueName        string `json:"queue_name"`
	ClusterQueueName string `json:"cluster_queue_name"`

	Namespace   string                      `json:"-"`
	Labels      map[string]string           `json:"-"`
	Annotations map[string]string           `json:"-"`
	GVK         schema.GroupVersionKind     `json:"gvk"`
	GVR         schema.GroupVersionResource `json:"-"`
}

type GPUResource struct {
	Name   string  `json:"name"`
	Count  float64 `json:"count"`
	Cores  int64   `json:"-"`
	Memory string  `json:"memory"`
}

type ResourceSpec struct {
	CPU    string         `json:"cpu"`
	Memory string         `json:"memory"`
	GPU    []*GPUResource `json:"gpu"`
}

type Resources struct {
	Requests ResourceSpec  `json:"requests"`
	Limits   ResourceSpec  `json:"limits"`
	Used     *ResourceSpec `json:"used"`
}

func (r *Resources) Add(other *Resources) {
	if other == nil {
		return
	}

	// Add CPU and Memory
	r.Requests.CPU = addResourceQuantity(r.Requests.CPU, other.Requests.CPU)
	r.Requests.Memory = addResourceQuantity(r.Requests.Memory, other.Requests.Memory)
	r.Limits.CPU = addResourceQuantity(r.Limits.CPU, other.Limits.CPU)
	r.Limits.Memory = addResourceQuantity(r.Limits.Memory, other.Limits.Memory)

	// Add GPU resources
	r.Requests.GPU = mergeGPUResources(r.Requests.GPU, other.Requests.GPU)
	r.Limits.GPU = mergeGPUResources(r.Limits.GPU, other.Limits.GPU)
}

func mergeGPUResources(existing, other []*GPUResource) []*GPUResource {
	if len(other) == 0 {
		return existing
	}

	// Create a map to store merged GPU resources
	gpuMap := make(map[string]*GPUResource)

	// Add existing GPU resources to map
	for _, gpu := range existing {
		gpuMap[gpu.Name] = &GPUResource{
			Name:   gpu.Name,
			Count:  gpu.Count,
			Cores:  gpu.Cores,
			Memory: gpu.Memory,
		}
	}

	// Merge other GPU resources
	for _, gpu := range other {
		if existingGPU, ok := gpuMap[gpu.Name]; ok {
			// Add resources for existing GPU type
			existingGPU.Count = existingGPU.Count + gpu.Count
			existingGPU.Cores = existingGPU.Cores + gpu.Cores
			existingGPU.Memory = addResourceQuantity(existingGPU.Memory, gpu.Memory)
		} else {
			// Add new GPU type
			gpuMap[gpu.Name] = &GPUResource{
				Name:   gpu.Name,
				Count:  gpu.Count,
				Cores:  gpu.Cores,
				Memory: gpu.Memory,
			}
		}
	}

	// Convert map back to slice
	result := make([]*GPUResource, 0, len(gpuMap))
	for _, gpu := range gpuMap {
		result = append(result, gpu)
	}

	// Sort result by GPU name for consistent ordering
	sort.Slice(result, func(i, j int) bool {
		return result[i].Name < result[j].Name
	})

	return result
}
func addResourceQuantity(a, b string) string {
	if a == "" {
		return b
	}
	if b == "" {
		return a
	}

	qa, err := resource.ParseQuantity(a)
	if err != nil {
		return a
	}

	qb, err := resource.ParseQuantity(b)
	if err != nil {
		return a
	}

	qa.Add(qb)
	return qa.String()
}

type TaskType string

const (
	TaskTypePipeline         TaskType = "PIPELINE"
	TaskTypeTraining         TaskType = "TRAINING"
	TaskTypeInference        TaskType = "INFERENCE"
	TaskTypeModelEvaluation  TaskType = "MODEL_EVALUATION"
	TaskTypeModelFineTuning  TaskType = "MODEL_FINE_TUNING"
	TaskTypeCorupsProcessing TaskType = "CORUPS_PROCESSING"
	TaskTypeCorupsEvaluation TaskType = "CORUPS_EVALUATION"
	TaskTypeCustom           TaskType = "CUSTOM"
)

func ToTaskType(t string) TaskType {
	switch strings.ToUpper(t) {
	case string(TaskTypePipeline):
		return TaskTypePipeline
	case string(TaskTypeTraining):
		return TaskTypeTraining
	case string(TaskTypeInference):
		return TaskTypeInference
	case string(TaskTypeModelEvaluation):
		return TaskTypeModelEvaluation
	case string(TaskTypeModelFineTuning):
		return TaskTypeModelFineTuning
	case string(TaskTypeCorupsProcessing):
		return TaskTypeCorupsProcessing
	case string(TaskTypeCorupsEvaluation):
		return TaskTypeCorupsEvaluation
	default:
		return TaskTypeCustom
	}
}

type TaskPriority string

const (
	TaskPriorityDefault TaskPriority = "0"

	TaskPriorityLow    TaskPriority = "10"
	TaskPriorityMedium TaskPriority = "100"
	TaskPriorityHigh   TaskPriority = "1000"
)

func (tp *TaskPriority) ToInt() int {
	switch *tp {
	case TaskPriorityHigh:
		return 1000
	case TaskPriorityMedium:
		return 100
	case TaskPriorityLow:
		return 10
	default:
		return 0
	}
}

func ToTaskPriority(p *int32) TaskPriority {
	if p == nil {
		return TaskPriorityDefault
	} else if *p < 50 {
		return TaskPriorityLow
	} else if *p < 500 {
		return TaskPriorityMedium
	}
	return TaskPriorityHigh
}

type TaskStatus string

const (
	TaskStatusPending  TaskStatus = "pending"
	TaskStatusAdmitted TaskStatus = "admitted"
	TaskStatusRunning  TaskStatus = "running"
	// StatusPaused    TaskStatus = "paused"
	TaskStatusSucceeded TaskStatus = "succeeded"
	TaskStatusFailed    TaskStatus = "failed"
	// StatusRetrying  TaskStatus = "retrying"
	TaskStatusCancelled TaskStatus = "cancelled"
	TaskStatusUnknown   TaskStatus = "unknown"
	// TaskStatusTimeout   TaskStatus = "timeout"

	// TaskStatusSuspended TaskStatus = "suspended"
)

// subTask == kueue workload
type SubTask struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	WorkloadRef *kueuev1beta1.Workload `json:"-"`
	Status      SubTaskStatus          `json:"status"`
	CreatedAt   time.Time              `json:"created_at"`
	StartedAt   *time.Time             `json:"started_at"`
	EndedAt     *time.Time             `json:"ended_at"`

	Resources *Resources `json:"resources"`

	Pods []*Pod `json:"pods"`
}

func (st *SubTask) RunningNodeNames() []string {
	nodeNames := make([]string, 0)
	for _, pod := range st.Pods {
		if pod.NodeName != "" {
			nodeNames = append(nodeNames, pod.NodeName)
		}
	}
	return nodeNames
}

type WorkloadRef struct {
	Name      string `json:"name"`
	Namespace string `json:"namespace"`
	UID       string `json:"uid"`
}

type Pod struct {
	Name      string          `json:"name"`
	Namespace string          `json:"namespace"`
	NodeName  string          `json:"node_name"`
	Phase     corev1.PodPhase `json:"phase"`
	HostIP    string          `json:"host_ip"`
	PodIP     string          `json:"-"`
	StartedAt *time.Time      `json:"started_at"`
	EndedAt   *time.Time      `json:"ended_at"`

	Resources *Resources `json:"resources"`

	// Events []*PodEvent `json:"events"`
	// Logs   []*PodLog   `json:"logs"`

	// Node *Node `json:"node"`
	Containers []*Container `json:"containers"`
}

type Container struct {
	Name      string     `json:"name"`
	Image     string     `json:"image"`
	Resources *Resources `json:"resources"`
}

type PodEvent struct {
	Reason    string    `json:"reason"`
	Message   string    `json:"message"`
	Type      string    `json:"type"`
	Source    string    `json:"source"`
	FirstSeen time.Time `json:"first_seen"`
	LastSeen  time.Time `json:"last_seen"`
	Count     int       `json:"count"`
}

type PodLog struct {
	Timestamp time.Time `json:"timestamp"`
	Message   string    `json:"message"`
	Level     string    `json:"level"`
}

type SubTaskStatus string

const (
	SubTaskAdmitted           = "Admitted"
	SubTaskQuotaReserved      = "QuotaReserved"
	SubTaskFinished           = "Finished"
	SubTaskPodsReady          = "PodsReady"
	SubTaskEvicted            = "Evicted"
	SubTaskPreempted          = "Preempted"
	SubTaskRequeued           = "Requeued"
	SubTaskDeactivationTarget = "DeactivationTarget"
)

type Node struct {
	ID     string
	Name   string
	IP     string
	Status string
	Labels map[string]string
}

func (t *Task) UpdateTaskStatus() *Task {
	allFinished := true
	anyRunning := false
	anyFailed := false
	anyPending := false
	for _, st := range t.SubTasks {
		switch st.Status {
		case SubTaskFinished:
			allFinished = false
		case SubTaskAdmitted, SubTaskPodsReady:
			// These indicate the task is running or about to run (already scheduled)
			anyRunning = true
			allFinished = false
		case SubTaskQuotaReserved, SubTaskEvicted, SubTaskPreempted, SubTaskRequeued, SubTaskDeactivationTarget:
			// These indicate states where the subtask is not actively running but waiting or paused
			anyPending = true
			allFinished = false
		default:
			// Handle any other unexpected or custom status
			// For robustness, you might treat unknown as `anyPending` or `anyRunning` based on context
			anyPending = true
			allFinished = false
		}
	}
	if anyFailed {
		t.Status = TaskStatusFailed
	} else if anyRunning {
		t.Status = TaskStatusRunning
	} else if allFinished { // Only if no running/failed and all are finished
		t.Status = TaskStatusSucceeded
	} else if anyPending { // If no running/failed/all-finished, and some are pending
		t.Status = TaskStatusPending
	} else {
		// Fallback for unexpected scenarios, or if all subtasks are in an unhandled state
		t.Status = TaskStatusUnknown
	}
	return t
}
func (t *Task) UpdateTaskStartedAt() *Task {
	for _, st := range t.SubTasks {
		if st.StartedAt != nil {
			if t.StartedAt == nil || st.StartedAt.Before(*t.StartedAt) {
				t.StartedAt = st.StartedAt
			}
		}
	}
	return t
}
func (t *Task) UpdateTaskEndedAt() *Task {
	for _, st := range t.SubTasks {
		if st.EndedAt != nil {
			if t.EndedAt == nil || st.EndedAt.After(*t.EndedAt) {
				t.EndedAt = st.EndedAt
			}
		} else {
			t.EndedAt = nil
			break
		}
	}
	return t
}
func (t *Task) UpdateQueueDuration() *Task {
	switch t.Status {
	case TaskStatusPending:
		t.QueuedDuration = time.Since(t.CreatedAt)
	case TaskStatusAdmitted, TaskStatusRunning, TaskStatusSucceeded, TaskStatusFailed, TaskStatusCancelled:
		if t.StartedAt != nil {
			t.QueuedDuration = t.StartedAt.Sub(t.CreatedAt)
		}
	}
	return t
}
