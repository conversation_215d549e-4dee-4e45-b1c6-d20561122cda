package model

import (
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
	"transwarp.io/aip/llmops-common/pb/common"
)

type Handler struct {
	Exec      *common.ExecAction
	HttpGet   *common.HTTPGetAction
	TcpSocket *common.TCPSocketAction
}

func (h *Handler) FromK8s(handler *v1.Handler) *Handler {
	if handler.Exec !=nil {
		h.Exec = &common.ExecAction{
			Command: h.Exec.Command,
		}
	}
	if handler.HTTPGet != nil {
		var port *common.IntOrString
		switch handler.HTTPGet.Port.Type {
		case intstr.Int:
			port = &common.IntOrString{
				Type:   common.IntOrString_int,
				IntVal: handler.HTTPGet.Port.IntVal,
			}
		case intstr.String:
			port = &common.IntOrString{
				Type:   common.IntOrString_string,
				StrVal: handler.HTTPGet.Port.StrVal,
			}
		}
		var scheme common.URIScheme
		switch h.HttpGet.Scheme {
		case common.URIScheme_HTTP:
			scheme = common.URIScheme_HTTP
		case common.URIScheme_HTTPS:
			scheme = common.URIScheme_HTTPS
		}
		httpHeaders := make([]*common.HTTPHeader, 0)
		for _, header := range h.HttpGet.HttpHeaders {
			httpHeaders = append(httpHeaders, &common.HTTPHeader{
				Name:  header.Name,
				Value: header.Value,
			})
		}
		h.HttpGet = &common.HTTPGetAction{
			Path:        handler.HTTPGet.Path,
			Port:        port,
			Host:        handler.HTTPGet.Host,
			Scheme:      scheme,
			HttpHeaders: httpHeaders,
		}
	}
	if handler.TCPSocket != nil {
		var port *common.IntOrString
		switch handler.TCPSocket.Port.Type {
		case intstr.Int:
			port = &common.IntOrString{
				Type:   common.IntOrString_int,
				IntVal: handler.TCPSocket.Port.IntVal,
			}
		case intstr.String:
			port = &common.IntOrString{
				Type:   common.IntOrString_string,
				StrVal: handler.TCPSocket.Port.StrVal,
			}
		}
		h.TcpSocket = &common.TCPSocketAction{
				Port: port,
				Host: handler.TCPSocket.Host,
		}
	}
	return h
}

func (h *Handler) ToK8s() *v1.Handler {
	if h == nil {
		return nil
	}
	if h.Exec != nil {
		return &v1.Handler{
			Exec: &v1.ExecAction{
				Command: h.Exec.Command,
			},
		}
	}
	if h.HttpGet != nil {
		var port intstr.IntOrString
		switch h.HttpGet.Port.Type {
		case common.IntOrString_int:
			port = intstr.IntOrString{
				Type:   intstr.Int,
				IntVal: h.HttpGet.Port.IntVal,
			}
		case common.IntOrString_string:
			port = intstr.IntOrString{
				Type:   intstr.String,
				StrVal: h.HttpGet.Port.StrVal,
			}
		}
		var scheme v1.URIScheme
		switch h.HttpGet.Scheme {
		case common.URIScheme_HTTP:
			scheme = v1.URISchemeHTTP
		case common.URIScheme_HTTPS:
			scheme = v1.URISchemeHTTPS
		}
		httpHeaders := make([]v1.HTTPHeader, 0)
		for _, header := range h.HttpGet.HttpHeaders {
			httpHeaders = append(httpHeaders, v1.HTTPHeader{
				Name:  header.Name,
				Value: header.Value,
			})
		}

		return &v1.Handler{
			HTTPGet: &v1.HTTPGetAction{
				Path:        h.HttpGet.Path,
				Port:        port,
				Host:        h.HttpGet.Host,
				Scheme:      scheme,
				HTTPHeaders: httpHeaders,
			},
		}
	}
	if h.TcpSocket != nil {
		var port intstr.IntOrString
		switch h.HttpGet.Port.Type {
		case common.IntOrString_int:
			port = intstr.IntOrString{
				Type:   intstr.Int,
				IntVal: h.HttpGet.Port.IntVal,
			}
		case common.IntOrString_string:
			port = intstr.IntOrString{
				Type:   intstr.String,
				StrVal: h.HttpGet.Port.StrVal,
			}
		}
		return &v1.Handler{
			TCPSocket: &v1.TCPSocketAction{
				Port: port,
				Host: h.HttpGet.Host,
			},
		}
	}
	return nil
}
func (h *Handler) FromPb(pb *common.Handler) *Handler{
	h.TcpSocket = pb.TcpSocket
	h.HttpGet = pb.HttpGet
	h.Exec = pb.Exec
	return h

}
func (h *Handler) ToPb() *common.Handler {
	if h == nil {
		return nil
	}
	return &common.Handler{
		Exec: h.Exec,
		HttpGet: h.HttpGet,
		TcpSocket: h.TcpSocket,
	}
}