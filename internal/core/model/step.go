package model

import (
	"fmt"
	"github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	pb "transwarp.io/aip/llmops-common/pb/pipeline"
)

type Step struct {
	NodeId    string
	Name      string
	Phase     pb.Step_Phase
	Params    map[string]string
	StartTime int64
	EndTime   int64
	Inputs    []*pb.Artifact
	Outputs   []*pb.Artifact
	Metrics   map[string]string
}

func (step *Step) ToPb() *pb.Step {
	return &pb.Step{
		NodeId:    step.NodeId,
		Name:      step.Name,
		Phase:     step.Phase,
		Params:    step.Params,
		StartTime: step.StartTime,
		EndTime:   step.EndTime,
		Inputs:    step.Inputs,
		Outputs:   step.Outputs,
		Metrics:   step.Metrics,
	}
}
func (step *Step) FromWorkflow(nodeStatus *v1alpha1.NodeStatus, nodeName string) *Step {
	var inputs []*pb.Artifact
	var outputs []*pb.Artifact
	params := map[string]string{}
	if nodeStatus.Inputs != nil {
		for _, param := range nodeStatus.Inputs.Parameters {
			params[param.Name] = string(*param.Value)
		}
		for _, input := range nodeStatus.Inputs.Artifacts {
			inputs = append(inputs, &pb.Artifact{
				Name:          input.Name,
				ContainerPath: input.Path,
				S3Path:        fmt.Sprintf("minio://%s/%s", input.S3.Bucket, input.S3.Key),
				Type:          pb.ArtifactType_INPUT,
			})
		}
	}

	if nodeStatus.Outputs != nil {
		for _, output := range nodeStatus.Outputs.Artifacts {
			if output.Name == "main-logs" {
				continue
			}
			outputs = append(outputs, &pb.Artifact{
				Name:          output.Name,
				ContainerPath: output.Path,
				S3Path:        fmt.Sprintf("minio://%s/%s", output.S3.Bucket, output.S3.Key),
				Type:          pb.ArtifactType_OUTPUT,
			})
		}
	}
	step.NodeId = nodeStatus.TemplateName
	step.Name = nodeName
	step.Phase = pb.Step_Phase(pb.Step_Phase_value[string(nodeStatus.Phase)])
	step.StartTime = nodeStatus.StartedAt.Unix()
	step.EndTime = nodeStatus.FinishedAt.Unix()
	step.Outputs = outputs
	step.Inputs = inputs
	step.Params = params
	return step
}
