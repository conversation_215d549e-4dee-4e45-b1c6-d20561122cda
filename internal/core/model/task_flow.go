package model

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	"github.com/google/uuid"
	"github.com/spf13/cast"
	apiv1 "k8s.io/api/core/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/utils/pointer"
	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	"transwarp.io/aip/llmops-common/pkg/serving"
	"transwarp.io/applied-ai/aiot/vision-std/stdmetric"
	k8s_util "transwarp.io/mlops/mlops-std/k8s"
	"transwarp.io/mlops/mlops-std/stderr"
	"transwarp.io/mlops/mlops-std/util"
)

const (
	Platform               = "platform"
	Sophon                 = "sophon"
	MetaNodeId             = "node_id"
	MetaNodeName           = "node_name"
	MetaOwner              = "owner"
	MetaGPUUseConf         = "gpu_use_conf"
	MetaTemplateType       = "template_type"
	MetaTemplateSourceId   = "template_source_id"
	MetaTemplateSourceName = "template_source_name"
	MetaTemplateSourceInfo = "template_source_info"
	MetaTaskFlow           = "task_flow"
	// TODO 通用配置
	PipelineServiceAccount = "llmops-admin"
	// PipelineServiceAccount = "pipeline-runner"
	LabelKeyCacheEnabled = "pipelines.kubeflow.org/cache_enabled"
	EnvGatewayUrl        = "GATEWAY_URL"
	StsPvcMountPath      = "/data/nfs/"
	StsVolumeName        = "sfs-volume"
	PodInfoVolumeName    = "pod-info"
	ArtifactsVolumeName  = "artifacts"
	AnnotationsPath      = "/etc/podinfo"
	ArtifactsPath        = "/artifact"

	// 在任务 pod 中注入环境变量, 用于给任务中动态创建的 k8s 资源设置 ownerReferences 关联.
	EnvPodUID  = "POD_UID"  // pod uid
	EnvPodName = "POD_NAME" // pod 名称
)

type TaskFlow struct {
	Nodes        []*Node
	Dependencies []*pb.Dependency
	HostNetwork  bool
}

// isV2 任意 UnifyResource 不为空则执行 V2
func (taskFlow *TaskFlow) isV2() bool {
	if taskFlow != nil {
		for _, node := range taskFlow.Nodes {
			if node != nil && node.UnifyResource != nil {
				return true
			}
		}
	}
	return false
}

func (taskFlow *TaskFlow) ToWorkflow(ctx context.Context, name, proid string, annotations map[string]string) (*v1alpha1.Workflow, error) {
	if name == "" {
		name = "run-" + uuid.New().String()
	}
	if !strings.HasPrefix(name, "run-") {
		name = "run-" + name
	}

	if taskFlow.isV2() {
		return taskFlow.toWorkflowV2(ctx, name, proid, annotations)
	}

	dependencyMap := map[string][]string{}
	for _, dependency := range taskFlow.Dependencies {
		dependencyMap[dependency.NodeId] = dependency.DepNodeId
	}

	var templates []v1alpha1.Template
	var tasks []v1alpha1.DAGTask
	var dagEventParams []v1alpha1.Parameter
	for _, node := range taskFlow.Nodes {

		node.conformComponent()
		inputArtifacts := v1alpha1.Artifacts{}
		outputArtifacts := v1alpha1.Artifacts{}
		dagFromArtifacts := v1alpha1.Artifacts{}
		var parameters []v1alpha1.Parameter
		var eventParams []v1alpha1.Parameter
		for _, input := range node.Inputs {
			artifact := v1alpha1.Artifact{
				Name: input.Name,
				Path: input.Path,
			}
			if input.From == nil {
				return nil, stderr.PipelineInputFromIsNil.Error()
			}
			from := fmt.Sprintf("{{tasks.%s.outputs.artifacts.%s}}", input.From.NodeId, input.From.ArtifactName)
			dagArtifact := v1alpha1.Artifact{
				Name: input.Name,
				From: from,
			}
			inputArtifacts = append(inputArtifacts, artifact)
			dagFromArtifacts = append(dagFromArtifacts, dagArtifact)
		}
		for _, output := range node.Outputs {
			artifact := v1alpha1.Artifact{
				Name: output.Name,
				Path: output.Path,
			}
			outputArtifacts = append(outputArtifacts, artifact)
		}
		for key, _ := range node.EventParams {
			parameter := v1alpha1.Parameter{
				Name: key,
			}
			parameterFrom := v1alpha1.Parameter{
				Name:  key,
				Value: v1alpha1.AnyStringPtr(fmt.Sprintf("{{inputs.parameters.%s}}", key)),
			}
			parameters = append(parameters, parameter)
			eventParams = append(eventParams, parameterFrom)
			dagEventParams = append(dagEventParams, parameter)
		}
		for key, value := range node.Params {
			pValue := value
			parameter := v1alpha1.Parameter{
				Name:  key,
				Value: v1alpha1.AnyStringPtr(pValue),
			}

			parameters = append(parameters, parameter)
		}
		task := v1alpha1.DAGTask{
			Name:         node.Id,
			Template:     node.Id,
			Dependencies: dependencyMap[node.Id],
			Arguments: v1alpha1.Arguments{
				Artifacts:  dagFromArtifacts,
				Parameters: eventParams,
			},
		}
		mainContainer, err := node.Container.ToK8s(ctx)
		if err != nil {
			return nil, err
		}
		// 指定 GPU
		if node.Container.Resources != nil && len(node.Container.Resources.GpuCards) > 0 {
			gpuAnno := serving.ResourceGPUCardToUseHami
			if node.Container.Resources.GpuType == serving.GPUTypeAscend {
				gpuAnno = fmt.Sprintf(serving.ResourceGPUCardToUseHamiAscend, node.Container.Resources.AscendConfig.NpuName)
			}
			annotations[gpuAnno] = strings.Join(node.Container.Resources.GpuCards, ",")
		}

		mainContainer.VolumeMounts = append(mainContainer.VolumeMounts, apiv1.VolumeMount{
			Name:      PodInfoVolumeName,
			MountPath: AnnotationsPath,
		})
		mainContainer.Env = append(mainContainer.Env,
			apiv1.EnvVar{
				Name:  EnvGatewayUrl,
				Value: fmt.Sprintf("http://autocv-gateway-service.%s.svc:80", k8s_util.CurrentNamespaceInCluster()),
			}, apiv1.EnvVar{
				Name:      EnvPodUID,
				ValueFrom: &apiv1.EnvVarSource{FieldRef: &apiv1.ObjectFieldSelector{FieldPath: "metadata.uid"}},
			}, apiv1.EnvVar{
				Name:      EnvPodName,
				ValueFrom: &apiv1.EnvVarSource{FieldRef: &apiv1.ObjectFieldSelector{FieldPath: "metadata.name"}},
			})
		nodeLables := map[string]string{}
		if node.AdvancedConfig != nil {
			nodeLables[LabelKeyCacheEnabled] = strconv.FormatBool(node.AdvancedConfig.EnableCached)
		}
		for k, v := range node.Labels {
			nodeLables[k] = v
		}
		nodeLables, _, err = stdmetric.WithMonitorKeys(nodeLables, nil, stdmetric.ResourceTypeTask, node.Id, name, proid)
		if err != nil {
			return nil, err
		}
		nodeAnnotations := map[string]string{
			MetaNodeId:             node.Id,
			MetaNodeName:           node.Name,
			MetaOwner:              util.GetUsername(ctx),
			MetaTemplateType:       node.TemplateSource.TemplateType.String(),
			MetaTemplateSourceId:   node.TemplateSource.SourceId,
			MetaTemplateSourceName: node.TemplateSource.SourceName,
			MetaTemplateSourceInfo: node.TemplateSource.SourceInfo,
			MetaGPUUseConf:         util.ToJson(node.Container.Resources),
		}
		for k, v := range node.Annotations {
			nodeAnnotations[k] = v
		}
		for k, v := range annotations {
			nodeAnnotations[k] = v
		}

		volumes, err := node.VolumeCfgs.ToK8s()
		if err != nil {
			return nil, err
		}
		volumes = append(volumes, apiv1.Volume{
			Name: PodInfoVolumeName,
			VolumeSource: apiv1.VolumeSource{
				DownwardAPI: &apiv1.DownwardAPIVolumeSource{
					Items: []apiv1.DownwardAPIVolumeFile{
						{
							Mode: pointer.Int32Ptr(420),
							FieldRef: &apiv1.ObjectFieldSelector{
								APIVersion: "v1",
								FieldPath:  "metadata.annotations",
							},
							Path: "annotations",
						},
					},
				},
			},
		})
		// K8s API 模式的 executor
		mainContainer.VolumeMounts = append(mainContainer.VolumeMounts, apiv1.VolumeMount{
			Name:      ArtifactsVolumeName,
			MountPath: ArtifactsPath,
		})
		volumes = append(volumes, apiv1.Volume{
			Name: ArtifactsVolumeName,
			VolumeSource: apiv1.VolumeSource{
				EmptyDir: &apiv1.EmptyDirVolumeSource{},
			},
		})

		selector := node.NodeSelector
		if selector == nil {
			selector = make(map[string]string)
		}
		label, err := serving.LabelSelectorToStr(ctx)
		if err == nil && len(label) != 0 {
			selector[label] = "true"
		}

		template := v1alpha1.Template{
			Name: node.Id,
			Inputs: v1alpha1.Inputs{
				Artifacts:  inputArtifacts,
				Parameters: parameters,
			},
			Outputs: v1alpha1.Outputs{
				Artifacts: outputArtifacts,
			},
			Container: mainContainer,
			Metadata: v1alpha1.Metadata{
				Labels:      nodeLables,
				Annotations: nodeAnnotations,
			},
			NodeSelector: selector,
			Volumes:      volumes,
		}
		if node.AdvancedConfig != nil {
			template.ServiceAccountName = node.AdvancedConfig.ServiceAccountName
		}
		templates = append(templates, template)
		tasks = append(tasks, task)
	}
	dag := v1alpha1.Template{
		Name: name,
		DAG: &v1alpha1.DAGTemplate{
			Tasks: tasks,
		},
		Inputs: v1alpha1.Inputs{
			Parameters: dagEventParams,
		},
	}
	templates = append(templates, dag)
	workflow := &v1alpha1.Workflow{
		TypeMeta: v1.TypeMeta{
			Kind:       "Workflow",
			APIVersion: "argoproj.io/v1alpha1",
		},
		ObjectMeta: v1.ObjectMeta{
			GenerateName: name,
			Annotations: map[string]string{
				Platform:     Sophon,
				MetaTaskFlow: util.ToJson(taskFlow),
			},
		},
		Spec: v1alpha1.WorkflowSpec{
			Entrypoint:         name,
			Templates:          templates,
			ServiceAccountName: PipelineServiceAccount,
			HostNetwork:        &taskFlow.HostNetwork,
		},
	}
	for k, v := range annotations {
		workflow.Annotations[k] = v
	}

	return workflow, nil
}

func (taskFlow *TaskFlow) FromWorkflow(workflow *v1alpha1.Workflow) (*TaskFlow, error) {
	isSophon := workflow.Annotations[Platform] == Sophon
	if isSophon {
		util.FromJson(workflow.Annotations[MetaTaskFlow], taskFlow)
		return taskFlow, nil
	}

	taskFlow.HostNetwork = *workflow.Spec.HostNetwork
	taskFlow.Dependencies = []*pb.Dependency{}
	taskFlow.Nodes = []*Node{}

	var dag *v1alpha1.DAGTemplate
	artifactRelation := map[string]map[string]string{}
	for _, template := range workflow.Spec.Templates {
		if template.DAG != nil {
			dag = template.DAG
		}
	}
	for _, task := range dag.Tasks {
		taskFlow.Dependencies = append(taskFlow.Dependencies, &pb.Dependency{
			NodeId:    task.Name,
			DepNodeId: task.Dependencies,
		})
		for _, artifact := range task.Arguments.Artifacts {
			artifactRelation[task.Name][artifact.Name] = artifact.From
		}
	}

	for _, template := range workflow.Spec.Templates {
		if template.DAG != nil {
			continue
		}
		var inputs []*pb.ArtifactConfig
		var outputs []*pb.ArtifactConfig

		params := map[string]string{}
		for _, artifact := range template.Inputs.Artifacts {
			var nodeId string
			var artifactName string

			ss := strings.Split(artifactRelation[template.Name][artifact.Name], ".")
			nodeId = ss[1]
			artifactName = ss[4][:len(ss[4])-2]
			from := &pb.From{
				NodeId:       nodeId,
				ArtifactName: artifactName,
			}
			input := &pb.ArtifactConfig{
				Name: artifact.Name,
				Path: artifact.Path,
				From: from,
			}
			inputs = append(inputs, input)
		}
		for _, artifact := range template.Outputs.Artifacts {
			output := &pb.ArtifactConfig{
				Name: artifact.Name,
				Path: artifact.Path,
			}
			outputs = append(outputs, output)
		}

		for _, parameter := range template.Inputs.Parameters {
			if isSophon {
				params[parameter.Name] = string(*parameter.Value)
			}
		}
		env := map[string]string{}

		for _, e := range template.Container.Env {
			env[e.Name] = e.Value
		}
		node := &Node{
			Id: template.Metadata.Annotations[MetaNodeId],
			TemplateSource: &pb.TemplateSource{
				TemplateType: pb.TaskSourceType(pb.TaskSourceType_value[template.Metadata.Annotations[MetaTemplateType]]),
				SourceId:     template.Metadata.Annotations[MetaTemplateSourceId],
				SourceName:   template.Metadata.Annotations[MetaTemplateSourceName],
				SourceInfo:   template.Metadata.Annotations[MetaTemplateSourceInfo],
			},
			Inputs:     inputs,
			Outputs:    outputs,
			Params:     params,
			Name:       template.Metadata.Annotations[MetaNodeName],
			Container:  (&Container{}).FromK8s(template.Container, template.Metadata.Annotations[MetaGPUUseConf]),
			VolumeCfgs: &VolumeCfgs{},
		}
		node.VolumeCfgs.FromK8s(template.Volumes)
		taskFlow.Nodes = append(taskFlow.Nodes, node)
	}
	return taskFlow, nil
}

func (taskFlow *TaskFlow) toWorkflowV2(ctx context.Context, name, proid string, annotations map[string]string) (*v1alpha1.Workflow, error) {
	depsMap := map[string][]string{} // node -> deps
	for _, dep := range taskFlow.Dependencies {
		depsMap[dep.GetNodeId()] = dep.GetDepNodeId()
	}

	// 提前生成多实例依赖关系
	depsRevsMap := map[string][]string{} // dep -> nodes, 多实例时获取依赖关系
	for _, dep := range taskFlow.Dependencies {
		for _, d := range dep.GetDepNodeId() {
			depsRevsMap[d] = append(depsRevsMap[d], dep.GetNodeId())
		}
	}
	for _, node := range taskFlow.Nodes {
		if node.UnifyResource.GetDistributed().GetEnabled() && node.UnifyResource.GetDistributed().GetNumWorkers() > 1 {
			for _, nid := range depsRevsMap[node.Id] {
				for i := int32(0); i < node.UnifyResource.GetDistributed().GetNumWorkers(); i++ {
					depsMap[nid] = append(depsMap[nid], fmt.Sprintf("%s-%d", node.Id, i))
				}
			}
		}
	}

	templates := make([]v1alpha1.Template, 0, len(taskFlow.Nodes)+1)
	tasks := make([]v1alpha1.DAGTask, 0, len(taskFlow.Nodes))

	for _, node := range taskFlow.Nodes {
		node.conformComponent()
		inputs := v1alpha1.Artifacts{}
		outputs := v1alpha1.Artifacts{}
		dagFroms := v1alpha1.Artifacts{}
		var parameters []v1alpha1.Parameter
		for _, input := range node.Inputs {
			if input.GetFrom() == nil {
				return nil, stderr.PipelineInputFromIsNil.Error()
			}
			inputs = append(inputs, v1alpha1.Artifact{
				Name: input.GetName(),
				Path: input.GetPath(),
			})
			dagFroms = append(dagFroms, v1alpha1.Artifact{
				Name: input.GetName(),
				From: fmt.Sprintf("{{tasks.%s.outputs.artifacts.%s}}", input.GetFrom().GetNodeId(), input.GetFrom().GetArtifactName()),
			})
		}
		for _, output := range node.Outputs {
			outputs = append(outputs, v1alpha1.Artifact{
				Name: output.GetName(),
				Path: output.GetPath(),
			})
		}
		for key, _ := range node.EventParams {
			parameter := v1alpha1.Parameter{
				Name: key,
			}
			parameters = append(parameters, parameter)
		}
		for key, value := range node.Params {
			pValue := value
			parameters = append(parameters, v1alpha1.Parameter{
				Name:  key,
				Value: v1alpha1.AnyStringPtr(pValue),
			})
		}
		task := v1alpha1.DAGTask{
			Name:         node.Id,
			Template:     node.Id,
			Dependencies: depsMap[node.Id],
			Arguments: v1alpha1.Arguments{
				Artifacts: dagFroms,
			},
		}
		mainContainer, err := node.Container.ToK8s(ctx)
		if err != nil {
			return nil, err
		}
		mainContainer.VolumeMounts = append(mainContainer.VolumeMounts, apiv1.VolumeMount{
			Name:      PodInfoVolumeName,
			MountPath: AnnotationsPath,
		})
		mainContainer.Env = append(mainContainer.Env,
			apiv1.EnvVar{
				Name:  EnvGatewayUrl,
				Value: fmt.Sprintf("http://autocv-gateway-service.%s.svc:80", k8s_util.CurrentNamespaceInCluster()),
			}, apiv1.EnvVar{
				Name:      EnvPodUID,
				ValueFrom: &apiv1.EnvVarSource{FieldRef: &apiv1.ObjectFieldSelector{FieldPath: "metadata.uid"}},
			}, apiv1.EnvVar{
				Name:      EnvPodName,
				ValueFrom: &apiv1.EnvVarSource{FieldRef: &apiv1.ObjectFieldSelector{FieldPath: "metadata.name"}},
			},
		)
		arch := node.UnifyResource.GetArch()
		servingRes := &serving.PodResourceDTO{
			NodeChooseCfg:  node.UnifyResource.GetNodeChooseCfg(),
			ResourceRuleId: cast.ToInt32(node.UnifyResource.GetResourceRuleId()),
			ResourceRule:   node.UnifyResource.GetResourceRule(),
			Resource:       node.UnifyResource.GetResource(),
			Arch:           arch,
			Distributed:    node.UnifyResource.GetDistributed(),
			IsAdvancedMode: node.UnifyResource.GetIsAdvancedMode(),
		}
		spec, err := servingRes.ToPodResourceDO(ctx)
		if err != nil {
			return nil, fmt.Errorf("ToPodResourceDO: %w", err)
		}
		mainContainer.Resources = *spec.Container.Resources

		nodeLables := make(map[string]string, 10)
		if node.AdvancedConfig != nil {
			nodeLables[LabelKeyCacheEnabled] = strconv.FormatBool(node.AdvancedConfig.EnableCached)
		}
		nodeLables, _, err = stdmetric.WithMonitorKeys(nodeLables, nil, stdmetric.ResourceTypeTask, node.Id, name, proid)
		if err != nil {
			return nil, err
		}
		for k, v := range spec.Labels {
			nodeLables[k] = v
		}
		nodeAnnotations := map[string]string{
			MetaNodeId:             node.Id,
			MetaNodeName:           node.Name,
			MetaOwner:              util.GetUsername(ctx),
			MetaTemplateType:       node.TemplateSource.GetTemplateType().String(),
			MetaTemplateSourceId:   node.TemplateSource.GetSourceId(),
			MetaTemplateSourceName: node.TemplateSource.GetSourceName(),
			MetaTemplateSourceInfo: node.TemplateSource.GetSourceInfo(),
			MetaGPUUseConf:         util.ToJson(node.Container.Resources),
		}
		for k, v := range annotations {
			nodeAnnotations[k] = v
		}
		for k, v := range spec.Annotations {
			nodeAnnotations[k] = v
		}

		volumes, err := node.VolumeCfgs.ToK8s()
		if err != nil {
			return nil, err
		}
		volumes = append(volumes, apiv1.Volume{
			Name: PodInfoVolumeName,
			VolumeSource: apiv1.VolumeSource{
				DownwardAPI: &apiv1.DownwardAPIVolumeSource{
					Items: []apiv1.DownwardAPIVolumeFile{
						{
							Mode: pointer.Int32Ptr(420),
							FieldRef: &apiv1.ObjectFieldSelector{
								APIVersion: "v1",
								FieldPath:  "metadata.annotations",
							},
							Path: "annotations",
						},
					},
				},
			},
		})
		selector := make(map[string]string)
		label, err := serving.LabelSelectorToStr(ctx)
		if err == nil && len(label) != 0 {
			selector[label] = "true"
		}
		template := v1alpha1.Template{
			Name: node.Id,
			Inputs: v1alpha1.Inputs{
				Artifacts:  inputs,
				Parameters: parameters,
			},
			Outputs: v1alpha1.Outputs{
				Artifacts: outputs,
			},
			Container: mainContainer,
			Metadata: v1alpha1.Metadata{
				Labels:      nodeLables,
				Annotations: nodeAnnotations,
			},
			NodeSelector: selector,
			Affinity:     spec.Affinity,
			Volumes:      volumes,
		}
		if node.AdvancedConfig != nil {
			template.ServiceAccountName = node.AdvancedConfig.ServiceAccountName
		}
		templates = append(templates, template)
		tasks = append(tasks, task)
		if node.UnifyResource.GetDistributed().GetEnabled() && node.UnifyResource.GetDistributed().GetNumWorkers() > 1 {
			for i := int32(0); i < node.UnifyResource.GetDistributed().GetNumWorkers(); i++ {
				t := task.DeepCopy()
				t.Name = fmt.Sprintf("%s-%d", t.Name, i)
				tasks = append(tasks, *t)
			}
		}
	}
	templates = append(templates, v1alpha1.Template{
		Name: name,
		DAG: &v1alpha1.DAGTemplate{
			Tasks: tasks,
		},
	})
	workflow := &v1alpha1.Workflow{
		TypeMeta: v1.TypeMeta{
			Kind:       "Workflow",
			APIVersion: "argoproj.io/v1alpha1",
		},
		ObjectMeta: v1.ObjectMeta{
			GenerateName: name,
			Annotations: map[string]string{
				Platform:     Sophon,
				MetaTaskFlow: util.ToJson(taskFlow),
			},
		},
		Spec: v1alpha1.WorkflowSpec{
			Entrypoint:         name,
			Templates:          templates,
			ServiceAccountName: PipelineServiceAccount,
			HostNetwork:        &taskFlow.HostNetwork,
		},
	}
	for k, v := range annotations {
		workflow.Annotations[k] = v
	}

	return workflow, nil
}

func (taskFlow *TaskFlow) fromWorkflowV2(workflow *v1alpha1.Workflow) (*TaskFlow, error) {
	isSophon := workflow.Annotations[Platform] == Sophon
	if isSophon {
		util.FromJson(workflow.Annotations[MetaTaskFlow], taskFlow)
		return taskFlow, nil
	}

	taskFlow.HostNetwork = *workflow.Spec.HostNetwork
	taskFlow.Dependencies = []*pb.Dependency{}
	taskFlow.Nodes = []*Node{}

	var dag *v1alpha1.DAGTemplate
	artifactRelation := map[string]map[string]string{}
	for _, template := range workflow.Spec.Templates {
		if template.DAG != nil {
			dag = template.DAG
		}
	}
	for _, task := range dag.Tasks {
		taskFlow.Dependencies = append(taskFlow.Dependencies, &pb.Dependency{
			NodeId:    task.Name,
			DepNodeId: task.Dependencies,
		})
		for _, artifact := range task.Arguments.Artifacts {
			artifactRelation[task.Name][artifact.Name] = artifact.From
		}
	}

	for _, template := range workflow.Spec.Templates {
		if template.DAG != nil {
			continue
		}
		var inputs []*pb.ArtifactConfig
		var outputs []*pb.ArtifactConfig

		params := map[string]string{}
		for _, artifact := range template.Inputs.Artifacts {
			var nodeId string
			var artifactName string

			ss := strings.Split(artifactRelation[template.Name][artifact.Name], ".")
			nodeId = ss[1]
			artifactName = ss[4][:len(ss[4])-2]
			from := &pb.From{
				NodeId:       nodeId,
				ArtifactName: artifactName,
			}
			input := &pb.ArtifactConfig{
				Name: artifact.Name,
				Path: artifact.Path,
				From: from,
			}
			inputs = append(inputs, input)
		}
		for _, artifact := range template.Outputs.Artifacts {
			output := &pb.ArtifactConfig{
				Name: artifact.Name,
				Path: artifact.Path,
			}
			outputs = append(outputs, output)
		}

		for _, parameter := range template.Inputs.Parameters {
			if isSophon {
				params[parameter.Name] = string(*parameter.Value)
			}
		}
		env := map[string]string{}

		for _, e := range template.Container.Env {
			env[e.Name] = e.Value
		}
		node := &Node{
			Id: template.Metadata.Annotations[MetaNodeId],
			TemplateSource: &pb.TemplateSource{
				TemplateType: pb.TaskSourceType(pb.TaskSourceType_value[template.Metadata.Annotations[MetaTemplateType]]),
				SourceId:     template.Metadata.Annotations[MetaTemplateSourceId],
				SourceName:   template.Metadata.Annotations[MetaTemplateSourceName],
				SourceInfo:   template.Metadata.Annotations[MetaTemplateSourceInfo],
			},
			Inputs:     inputs,
			Outputs:    outputs,
			Params:     params,
			Name:       template.Metadata.Annotations[MetaNodeName],
			Container:  (&Container{}).FromK8s(template.Container, template.Metadata.Annotations[MetaGPUUseConf]),
			VolumeCfgs: &VolumeCfgs{},
		}
		node.VolumeCfgs.FromK8s(template.Volumes)
		taskFlow.Nodes = append(taskFlow.Nodes, node)
	}
	return taskFlow, nil
}

func (taskFlow *TaskFlow) FromPb(pb *pb.TaskFlow) *TaskFlow {
	taskFlow.HostNetwork = pb.HostNetwork
	taskFlow.Dependencies = pb.Dependencies
	taskFlow.Nodes = []*Node{}
	for _, node := range pb.Nodes {
		taskFlow.Nodes = append(taskFlow.Nodes, (&Node{}).FromPb(node))
	}
	return taskFlow
}

func (taskFlow *TaskFlow) ToPb() *pb.TaskFlow {
	nodes := make([]*pb.Node, 0)
	for _, node := range taskFlow.Nodes {
		nodes = append(nodes, node.ToPb())
	}

	return &pb.TaskFlow{
		HostNetwork:  taskFlow.HostNetwork,
		Dependencies: taskFlow.Dependencies,
		Nodes:        nodes,
	}
}

func (taskFlow *TaskFlow) FromPbV2(pb *pb.TaskFlowV2) *TaskFlow {
	taskFlow.HostNetwork = pb.GetHostNetwork()
	taskFlow.Dependencies = pb.GetDependencies()
	taskFlow.Nodes = make([]*Node, 0, len(pb.GetNodes()))
	for _, node := range pb.GetNodes() {
		taskFlow.Nodes = append(taskFlow.Nodes, (&Node{}).FromPbV2(node))
	}
	return taskFlow
}

func (taskFlow *TaskFlow) ToPbV2() *pb.TaskFlowV2 {
	nodes := make([]*pb.NodeV2, 0, len(taskFlow.Nodes))
	for _, node := range taskFlow.Nodes {
		nodes = append(nodes, node.ToPbV2())
	}
	return &pb.TaskFlowV2{
		HostNetwork:  taskFlow.HostNetwork,
		Dependencies: taskFlow.Dependencies,
		Nodes:        nodes,
	}
}
