package model

import (
	"context"
	"strings"

	"github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	k8s_util "transwarp.io/mlops/mlops-std/k8s"
	"transwarp.io/mlops/mlops-std/util"
	"transwarp.io/mlops/pipeline/conf"
	"transwarp.io/mlops/pipeline/internal/dao/model"
)

const (
	META_PIPELINE_ID         = "pipeline_id"
	META_PIPELINE_VERSION_ID = "pipeline_version_id"
	META_PROJECT_ID          = "project_id"
	META_TENANT_ID           = "tenant_id"
	META_TASK_TYPE           = "task_type"
	META_TASK_SOURCE_ID      = "task_source_id"
	META_TASK_SOURCE_NAME    = "task_source_name"
	META_TASK_SOURCE_INFO    = "task_source_info"
	META_MLOPS_NAMESPACE     = "mlops_namespace"
	META_SOPHON_Id           = "sophon_id"
)

type Run struct {
	Id            string
	ProjectId     string
	TaskSource    *pb.TaskSource
	TaskFlow      *TaskFlow
	StartTime     int64
	EndTime       int64
	Status        pb.Run_Status
	ScheduledTime int64
	Steps         []Step
	CreateUser    string
}

func (run *Run) ToWorkflow(ctx context.Context, tenantUid string) (*v1alpha1.Workflow, error) {
	annotations := map[string]string{
		META_SOPHON_Id:        conf.SophonServiceId,
		META_PROJECT_ID:       run.ProjectId,
		META_TENANT_ID:        tenantUid,
		META_TASK_TYPE:        run.TaskSource.SourceType.String(),
		META_TASK_SOURCE_ID:   run.TaskSource.SourceId,
		META_TASK_SOURCE_NAME: run.TaskSource.SourceName,
		META_TASK_SOURCE_INFO: run.TaskSource.SourceInfo,
		MetaOwner:             run.CreateUser,
		META_MLOPS_NAMESPACE:  k8s_util.CurrentNamespaceInCluster(),
	}
	return run.TaskFlow.ToWorkflow(ctx, strings.ReplaceAll(run.TaskSource.GetSourceId(), "/", ""), run.ProjectId, annotations)
}

func (run *Run) ToDesc() string {
	return util.ToJson(RunDesc{
		SophonId:   conf.SophonServiceId,
		ProjectId:  run.ProjectId,
		SourceId:   run.TaskSource.SourceId,
		SourceName: run.TaskSource.SourceName,
		SourceType: run.TaskSource.SourceType.String(),
		Owner:      run.CreateUser,
	})
}

func (run *Run) ToPb() *pb.Run {
	steps := make([]*pb.Step, 0)
	for _, s := range run.Steps {
		steps = append(steps, s.ToPb())
	}
	return &pb.Run{
		Id:            run.Id,
		ProjectId:     run.ProjectId,
		TaskSource:    run.TaskSource,
		TaskFlow:      run.TaskFlow.ToPb(),
		StartTime:     run.StartTime,
		EndTime:       run.EndTime,
		Status:        run.Status,
		ScheduledTime: run.ScheduledTime,
		Steps:         steps,
		CreateUser:    run.CreateUser,
	}
}

func (run *Run) ToPbV2() *pb.RunV2 {
	steps := make([]*pb.Step, 0, len(run.Steps))
	for _, s := range run.Steps {
		steps = append(steps, s.ToPb())
	}
	return &pb.RunV2{
		Id:            run.Id,
		ProjectId:     run.ProjectId,
		TaskSource:    run.TaskSource,
		TaskFlow:      run.TaskFlow.ToPbV2(),
		StartTime:     run.StartTime,
		EndTime:       run.EndTime,
		Status:        pb.RunV2_Status(run.Status),
		ScheduledTime: run.ScheduledTime,
		Steps:         steps,
		CreateUser:    run.CreateUser,
	}
}

func (run *Run) FromEntry(entry model.Run) *Run {
	var workflow v1alpha1.Workflow
	util.FromJson(entry.WorkflowRuntimeManifest, &workflow)
	var steps []Step
	var taskFlow TaskFlow
	util.FromJson(workflow.Annotations[MetaTaskFlow], &taskFlow)
	nodeMap := nodes2Map(taskFlow.Nodes)

	for _, node := range workflow.Status.Nodes {
		if node.Type == v1alpha1.NodeTypePod {
			nodeName := node.TemplateName
			runNode := nodeMap[node.TemplateName]
			if runNode != nil {
				nodeName = runNode.Name
			}
			step := &Step{}
			step.FromWorkflow(&node, nodeName)
			steps = append(steps, *step)
		}
	}

	run.Id = entry.UUID
	run.CreateUser = entry.Owner
	run.ProjectId = entry.ProjectID
	run.TaskSource = &pb.TaskSource{
		SourceType: pb.TaskSourceType(pb.TaskSourceType_value[entry.SourceType]),
		SourceId:   entry.SourceID,
		SourceName: entry.SourceName,
		SourceInfo: workflow.Annotations[META_TASK_SOURCE_INFO],
	}

	run.TaskFlow = &taskFlow
	condition, ok := pb.Run_Status_value[entry.Conditions]
	if ok {
		run.Status = pb.Run_Status(condition)
	} else {
		run.Status = pb.Run_Running
	}
	run.StartTime = entry.CreatedAtInSec
	run.EndTime = entry.FinishedAtInSec
	run.Steps = steps
	run.ScheduledTime = 0

	if run.EndTime != 0 && run.StartTime != 0 {
		run.ScheduledTime = run.EndTime - run.StartTime
	}
	return run
}

func nodes2Map(nodes []*Node) map[string]*Node {
	nodeMap := map[string]*Node{}
	for _, node := range nodes {
		nodeMap[node.Id] = node
	}
	return nodeMap
}

type RunDesc struct {
	SophonId   string
	ProjectId  string
	Owner      string
	SourceId   string
	SourceType string
	SourceName string
}
