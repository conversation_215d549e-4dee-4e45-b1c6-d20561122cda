package model

import (
	"github.com/argoproj/argo/pkg/apis/workflow/v1alpha1"
	pb "transwarp.io/aip/llmops-common/pb/pipeline"
)

type ComponentAdvancedConfig struct {
	EnableCached       bool
	Parallelism        int
	ServiceAccountName string
	RetryStrategy      *v1alpha1.RetryStrategy
}

func (c *ComponentAdvancedConfig) FromPb(advancedCfg *pb.ComponentAdvancedConfig) *ComponentAdvancedConfig {
	if advancedCfg == nil {
		c = nil
		return nil
	}
	c.EnableCached = advancedCfg.EnableCached
	c.Parallelism = int(advancedCfg.Parallelism)
	c.ServiceAccountName = advancedCfg.ServiceAccountName
	if advancedCfg.RetryStrategy != nil {
		retryPolicy := v1alpha1.RetryPolicyOnFailure
		switch advancedCfg.RetryStrategy.RetryPolicy {
		case pb.RetryStrategy_RetryPolicyAlways:
			retryPolicy = v1alpha1.RetryPolicyAlways
		case pb.RetryStrategy_RetryPolicyOnError:
			retryPolicy = v1alpha1.RetryPolicyOnError
		case pb.RetryStrategy_RetryPolicyOnFailure:
			retryPolicy = v1alpha1.RetryPolicyOnFailure
		}

		c.RetryStrategy = &v1alpha1.RetryStrategy{
			Limit:       &advancedCfg.RetryStrategy.Limit,
			RetryPolicy: retryPolicy,
		}
	}

	return c
}
func (c *ComponentAdvancedConfig) ToPb() *pb.ComponentAdvancedConfig {
	if c == nil {
		return nil
	}
	var retryStrategy *pb.RetryStrategy
	if c.RetryStrategy != nil {
		retryPolicy := pb.RetryStrategy_RetryPolicyOnFailure
		switch c.RetryStrategy.RetryPolicy {
		case v1alpha1.RetryPolicyAlways:
			retryPolicy = pb.RetryStrategy_RetryPolicyAlways
		case v1alpha1.RetryPolicyOnError:
			retryPolicy = pb.RetryStrategy_RetryPolicyOnError
		case v1alpha1.RetryPolicyOnFailure:
			retryPolicy = pb.RetryStrategy_RetryPolicyOnFailure
		}
		retryStrategy = &pb.RetryStrategy{
			RetryPolicy: retryPolicy,
			Limit:       *c.RetryStrategy.Limit,
		}
	}

	return &pb.ComponentAdvancedConfig{
		EnableCached:       c.EnableCached,
		Parallelism:        int32(c.Parallelism),
		ServiceAccountName: c.ServiceAccountName,
		RetryStrategy:      retryStrategy,
	}
}
