apiVersion: kueue.x-k8s.io/v1beta1
kind: Workload
metadata:
  creationTimestamp: "2025-07-07T00:56:25Z"
  generation: 1
  labels:
    kueue.x-k8s.io/job-uid: 0d6b6c82-1662-433b-bc71-9ea701a75e50
  name: job-sample-job-e27d3
  namespace: test
  ownerReferences:
  - apiVersion: batch/v1
    blockOwnerDeletion: true
    controller: true
    kind: Job
    name: sample-job
    uid: 0d6b6c82-1662-433b-bc71-9ea701a75e50
  resourceVersion: "29720196"
  uid: 7bf64bcf-64be-4856-b350-3aafb799db17
spec:
  active: true
  podSets:
  - count: 2
    name: main
    template:
      metadata: {}
      spec:
        containers:
        - args:
          - -c
          - sleep 60
          command:
          - /bin/sh
          image: *************:5000/aip/deps/nginx:llm-1.4.3
          imagePullPolicy: IfNotPresent
          name: dummy-job
          resources:
            requests:
              cpu: "1"
              memory: 200Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
        dnsPolicy: ClusterFirst
        restartPolicy: Never
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
  priority: 0
  priorityClassSource: ""
  queueName: task
status:
  admission:
    clusterQueue: test-task-cq
    podSetAssignments:
    - count: 2
      flavors:
        cpu: llmops-default-flavor
        memory: llmops-default-flavor
      name: main
      resourceUsage:
        cpu: "2"
        memory: 400Mi
  conditions:
  - lastTransitionTime: "2025-07-07T00:56:25Z"
    message: Quota reserved in ClusterQueue test-task-cq
    observedGeneration: 1
    reason: QuotaReserved
    status: "True"
    type: QuotaReserved
  - lastTransitionTime: "2025-07-07T00:56:25Z"
    message: The workload is admitted
    observedGeneration: 1
    reason: Admitted
    status: "True"
    type: Admitted
  - lastTransitionTime: "2025-07-07T00:57:28Z"
    message: ""
    observedGeneration: 1
    reason: Succeeded
    status: "True"
    type: Finished