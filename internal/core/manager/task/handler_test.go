package task

// import (
// 	"context"
// 	"fmt"
// 	"testing"
// 	"time"
//
// 	handler "transwarp.io/mlops/pipeline/internal/core/manager/task/handlerframework"
// 	_ "transwarp.io/mlops/pipeline/internal/core/manager/task/handlers"
// )
//
// func TestHandlers(t *testing.T) {
// 	if testing.Short() {
// 		t.Skip("Skipping long-running integration test in short mode")
// 	}
//
// 	err := handler.StartHandlerManagerLeaderElection(context.Background())
// 	if err != nil {
// 		fmt.Println(err.Error())
// 	}
// 	time.Sleep(10 * time.Minute)
// }
