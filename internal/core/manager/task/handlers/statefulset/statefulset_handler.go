package statefulset

import (
	"fmt"

	appsv1 "k8s.io/api/apps/v1"
	"k8s.io/apimachinery/pkg/runtime"
	framework "transwarp.io/mlops/pipeline/internal/core/manager/task/handlerframework"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
)

var (
	gvk = appsv1.SchemeGroupVersion.WithKind("StatefulSet")
)

const (
	FrameworkName = "statefulset"
)

type StatefulSetHandler struct {
	*framework.BaseHandler
	generator framework.TaskGenerator
}

func init() {
	handler := NewStatefulsetHandler(
		framework.NewBaseHandler(
			client.MustGetK8sClient().StatefulSetInformer,
			framework.HandleResourceCreate,
			framework.HandleResourceUpdate,
			framework.HandleResourceDelete,
		),
		framework.NewDefaultTaskGenerator(),
	)

	framework.RegisterHandler(FrameworkName, handler)
}

func NewStatefulsetHandler(baseHandler *framework.BaseHandler, generator framework.TaskGenerator) *StatefulSetHandler {
	return &StatefulSetHandler{
		BaseHandler: baseHandler,
		generator:   generator,
	}
}

func (h *StatefulSetHandler) Name() string {
	return FrameworkName
}

func (h *StatefulSetHandler) Handle(obj runtime.Object) (*model.Task, error) {
	sts, ok := obj.(*appsv1.StatefulSet)
	if !ok {
		return nil, fmt.Errorf("object is not a Statefulset")
	}

	task, err := h.generator.GenerateTask(sts)
	if err != nil {
		return nil, fmt.Errorf("failed to extract statefulset task info: %w", err)
	}

	return task, nil
}
