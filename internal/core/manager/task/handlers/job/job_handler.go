package job

import (
	"fmt"

	batchv1 "k8s.io/api/batch/v1"
	"k8s.io/apimachinery/pkg/runtime"
	framework "transwarp.io/mlops/pipeline/internal/core/manager/task/handlerframework"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
)

var (
	JobHandlerName = "kubernetes-job"
)

type JobHandler struct {
	*framework.BaseHandler
	generator framework.TaskGenerator
}

func init() {
	handler := NewJobHandler(
		framework.NewBaseHandler(
			client.MustGetK8sClient().JobInformer,
			framework.HandleResourceCreate,
			framework.HandleResourceUpdate,
			framework.HandleResourceDelete,
		),
		framework.NewDefaultTaskGenerator(),
	)

	framework.RegisterHandler(JobHandlerName, handler)
}

func NewJobHandler(baseHandler *framework.BaseHandler, generator framework.TaskGenerator) *JobHandler {
	return &JobHandler{
		BaseHandler: baseHandler,
		generator:   generator,
	}
}

func (h *JobHandler) Name() string {
	return JobHandlerName
}

func (h *JobHandler) Handle(obj runtime.Object) (*model.Task, error) {
	job, ok := obj.(*batchv1.Job)
	if !ok {
		return nil, fmt.Errorf("object is not a Job")
	}

	task, err := h.generator.GenerateTask(job)
	if err != nil {
		return nil, fmt.Errorf("failed to extract job task info: %w", err)
	}

	return task, nil
}
