package deployment

import (
	"fmt"

	appsv1 "k8s.io/api/apps/v1"
	"k8s.io/apimachinery/pkg/runtime"
	framework "transwarp.io/mlops/pipeline/internal/core/manager/task/handlerframework"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
)

var (
	gvk = appsv1.SchemeGroupVersion.WithKind("Deployment")
)

const (
	FrameworkName = "deployment"
)

type DeploymentHandler struct {
	*framework.BaseHandler
	generator framework.TaskGenerator
}

func init() {
	handler := NewDeploymentHandler(
		framework.NewBaseHandler(
			client.MustGetK8sClient().DeploymentInformer,
			framework.HandleResourceCreate,
			framework.HandleResourceUpdate,
			framework.HandleResourceDelete,
		),
		framework.NewDefaultTaskGenerator(),
	)

	framework.RegisterHandler(FrameworkName, handler)
}

func NewDeploymentHandler(baseHandler *framework.BaseHandler, generator framework.TaskGenerator) *DeploymentHandler {
	return &DeploymentHandler{
		BaseHandler: baseHandler,
		generator:   generator,
	}
}

func (h *DeploymentHandler) Name() string {
	return FrameworkName
}

func (h *DeploymentHandler) Handle(obj runtime.Object) (*model.Task, error) {
	deployment, ok := obj.(*appsv1.Deployment)
	if !ok {
		return nil, fmt.Errorf("object is not a Deployment")
	}

	task, err := h.generator.GenerateTask(deployment)
	if err != nil {
		return nil, fmt.Errorf("failed to extract deployment task info: %w", err)
	}

	return task, nil
}
