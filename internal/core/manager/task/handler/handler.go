package handler

import (
	"context"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/tools/cache"

	"transwarp.io/mlops/pipeline/internal/core/consts"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
	"transwarp.io/mlops/pipeline/internal/util"

	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

type ResourceType struct {
	Group    string
	Version  string
	Resource string
	Kind     string
}

type Handler interface {
	ShouldProcess(obj runtime.Object) bool
	GenerateTask(obj runtime.Object) (*model.Task, error)
	Start(ctx context.Context) error
	Stop() error
}

type BaseHandler struct {
	informer cache.SharedIndexInformer
	stopCh   chan struct{}

	onCreate func(obj runtime.Object)
	onUpdate func(oldObj, newObj runtime.Object)
	onDelete func(obj runtime.Object)
}

func NewBaseHandler(informer cache.SharedIndexInformer,
	onCreate func(obj runtime.Object),
	onUpdate func(oldObj, newObj runtime.Object),
	onDelete func(obj runtime.Object),
) *BaseHandler {
	return &BaseHandler{
		informer: informer,
		onCreate: onCreate,
		onUpdate: onUpdate,
		onDelete: onDelete,
		stopCh:   make(chan struct{}),
	}
}

func (h *BaseHandler) ShouldProcess(obj runtime.Object) bool {
	if !h.hasRequiredLabels(obj) {
		return false
	}

	if !h.isLLMOpsManagedNamespace(obj) {
		return false
	}

	return true
}

func (h *BaseHandler) hasRequiredLabels(obj runtime.Object) bool {
	metaObj, ok := obj.(metav1.Object)
	if !ok {
		return false
	}

	labels := metaObj.GetLabels()
	if labels == nil {
		return false
	}

	queueName, hasQueue := labels[consts.LabelKueueQueueNameKey]
	if !hasQueue || queueName == "" {
		return false
	}

	return true
}

func (h *BaseHandler) isLLMOpsManagedNamespace(obj runtime.Object) bool {
	metaObj, ok := obj.(metav1.Object)
	if !ok {
		return false
	}

	currentNamespace := util.GetCurrentNamespace()

	namespace := metaObj.GetNamespace()
	nsObj, err := client.MustGetK8sClient().GetNamespace(context.Background(), namespace)
	if err != nil || nsObj == nil {
		return false
	}
	if ns, ok := nsObj.Labels[consts.LabelLLMOpsManagedByKey]; ok && currentNamespace == ns {
		return true
	}
	return false
}

func (h *BaseHandler) Start(ctx context.Context) error {
	h.informer.AddEventHandler(cache.FilteringResourceEventHandler{
		FilterFunc: func(obj interface{}) bool {
			return h.ShouldProcess(obj.(runtime.Object))
		},
		Handler: cache.ResourceEventHandlerFuncs{
			AddFunc: func(obj interface{}) {
				if runtimeObj, ok := obj.(runtime.Object); ok {
					if h.onCreate != nil {
						h.onCreate(runtimeObj)
					}
				}
			},
			UpdateFunc: func(oldObj, newObj interface{}) {
				if oldRuntimeObj, ok := oldObj.(runtime.Object); ok {
					if newRuntimeObj, ok := newObj.(runtime.Object); ok {
						if h.onUpdate != nil {
							h.onUpdate(oldRuntimeObj, newRuntimeObj)
						}
					}
				}
			},
			DeleteFunc: func(obj interface{}) {
				if runtimeObj, ok := obj.(runtime.Object); ok {
					if h.onDelete != nil {
						h.onDelete(runtimeObj)
					}
				}
			},
		},
	})

	go h.informer.Run(h.stopCh)

	if !cache.WaitForCacheSync(ctx.Done(), h.informer.HasSynced) {
		zlog.SugarInfoln("Cache synced failed.")
	}

	zlog.SugarInfof("Started handler")
	return nil
}

func (h *BaseHandler) Stop() error {
	close(h.stopCh)
	return nil
}

func (h *BaseHandler) handleResourceCreate(obj runtime.Object) {
	task, err := h.GenerateTask(obj)
	if err != nil {
		zlog.SugarErrorf("Failed to handle task creation: %v", err)
	}
	zlog.SugarInfof("Succeeded to handle task creation, task id: %s, task_name:%s", task.ID, task.Name)

	// TODO save to DB
}

func (h *BaseHandler) handleResourceUpdate(oldObj, newObj runtime.Object) {
	zlog.SugarDebugf("Resource updated", "object", newObj)
}

func (h *BaseHandler) handleResourceDelete(obj runtime.Object) {
	zlog.SugarDebugf("Resource deleted", "object", obj)
}
