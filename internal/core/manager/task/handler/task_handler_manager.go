package handler

import (
	"context"
	"fmt"
	"sync"

	"transwarp.io/mlops/mlops-std/stdlog"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

var (
	handlerManager HandlerManager
	once           sync.Once
)

func init() {
	once.Do(func() {
		handlerManager = NewDefaultHandlerManager()
	})
}

func MustGetHandlerManager() HandlerManager {
	if handlerManager == nil {
		panic("handler manager not initialized")
	}
	return handlerManager
}

func RegisterHandler(name string, handler Handler) error {
	return MustGetHandlerManager().RegisterHandler(name, handler)
}

type HandlerManager interface {
	RegisterHandler(name string, handler Handler) error
	StartAll(ctx context.Context) error
	StopAll() error
}

type DefaultHandlerManager struct {
	handlers map[string]Handler
	mu       sync.RWMutex
}

func NewDefaultHandlerManager() *DefaultHandlerManager {
	return &DefaultHandlerManager{
		handlers: make(map[string]Handler),
	}
}

func (dwm *DefaultHandlerManager) RegisterHandler(name string, handler Handler) error {
	dwm.mu.Lock()
	defer dwm.mu.Unlock()

	if _, exists := dwm.handlers[name]; exists {
		return fmt.Errorf("handler for %s already registered", name)
	}

	dwm.handlers[name] = handler
	zlog.SugarInfof("Registered %s handler", name)
	return nil
}

func (dwm *DefaultHandlerManager) StartAll(ctx context.Context) error {
	dwm.mu.RLock()
	defer dwm.mu.RUnlock()

	if err := dwm.registerBuiltinHandlers(); err != nil {
		return fmt.Errorf("failed to register builtin handlers: %w", err)
	}

	for key, handler := range dwm.handlers {
		if err := handler.Start(ctx); err != nil {
			stdlog.Error("Failed to start handler", "resource", key, "error", err)
			return fmt.Errorf("failed to start handler %s: %w", key, err)
		}
	}

	stdlog.Info("All handlers started successfully")
	return nil
}

func (dwm *DefaultHandlerManager) StopAll() error {
	dwm.mu.RLock()
	defer dwm.mu.RUnlock()

	for key, handler := range dwm.handlers {
		if err := handler.Stop(); err != nil {
			stdlog.Error("Failed to stop handler", "resource", key, "error", err)
		}
	}

	stdlog.Info("All handlers stopped")
	return nil
}

func (dwm *DefaultHandlerManager) registerBuiltinHandlers() error {
	extractor := NewDefaultTaskGenerator()

	jobInformer := dwm.informerFactory.Batch().V1().Jobs().Informer()
	jobBaseHandler := NewBaseHandler(
		jobInformer,
		dwm.handleResourceCreate,
		dwm.handleResourceUpdate,
		dwm.handleResourceDelete,
	)
	jobHandler := NewJobHandler(jobBaseHandler, extractor)
	if err := dwm.RegisterHandler(jobHandler); err != nil {
		return err
	}

	podInformer := dwm.informerFactory.Core().V1().Pods().Informer()
	podBaseHandler := NewBaseHandler(
		podInformer,
		dwm.handleResourceCreate,
		dwm.handleResourceUpdate,
		dwm.handleResourceDelete,
	)
	podHandler := NewPodHandler(podBaseHandler, extractor)
	if err := dwm.RegisterHandler(podHandler); err != nil {
		return err
	}

	// TODO: 注册其他资源监听器 (CronJob, Deployment, StatefulSet, etc.)

	return nil
}
