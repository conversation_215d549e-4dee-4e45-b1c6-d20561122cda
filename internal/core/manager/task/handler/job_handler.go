package handler

import (
	"fmt"

	batchv1 "k8s.io/api/batch/v1"
	"k8s.io/apimachinery/pkg/runtime"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
)

type JobHandler struct {
	*BaseHandler
	generator TaskGenerator
}

func init() {
	jobHandler := NewJobHandler(
		NewBaseHandler(
			MustGetHandlerManager().InformerFactory.Batch().V1().Jobs().Informer(),
			func(obj runtime.Object){},
			DefaultHandleResourceUpdate,
			DefaultHandleResourceDelete,
		),
		NewDefaultTaskGenerator(),
	)

	RegisterHandler("Kubernetes-Job", jobHandler)
}

func NewJobHandler(baseHandler *BaseHandler, generator TaskGenerator) *JobHandler {
	return &JobHandler{
		BaseHandler: baseHandler,
		generator:   generator,
	}
}

func (h *JobHandler) GenerateTask(obj runtime.Object) (*model.Task, error) {
	job, ok := obj.(*batchv1.Job)
	if !ok {
		return nil, fmt.Errorf("object is not a Job")
	}

	task, err := h.generator.GenerateTask(job)
	if err != nil {
		return nil, fmt.Errorf("failed to extract resource info: %w", err)
	}

	return task, nil
}
