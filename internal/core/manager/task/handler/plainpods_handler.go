package handler

import (
	"fmt"
	"strings"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/runtime"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
)

type PodHandler struct {
	*BaseHandler
	generator TaskGenerator
}

func init() {
	handler := NewPodHandler(
		NewBaseHandler(
			client.MustGetK8sClient().PodInformer,
			DefaultHandleResourceCreate,
			DefaultHandleResourceUpdate,
			DefaultHandleResourceDelete,
		),
		NewDefaultTaskGenerator(),
	)

	RegisterHandler("Kubernetes-Pod", handler)
}

func NewPodHandler(baseHandler *BaseHandler, generator TaskGenerator) *PodHandler {
	return &PodHandler{
		BaseHandler: baseHandler,
		generator:   generator,
	}
}

func (pw *PodHandler) ShouldProcess(obj runtime.Object) bool {
	if !pw.BaseHandler.ShouldProcess(obj) {
		return false
	}

	pod, ok := obj.(*corev1.Pod)
	if !ok {
		return false
	}

	// 检查是否有 kueue.x-k8s.io/pod-suspending-parent: deployment 注解
	if annotations := pod.GetAnnotations(); annotations != nil {
		if parent, exists := annotations["kueue.x-k8s.io/pod-suspending-parent"]; exists {
			if strings.ToLower(parent) == "deployment" {
				return false // 不处理deployment管理的pod
			}
		}
	}

	// 检查是否有必需的Pod标签
	labels := pod.GetLabels()
	if labels == nil {
		return false
	}

	// 必须有 kueue.x-k8s.io/pod-group-name 标签
	podGroupName, hasPodGroup := labels["kueue.x-k8s.io/pod-group-name"]
	if !hasPodGroup || podGroupName == "" {
		return false
	}

	// 必须有 kueue.x-k8s.io/queue-name 标签
	queueName, hasQueue := labels["kueue.x-k8s.io/queue-name"]
	if !hasQueue || queueName == "" {
		return false
	}

	return true
}

func (pw *PodHandler) GenerateTask(obj runtime.Object) (*model.Task, error) {
	pod, ok := obj.(*corev1.Pod)
	if !ok {
		return nil, fmt.Errorf("object is not a Pod")
	}

	task, err := pw.generator.GenerateTask(pod)
	if err != nil {
		return nil, fmt.Errorf("failed to extract resource info: %w", err)
	}

	return task, nil
}
