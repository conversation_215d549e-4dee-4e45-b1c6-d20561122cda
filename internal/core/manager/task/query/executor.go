package query

import (
	"context"
	"fmt"
	"sync"
	"time"
)

type QueryExecutor[T any] struct {
	dataProvider DataProvider[T]
	config       *ExecutorConfig
	mu           sync.RWMutex
}

type DataProvider[T any] interface {
	GetData(ctx context.Context) ([]T, error)
	Count(ctx context.Context) (int32, error)
}

type ExecutorConfig struct {
	QueryTimeout       time.Duration `json:"query_timeout"`
	SlowQueryThreshold time.Duration `json:"slow_query_threshold"`
	MaxConcurrency     int           `json:"max_concurrency"`
}

func NewQueryExecutor[T any](provider DataProvider[T], config *ExecutorConfig) *QueryExecutor[T] {
	if config == nil {
		config = DefaultExecutorConfig()
	}

	executor := &QueryExecutor[T]{
		dataProvider: provider,
		config:       config,
	}

	return executor
}

func (qe *QueryExecutor[T]) Execute(ctx context.Context, query *QueryBuilder[T]) (*QueryResult[T], error) {
	startTime := time.Now()

	if qe.config.QueryTimeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, qe.config.QueryTimeout)
		defer cancel()
	}

	data, err := qe.dataProvider.GetData(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get data: %w", err)
	}

	result := query.Execute(data)
	result.QueryTime = time.Since(startTime).Milliseconds()

	return result, nil
}

func (qe *QueryExecutor[T]) Count(ctx context.Context, query *QueryBuilder[T]) (int32, error) {
	data, err := qe.dataProvider.GetData(ctx)
	if err != nil {
		return 0, fmt.Errorf("failed to get data: %w", err)
	}

	return query.Count(data), nil
}

func (qe *QueryExecutor[T]) Exists(ctx context.Context, query *QueryBuilder[T]) (bool, error) {
	data, err := qe.dataProvider.GetData(ctx)
	if err != nil {
		return false, fmt.Errorf("failed to get data: %w", err)
	}

	return query.Exists(data), nil
}

func (qe *QueryExecutor[T]) First(ctx context.Context, query *QueryBuilder[T]) (*T, error) {
	data, err := qe.dataProvider.GetData(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get data: %w", err)
	}

	item, found := query.First(data)
	if !found {
		return nil, fmt.Errorf("no matching record found")
	}

	return item, nil
}

func (qe *QueryExecutor[T]) ExecuteAsync(ctx context.Context, query *QueryBuilder[T]) <-chan *AsyncResult[T] {
	resultChan := make(chan *AsyncResult[T], 1)

	go func() {
		defer close(resultChan)

		result, err := qe.Execute(ctx, query)
		resultChan <- &AsyncResult[T]{
			Result: result,
			Error:  err,
		}
	}()

	return resultChan
}

func (qe *QueryExecutor[T]) ExecuteBatch(ctx context.Context, queries []*QueryBuilder[T]) ([]*QueryResult[T], error) {
	if len(queries) == 0 {
		return []*QueryResult[T]{}, nil
	}

	data, err := qe.dataProvider.GetData(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get data: %w", err)
	}

	results := make([]*QueryResult[T], len(queries))

	if qe.config.MaxConcurrency > 1 {
		return qe.executeBatchConcurrent(ctx, queries, data)
	}

	for i, query := range queries {
		results[i] = query.Execute(data)
	}

	return results, nil
}

func (qe *QueryExecutor[T]) executeBatchConcurrent(ctx context.Context, queries []*QueryBuilder[T], data []T) ([]*QueryResult[T], error) {
	results := make([]*QueryResult[T], len(queries))
	semaphore := make(chan struct{}, qe.config.MaxConcurrency)
	var wg sync.WaitGroup
	var mu sync.Mutex

	for i, query := range queries {
		wg.Add(1)
		go func(index int, q *QueryBuilder[T]) {
			defer wg.Done()

			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			result := q.Execute(data)

			mu.Lock()
			results[index] = result
			mu.Unlock()
		}(i, query)
	}

	wg.Wait()
	return results, nil
}

type AsyncResult[T any] struct {
	Result *QueryResult[T]
	Error  error
}

func DefaultExecutorConfig() *ExecutorConfig {
	return &ExecutorConfig{
		QueryTimeout:       30 * time.Second,
		SlowQueryThreshold: 1 * time.Second,
		MaxConcurrency:     10,
	}
}
