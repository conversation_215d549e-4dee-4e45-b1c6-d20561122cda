package query

import (
	"fmt"
	"strings"

	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/selection"
)

// LabelSelectorFilter 标签选择器过滤器
type LabelSelectorFilter[T any] struct {
	selector    labels.Selector
	selectorStr string
	getLabels   func(T) map[string]string
}

// NewLabelSelectorFilter 创建标签选择器过滤器
func NewLabelSelectorFilter[T any](selectorStr string, getLabels func(T) map[string]string) (*LabelSelectorFilter[T], error) {
	selector, err := labels.Parse(selectorStr)
	if err != nil {
		return nil, fmt.Errorf("invalid label selector: %w", err)
	}
	
	return &LabelSelectorFilter[T]{
		selector:    selector,
		selectorStr: selectorStr,
		getLabels:   getLabels,
	}, nil
}

// Match 匹配检查
func (lsf *LabelSelectorFilter[T]) Match(item T) bool {
	itemLabels := lsf.getLabels(item)
	labelSet := labels.Set(itemLabels)
	return lsf.selector.Matches(labelSet)
}

// String 字符串表示
func (lsf *LabelSelectorFilter[T]) String() string {
	return fmt.Sprintf("LABEL_SELECTOR(%s)", lsf.selectorStr)
}

// LabelSelectorBuilder 标签选择器构建器
type LabelSelectorBuilder struct {
	requirements []labels.Requirement
}

// NewLabelSelectorBuilder 创建标签选择器构建器
func NewLabelSelectorBuilder() *LabelSelectorBuilder {
	return &LabelSelectorBuilder{
		requirements: make([]labels.Requirement, 0),
	}
}

// Equals 添加等于条件
func (lsb *LabelSelectorBuilder) Equals(key, value string) *LabelSelectorBuilder {
	req, _ := labels.NewRequirement(key, selection.Equals, []string{value})
	lsb.requirements = append(lsb.requirements, *req)
	return lsb
}

// NotEquals 添加不等于条件
func (lsb *LabelSelectorBuilder) NotEquals(key, value string) *LabelSelectorBuilder {
	req, _ := labels.NewRequirement(key, selection.NotEquals, []string{value})
	lsb.requirements = append(lsb.requirements, *req)
	return lsb
}

// In 添加IN条件
func (lsb *LabelSelectorBuilder) In(key string, values []string) *LabelSelectorBuilder {
	req, _ := labels.NewRequirement(key, selection.In, values)
	lsb.requirements = append(lsb.requirements, *req)
	return lsb
}

// NotIn 添加NOT IN条件
func (lsb *LabelSelectorBuilder) NotIn(key string, values []string) *LabelSelectorBuilder {
	req, _ := labels.NewRequirement(key, selection.NotIn, values)
	lsb.requirements = append(lsb.requirements, *req)
	return lsb
}

// Exists 添加存在条件
func (lsb *LabelSelectorBuilder) Exists(key string) *LabelSelectorBuilder {
	req, _ := labels.NewRequirement(key, selection.Exists, nil)
	lsb.requirements = append(lsb.requirements, *req)
	return lsb
}

// DoesNotExist 添加不存在条件
func (lsb *LabelSelectorBuilder) DoesNotExist(key string) *LabelSelectorBuilder {
	req, _ := labels.NewRequirement(key, selection.DoesNotExist, nil)
	lsb.requirements = append(lsb.requirements, *req)
	return lsb
}

// GreaterThan 添加大于条件（用于数值标签）
func (lsb *LabelSelectorBuilder) GreaterThan(key, value string) *LabelSelectorBuilder {
	req, _ := labels.NewRequirement(key, selection.GreaterThan, []string{value})
	lsb.requirements = append(lsb.requirements, *req)
	return lsb
}

// LessThan 添加小于条件（用于数值标签）
func (lsb *LabelSelectorBuilder) LessThan(key, value string) *LabelSelectorBuilder {
	req, _ := labels.NewRequirement(key, selection.LessThan, []string{value})
	lsb.requirements = append(lsb.requirements, *req)
	return lsb
}

// Build 构建选择器
func (lsb *LabelSelectorBuilder) Build() labels.Selector {
	return labels.NewSelector().Add(lsb.requirements...)
}

// BuildString 构建选择器字符串
func (lsb *LabelSelectorBuilder) BuildString() string {
	if len(lsb.requirements) == 0 {
		return ""
	}
	
	parts := make([]string, len(lsb.requirements))
	for i, req := range lsb.requirements {
		parts[i] = req.String()
	}
	
	return strings.Join(parts, ",")
}

// Reset 重置构建器
func (lsb *LabelSelectorBuilder) Reset() *LabelSelectorBuilder {
	lsb.requirements = lsb.requirements[:0]
	return lsb
}

// LabelMatcher 标签匹配器
type LabelMatcher struct {
	matchers []LabelMatchFunc
}

// LabelMatchFunc 标签匹配函数
type LabelMatchFunc func(labels map[string]string) bool

// NewLabelMatcher 创建标签匹配器
func NewLabelMatcher() *LabelMatcher {
	return &LabelMatcher{
		matchers: make([]LabelMatchFunc, 0),
	}
}

// HasLabel 添加标签存在检查
func (lm *LabelMatcher) HasLabel(key string) *LabelMatcher {
	lm.matchers = append(lm.matchers, func(labels map[string]string) bool {
		_, exists := labels[key]
		return exists
	})
	return lm
}

// LabelEquals 添加标签值等于检查
func (lm *LabelMatcher) LabelEquals(key, value string) *LabelMatcher {
	lm.matchers = append(lm.matchers, func(labels map[string]string) bool {
		v, exists := labels[key]
		return exists && v == value
	})
	return lm
}

// LabelIn 添加标签值IN检查
func (lm *LabelMatcher) LabelIn(key string, values []string) *LabelMatcher {
	lm.matchers = append(lm.matchers, func(labels map[string]string) bool {
		v, exists := labels[key]
		if !exists {
			return false
		}
		for _, value := range values {
			if v == value {
				return true
			}
		}
		return false
	})
	return lm
}

// LabelContains 添加标签值包含检查
func (lm *LabelMatcher) LabelContains(key, substring string) *LabelMatcher {
	lm.matchers = append(lm.matchers, func(labels map[string]string) bool {
		v, exists := labels[key]
		return exists && strings.Contains(v, substring)
	})
	return lm
}

// LabelStartsWith 添加标签值开始检查
func (lm *LabelMatcher) LabelStartsWith(key, prefix string) *LabelMatcher {
	lm.matchers = append(lm.matchers, func(labels map[string]string) bool {
		v, exists := labels[key]
		return exists && strings.HasPrefix(v, prefix)
	})
	return lm
}

// LabelEndsWith 添加标签值结束检查
func (lm *LabelMatcher) LabelEndsWith(key, suffix string) *LabelMatcher {
	lm.matchers = append(lm.matchers, func(labels map[string]string) bool {
		v, exists := labels[key]
		return exists && strings.HasSuffix(v, suffix)
	})
	return lm
}

// Match 执行匹配
func (lm *LabelMatcher) Match(labels map[string]string) bool {
	for _, matcher := range lm.matchers {
		if !matcher(labels) {
			return false
		}
	}
	return true
}

// 预定义的常用标签选择器

// CommonLabelSelectors 常用标签选择器
type CommonLabelSelectors struct{}

// ByApp 按应用名称选择
func (cls *CommonLabelSelectors) ByApp(app string) string {
	return fmt.Sprintf("app=%s", app)
}

// ByVersion 按版本选择
func (cls *CommonLabelSelectors) ByVersion(version string) string {
	return fmt.Sprintf("version=%s", version)
}

// ByEnvironment 按环境选择
func (cls *CommonLabelSelectors) ByEnvironment(env string) string {
	return fmt.Sprintf("environment=%s", env)
}

// ByComponent 按组件选择
func (cls *CommonLabelSelectors) ByComponent(component string) string {
	return fmt.Sprintf("component=%s", component)
}

// ByTier 按层级选择
func (cls *CommonLabelSelectors) ByTier(tier string) string {
	return fmt.Sprintf("tier=%s", tier)
}

// ByOwner 按所有者选择
func (cls *CommonLabelSelectors) ByOwner(owner string) string {
	return fmt.Sprintf("owner=%s", owner)
}

// ByProject 按项目选择
func (cls *CommonLabelSelectors) ByProject(project string) string {
	return fmt.Sprintf("project=%s", project)
}

// ByTeam 按团队选择
func (cls *CommonLabelSelectors) ByTeam(team string) string {
	return fmt.Sprintf("team=%s", team)
}

// Production 生产环境
func (cls *CommonLabelSelectors) Production() string {
	return "environment=production"
}

// Development 开发环境
func (cls *CommonLabelSelectors) Development() string {
	return "environment=development"
}

// Testing 测试环境
func (cls *CommonLabelSelectors) Testing() string {
	return "environment=testing"
}

// Staging 预发布环境
func (cls *CommonLabelSelectors) Staging() string {
	return "environment=staging"
}

// 全局实例
var CommonLabels = &CommonLabelSelectors{}

// WorkloadLabelSelectors Workload专用标签选择器
type WorkloadLabelSelectors struct{}

// ByWorkloadType 按工作负载类型选择
func (wls *WorkloadLabelSelectors) ByWorkloadType(workloadType string) string {
	return fmt.Sprintf("workload-type=%s", workloadType)
}

// ByPriority 按优先级选择
func (wls *WorkloadLabelSelectors) ByPriority(priority string) string {
	return fmt.Sprintf("priority=%s", priority)
}

// ByQueue 按队列选择
func (wls *WorkloadLabelSelectors) ByQueue(queue string) string {
	return fmt.Sprintf("queue=%s", queue)
}

// ByCreator 按创建者选择
func (wls *WorkloadLabelSelectors) ByCreator(creator string) string {
	return fmt.Sprintf("creator=%s", creator)
}

// Pipeline 流水线工作负载
func (wls *WorkloadLabelSelectors) Pipeline() string {
	return "workload-type=PIPELINE"
}

// Training 训练工作负载
func (wls *WorkloadLabelSelectors) Training() string {
	return "workload-type=TRAINING"
}

// Inference 推理工作负载
func (wls *WorkloadLabelSelectors) Inference() string {
	return "workload-type=INFERENCE"
}

// HighPriority 高优先级工作负载
func (wls *WorkloadLabelSelectors) HighPriority() string {
	return "priority in (high,urgent)"
}

// LowPriority 低优先级工作负载
func (wls *WorkloadLabelSelectors) LowPriority() string {
	return "priority=low"
}

// GPUWorkloads GPU工作负载
func (wls *WorkloadLabelSelectors) GPUWorkloads() string {
	return "gpu=true"
}

// 全局实例
var WorkloadLabels = &WorkloadLabelSelectors{}

// LabelUtils 标签工具函数
type LabelUtils struct{}

// ParseSelector 解析选择器
func (lu *LabelUtils) ParseSelector(selector string) (labels.Selector, error) {
	return labels.Parse(selector)
}

// ValidateSelector 验证选择器
func (lu *LabelUtils) ValidateSelector(selector string) error {
	_, err := labels.Parse(selector)
	return err
}

// MatchesAny 检查是否匹配任一选择器
func (lu *LabelUtils) MatchesAny(labelSet map[string]string, selectors []string) bool {
	labelSetObj := labels.Set(labelSet)
	for _, selectorStr := range selectors {
		if selector, err := labels.Parse(selectorStr); err == nil {
			if selector.Matches(labelSetObj) {
				return true
			}
		}
	}
	return false
}

// MatchesAll 检查是否匹配所有选择器
func (lu *LabelUtils) MatchesAll(labelSet map[string]string, selectors []string) bool {
	labelSetObj := labels.Set(labelSet)
	for _, selectorStr := range selectors {
		selector, err := labels.Parse(selectorStr)
		if err != nil || !selector.Matches(labelSetObj) {
			return false
		}
	}
	return true
}

// CombineSelectors 组合选择器
func (lu *LabelUtils) CombineSelectors(selectors []string) string {
	validSelectors := make([]string, 0, len(selectors))
	for _, selector := range selectors {
		if selector != "" && lu.ValidateSelector(selector) == nil {
			validSelectors = append(validSelectors, selector)
		}
	}
	return strings.Join(validSelectors, ",")
}

// 全局实例
var LabelUtil = &LabelUtils{}
