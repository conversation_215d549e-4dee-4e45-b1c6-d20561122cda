package query

import (
	"time"

	"k8s.io/apimachinery/pkg/labels"
)

// QueryResult 查询结果
type QueryResult[T any] struct {
	Items      []T      `json:"items"`       // 结果项
	Total      int32    `json:"total"`       // 总数
	Page       int32    `json:"page"`        // 当前页
	PageSize   int32    `json:"page_size"`   // 页大小
	TotalPages int32    `json:"total_pages"` // 总页数
	Filters    []string `json:"filters"`     // 应用的过滤器
	Sorters    []string `json:"sorters"`     // 应用的排序器
	QueryTime  int64    `json:"query_time"`  // 查询耗时(毫秒)
}

// HasNextPage 是否有下一页
func (qr *QueryResult[T]) HasNextPage() bool {
	return qr.Page < qr.TotalPages
}

// HasPrevPage 是否有上一页
func (qr *QueryResult[T]) HasPrevPage() bool {
	return qr.Page > 1
}

// IsEmpty 是否为空结果
func (qr *QueryResult[T]) IsEmpty() bool {
	return len(qr.Items) == 0
}

// GetNextPage 获取下一页页码
func (qr *QueryResult[T]) GetNextPage() int32 {
	if qr.HasNextPage() {
		return qr.Page + 1
	}
	return qr.Page
}

// GetPrevPage 获取上一页页码
func (qr *QueryResult[T]) GetPrevPage() int32 {
	if qr.HasPrevPage() {
		return qr.Page - 1
	}
	return qr.Page
}

// Pagination 分页信息
type Pagination struct {
	Page     int32 `json:"page"`      // 页码，从1开始
	PageSize int32 `json:"page_size"` // 页大小
}

// NewPagination 创建分页信息
func NewPagination(page, pageSize int32) *Pagination {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 1000 {
		pageSize = 1000
	}

	return &Pagination{
		Page:     page,
		PageSize: pageSize,
	}
}

// GetOffset 获取偏移量
func (p *Pagination) GetOffset() int32 {
	return (p.Page - 1) * p.PageSize
}

// GetLimit 获取限制数量
func (p *Pagination) GetLimit() int32 {
	return p.PageSize
}

// Validate 验证分页参数
func (p *Pagination) Validate() error {
	if p.Page <= 0 {
		p.Page = 1
	}
	if p.PageSize <= 0 {
		p.PageSize = 20
	}
	if p.PageSize > 1000 {
		p.PageSize = 1000
	}
	return nil
}

// QueryOptions 查询选项
type QueryOptions struct {
	Timeout      time.Duration `json:"timeout"`       // 查询超时
	MaxResults   int32         `json:"max_results"`   // 最大结果数
	IncludeCount bool          `json:"include_count"` // 是否包含总数统计
	DebugMode    bool          `json:"debug_mode"`    // 调试模式
	TrackMetrics bool          `json:"track_metrics"` // 是否跟踪指标
}

// NewQueryOptions 创建查询选项
func NewQueryOptions() *QueryOptions {
	return &QueryOptions{
		Timeout:      30 * time.Second,
		MaxResults:   10000,
		IncludeCount: true,
		DebugMode:    false,
		TrackMetrics: true,
	}
}

// LabelSelector 标签选择器包装
type LabelSelector struct {
	Selector labels.Selector
	Raw      string
}

// NewLabelSelector 创建标签选择器
func NewLabelSelector(selector string) (*LabelSelector, error) {
	parsed, err := labels.Parse(selector)
	if err != nil {
		return nil, err
	}

	return &LabelSelector{
		Selector: parsed,
		Raw:      selector,
	}, nil
}

// Matches 检查标签是否匹配
func (ls *LabelSelector) Matches(labelSet labels.Set) bool {
	if ls.Selector == nil {
		return true
	}
	return ls.Selector.Matches(labelSet)
}

// String 字符串表示
func (ls *LabelSelector) String() string {
	return ls.Raw
}

// QueryStats 查询统计信息
type QueryStats struct {
	TotalQueries  int64         `json:"total_queries"`   // 总查询数
	CacheHits     int64         `json:"cache_hits"`      // 缓存命中数
	CacheMisses   int64         `json:"cache_misses"`    // 缓存未命中数
	AverageTime   time.Duration `json:"average_time"`    // 平均查询时间
	SlowQueries   int64         `json:"slow_queries"`    // 慢查询数
	ErrorQueries  int64         `json:"error_queries"`   // 错误查询数
	LastQueryTime time.Time     `json:"last_query_time"` // 最后查询时间
	LastError     string        `json:"last_error"`      // 最后错误
}

// QueryMetrics 查询指标
type QueryMetrics struct {
	QueryID     string        `json:"query_id"`     // 查询ID
	StartTime   time.Time     `json:"start_time"`   // 开始时间
	EndTime     time.Time     `json:"end_time"`     // 结束时间
	Duration    time.Duration `json:"duration"`     // 持续时间
	FilterCount int           `json:"filter_count"` // 过滤器数量
	SorterCount int           `json:"sorter_count"` // 排序器数量
	ResultCount int32         `json:"result_count"` // 结果数量
	TotalCount  int32         `json:"total_count"`  // 总数量
	CacheHit    bool          `json:"cache_hit"`    // 是否缓存命中
	Error       string        `json:"error"`        // 错误信息
}

// SortSpec 排序规格
type SortSpec struct {
	Field     string        `json:"field"`     // 字段名
	Direction SortDirection `json:"direction"` // 排序方向
}

// NewSortSpec 创建排序规格
func NewSortSpec(field string, direction SortDirection) *SortSpec {
	return &SortSpec{
		Field:     field,
		Direction: direction,
	}
}

// FilterSpec 过滤器规格
type FilterSpec struct {
	Field    string      `json:"field"`    // 字段名
	Operator Operator    `json:"operator"` // 操作符
	Value    interface{} `json:"value"`    // 值
}

// NewFilterSpec 创建过滤器规格
func NewFilterSpec(field string, operator Operator, value interface{}) *FilterSpec {
	return &FilterSpec{
		Field:    field,
		Operator: operator,
		Value:    value,
	}
}

// QueryRequest 查询请求
type QueryRequest struct {
	Filters       []FilterSpec  `json:"filters"`        // 过滤器列表
	Sorters       []SortSpec    `json:"sorters"`        // 排序器列表
	Pagination    *Pagination   `json:"pagination"`     // 分页信息
	LabelSelector string        `json:"label_selector"` // 标签选择器
	Options       *QueryOptions `json:"options"`        // 查询选项
}

// NewQueryRequest 创建查询请求
func NewQueryRequest() *QueryRequest {
	return &QueryRequest{
		Filters:    make([]FilterSpec, 0),
		Sorters:    make([]SortSpec, 0),
		Pagination: NewPagination(1, 20),
		Options:    NewQueryOptions(),
	}
}

// AddFilter 添加过滤器
func (qr *QueryRequest) AddFilter(field string, operator Operator, value interface{}) *QueryRequest {
	qr.Filters = append(qr.Filters, *NewFilterSpec(field, operator, value))
	return qr
}

// AddSort 添加排序
func (qr *QueryRequest) AddSort(field string, direction SortDirection) *QueryRequest {
	qr.Sorters = append(qr.Sorters, *NewSortSpec(field, direction))
	return qr
}

// SetPagination 设置分页
func (qr *QueryRequest) SetPagination(page, pageSize int32) *QueryRequest {
	qr.Pagination = NewPagination(page, pageSize)
	return qr
}

// SetLabelSelector 设置标签选择器
func (qr *QueryRequest) SetLabelSelector(selector string) *QueryRequest {
	qr.LabelSelector = selector
	return qr
}

// Validate 验证请求
func (qr *QueryRequest) Validate() error {
	if qr.Pagination != nil {
		if err := qr.Pagination.Validate(); err != nil {
			return err
		}
	}

	// 验证标签选择器
	if qr.LabelSelector != "" {
		_, err := labels.Parse(qr.LabelSelector)
		if err != nil {
			return err
		}
	}

	return nil
}
