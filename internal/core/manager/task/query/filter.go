package query

import (
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

type Operator string

const (
	OpEqual              Operator = "eq"       // 等于
	OpNotEqual           Operator = "ne"       // 不等于
	OpGreaterThan        Operator = "gt"       // 大于
	OpGreaterThanOrEqual Operator = "gte"      // 大于等于
	OpLessThan           Operator = "lt"       // 小于
	OpLessThanOrEqual    Operator = "lte"      // 小于等于
	OpIn                 Operator = "in"       // 包含
	OpNotIn              Operator = "nin"      // 不包含
	OpContains           Operator = "contains" // 字符串包含
	OpStartsWith         Operator = "starts"   // 字符串开始
	OpEndsWith           Operator = "ends"     // 字符串结束
	OpRegex              Operator = "regex"    // 正则表达式
	OpIsNull             Operator = "null"     // 为空
	OpIsNotNull          Operator = "notnull"  // 不为空
	OpBetween            Operator = "between"  // 范围
)

type Filter[T any] interface {
	Match(item T) bool
	String() string
}

type FieldFilter[T any] struct {
	Field    string
	Operator Operator
	Value    interface{}
}

func NewFieldFilter[T any](field string, operator Operator, value interface{}) *FieldFilter[T] {
	return &FieldFilter[T]{
		Field:    field,
		Operator: operator,
		Value:    value,
	}
}

func (f *FieldFilter[T]) Match(item T) bool {
	fieldValue, err := GetFieldValue(item, f.Field)
	if err != nil {
		return false
	}

	return f.compareValues(fieldValue, f.Value, f.Operator)
}

func (f *FieldFilter[T]) String() string {
	return fmt.Sprintf("%s %s %v", f.Field, f.Operator, f.Value)
}

func (f *FieldFilter[T]) compareValues(fieldValue, filterValue interface{}, op Operator) bool {
	switch op {
	case OpEqual:
		return f.isEqual(fieldValue, filterValue)
	case OpNotEqual:
		return !f.isEqual(fieldValue, filterValue)
	case OpGreaterThan:
		return f.isGreater(fieldValue, filterValue)
	case OpGreaterThanOrEqual:
		return f.isGreater(fieldValue, filterValue) || f.isEqual(fieldValue, filterValue)
	case OpLessThan:
		return f.isLess(fieldValue, filterValue)
	case OpLessThanOrEqual:
		return f.isLess(fieldValue, filterValue) || f.isEqual(fieldValue, filterValue)
	case OpContains:
		return f.contains(fieldValue, filterValue)
	case OpStartsWith:
		return f.startsWith(fieldValue, filterValue)
	case OpEndsWith:
		return f.endsWith(fieldValue, filterValue)
	case OpRegex:
		return f.matchRegex(fieldValue, filterValue)
	case OpIsNull:
		return fieldValue == nil || reflect.ValueOf(fieldValue).IsZero()
	case OpIsNotNull:
		return fieldValue != nil && !reflect.ValueOf(fieldValue).IsZero()
	default:
		return false
	}
}

func (f *FieldFilter[T]) isEqual(a, b interface{}) bool {
	if a == nil && b == nil {
		return true
	}
	if a == nil || b == nil {
		return false
	}
	return reflect.DeepEqual(a, b)
}

func (f *FieldFilter[T]) isGreater(a, b interface{}) bool {
	return f.compareNumeric(a, b) > 0
}

func (f *FieldFilter[T]) isLess(a, b interface{}) bool {
	return f.compareNumeric(a, b) < 0
}

func (f *FieldFilter[T]) compareNumeric(a, b interface{}) int {
	aVal := reflect.ValueOf(a)
	bVal := reflect.ValueOf(b)

	if aTime, ok := a.(time.Time); ok {
		if bTime, ok := b.(time.Time); ok {
			if aTime.Before(bTime) {
				return -1
			} else if aTime.After(bTime) {
				return 1
			}
			return 0
		}
	}

	if aVal.Kind() >= reflect.Int && aVal.Kind() <= reflect.Float64 &&
		bVal.Kind() >= reflect.Int && bVal.Kind() <= reflect.Float64 {
		aFloat := aVal.Convert(reflect.TypeOf(float64(0))).Float()
		bFloat := bVal.Convert(reflect.TypeOf(float64(0))).Float()

		if aFloat < bFloat {
			return -1
		} else if aFloat > bFloat {
			return 1
		}
		return 0
	}

	if aStr, ok := a.(string); ok {
		if bStr, ok := b.(string); ok {
			return strings.Compare(aStr, bStr)
		}
	}

	return 0
}

func (f *FieldFilter[T]) contains(fieldValue, filterValue interface{}) bool {
	fieldStr := fmt.Sprintf("%v", fieldValue)
	filterStr := fmt.Sprintf("%v", filterValue)
	return strings.Contains(strings.ToLower(fieldStr), strings.ToLower(filterStr))
}

func (f *FieldFilter[T]) startsWith(fieldValue, filterValue interface{}) bool {
	fieldStr := fmt.Sprintf("%v", fieldValue)
	filterStr := fmt.Sprintf("%v", filterValue)
	return strings.HasPrefix(strings.ToLower(fieldStr), strings.ToLower(filterStr))
}

func (f *FieldFilter[T]) endsWith(fieldValue, filterValue interface{}) bool {
	fieldStr := fmt.Sprintf("%v", fieldValue)
	filterStr := fmt.Sprintf("%v", filterValue)
	return strings.HasSuffix(strings.ToLower(fieldStr), strings.ToLower(filterStr))
}

func (f *FieldFilter[T]) matchRegex(fieldValue, filterValue interface{}) bool {
	fieldStr := fmt.Sprintf("%v", fieldValue)
	pattern := fmt.Sprintf("%v", filterValue)

	regex, err := regexp.Compile(pattern)
	if err != nil {
		return false
	}

	return regex.MatchString(fieldStr)
}

type InFilter[T any] struct {
	Field  string
	Values []interface{}
}

func NewInFilter[T any](field string, values []interface{}) *InFilter[T] {
	return &InFilter[T]{
		Field:  field,
		Values: values,
	}
}

func (f *InFilter[T]) Match(item T) bool {
	fieldValue, err := GetFieldValue(item, f.Field)
	if err != nil {
		return false
	}

	for _, value := range f.Values {
		if reflect.DeepEqual(fieldValue, value) {
			return true
		}
	}
	return false
}

func (f *InFilter[T]) String() string {
	return fmt.Sprintf("%s IN %v", f.Field, f.Values)
}

type RangeFilter[T any] struct {
	Field string
	Start interface{}
	End   interface{}
}

func NewRangeFilter[T any](field string, start, end interface{}) *RangeFilter[T] {
	return &RangeFilter[T]{
		Field: field,
		Start: start,
		End:   end,
	}
}

func (f *RangeFilter[T]) Match(item T) bool {
	fieldValue, err := GetFieldValue(item, f.Field)
	if err != nil {
		return false
	}

	fieldFilter := &FieldFilter[T]{Field: f.Field}

	if !fieldFilter.compareValues(fieldValue, f.Start, OpGreaterThanOrEqual) {
		return false
	}

	if !fieldFilter.compareValues(fieldValue, f.End, OpLessThanOrEqual) {
		return false
	}

	return true
}

func (f *RangeFilter[T]) String() string {
	return fmt.Sprintf("%s BETWEEN %v AND %v", f.Field, f.Start, f.End)
}

type FuncFilter[T any] struct {
	Fn   func(T) bool
	Name string
}

func NewFuncFilter[T any](fn func(T) bool) *FuncFilter[T] {
	return &FuncFilter[T]{
		Fn:   fn,
		Name: "custom_function",
	}
}

func NewNamedFuncFilter[T any](name string, fn func(T) bool) *FuncFilter[T] {
	return &FuncFilter[T]{
		Fn:   fn,
		Name: name,
	}
}

func (f *FuncFilter[T]) Match(item T) bool {
	return f.Fn(item)
}

func (f *FuncFilter[T]) String() string {
	return fmt.Sprintf("FUNC(%s)", f.Name)
}

type CompositeFilter[T any] struct {
	Filters []Filter[T]
	Logic   LogicOperator
}

type LogicOperator string

const (
	LogicAnd LogicOperator = "AND"
	LogicOr  LogicOperator = "OR"
)

func NewCompositeFilter[T any](logic LogicOperator, filters ...Filter[T]) *CompositeFilter[T] {
	return &CompositeFilter[T]{
		Filters: filters,
		Logic:   logic,
	}
}

func (f *CompositeFilter[T]) Match(item T) bool {
	if len(f.Filters) == 0 {
		return true
	}

	switch f.Logic {
	case LogicAnd:
		for _, filter := range f.Filters {
			if !filter.Match(item) {
				return false
			}
		}
		return true
	case LogicOr:
		for _, filter := range f.Filters {
			if filter.Match(item) {
				return true
			}
		}
		return false
	default:
		return false
	}
}

func (f *CompositeFilter[T]) String() string {
	if len(f.Filters) == 0 {
		return "EMPTY"
	}

	parts := make([]string, len(f.Filters))
	for i, filter := range f.Filters {
		parts[i] = filter.String()
	}

	return fmt.Sprintf("(%s)", strings.Join(parts, fmt.Sprintf(" %s ", f.Logic)))
}
