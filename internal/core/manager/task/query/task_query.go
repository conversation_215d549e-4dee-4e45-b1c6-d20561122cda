package query

import (
	"time"

	"k8s.io/apimachinery/pkg/labels"

	model "transwarp.io/mlops/pipeline/internal/core/model/task"
)

type TaskQuery struct {
	*QueryBuilder[*model.Task]
}

func NewTaskQuery() *TaskQuery {
	return &TaskQuery{
		QueryBuilder: NewQueryBuilder[*model.Task](),
	}
}

func (wq *TaskQuery) WhereProjectIDs(projectIDs []string) *TaskQuery {
	if len(projectIDs) > 0 {
		values := make([]interface{}, len(projectIDs))
		for i, p := range projectIDs {
			values[i] = p
		}
		wq.WhereIn("ProjectID", values)
	}
	return wq
}

func (wq *TaskQuery) WhereTenantIDs(projectIDs []string) *TaskQuery {
	if len(projectIDs) > 0 {
		values := make([]interface{}, len(projectIDs))
		for i, p := range projectIDs {
			values[i] = p
		}
		wq.WhereIn("TenantID", values)
	}
	return wq
}

func (wq *TaskQuery) WhereStatus(status model.TaskStatus) *TaskQuery {
	wq.WhereField("Status", OpEqual, status)
	return wq
}

func (wq *TaskQuery) WhereStatuses(statuses []model.TaskStatus) *TaskQuery {
	if len(statuses) > 0 {
		values := make([]interface{}, len(statuses))
		for i, s := range statuses {
			values[i] = s
		}
		wq.WhereIn("Status", values)
	}
	return wq
}

func (wq *TaskQuery) WhereType(workloadType model.TaskType) *TaskQuery {
	wq.WhereField("Type", OpEqual, workloadType)
	return wq
}

func (wq *TaskQuery) WhereTypes(types []model.TaskType) *TaskQuery {
	if len(types) > 0 {
		values := make([]interface{}, len(types))
		for i, t := range types {
			values[i] = t
		}
		wq.WhereIn("Type", values)
	}
	return wq
}

func (wq *TaskQuery) WherePriority(priority model.TaskPriority) *TaskQuery {
	wq.WhereField("Priority", OpEqual, priority)
	return wq
}

func (wq *TaskQuery) WherePriorities(priorities []model.TaskPriority) *TaskQuery {
	if len(priorities) > 0 {
		values := make([]interface{}, len(priorities))
		for i, p := range priorities {
			values[i] = p
		}
		wq.WhereIn("Priority", values)
	}
	return wq
}

func (wq *TaskQuery) WhereCreator(creator string) *TaskQuery {
	if creator != "" {
		wq.WhereField("Creator", OpEqual, creator)
	}
	return wq
}

func (wq *TaskQuery) WhereQueueName(queueName string) *TaskQuery {
	if queueName != "" {
		wq.WhereField("QueueName", OpEqual, queueName)
	}
	return wq
}

func (wq *TaskQuery) WhereClusterQueueName(clusterQueueName string) *TaskQuery {
	if clusterQueueName != "" {
		wq.WhereField("ClusterQueueName", OpEqual, clusterQueueName)
	}
	return wq
}

func (wq *TaskQuery) WhereNameContains(keyword string) *TaskQuery {
	if keyword != "" {
		wq.WhereField("Name", OpContains, keyword)
	}
	return wq
}

func (wq *TaskQuery) WhereDescriptionContains(keyword string) *TaskQuery {
	if keyword != "" {
		wq.WhereField("Description", OpContains, keyword)
	}
	return wq
}

func (wq *TaskQuery) WhereKeywordSearch(keyword string) *TaskQuery {
	if keyword != "" {
		nameFilter := NewFieldFilter[*model.Task]("Name", OpContains, keyword)
		descFilter := NewFieldFilter[*model.Task]("Description", OpContains, keyword)
		compositeFilter := NewCompositeFilter[*model.Task](LogicOr, nameFilter, descFilter)
		wq.Where(compositeFilter)
	}
	return wq
}

func (wq *TaskQuery) WhereCreatedAfter(t time.Time) *TaskQuery {
	wq.WhereField("CreatedAt", OpGreaterThan, t)
	return wq
}

func (wq *TaskQuery) WhereCreatedBefore(t time.Time) *TaskQuery {
	wq.WhereField("CreatedAt", OpLessThan, t)
	return wq
}

func (wq *TaskQuery) WhereCreatedBetween(start, end time.Time) *TaskQuery {
	wq.WhereBetween("CreatedAt", start, end)
	return wq
}

func (wq *TaskQuery) WhereStartedAfter(t time.Time) *TaskQuery {
	wq.WhereField("StartedAt", OpGreaterThan, &t)
	return wq
}

func (wq *TaskQuery) WhereStartedBefore(t time.Time) *TaskQuery {
	wq.WhereField("StartedAt", OpLessThan, &t)
	return wq
}

func (wq *TaskQuery) WhereStartedBetween(start, end time.Time) *TaskQuery {
	wq.WhereBetween("StartedAt", &start, &end)
	return wq
}

func (wq *TaskQuery) WherePending() *TaskQuery {
	return wq.WhereStatus(model.TaskStatusPending)
}

func (wq *TaskQuery) WhereRunning() *TaskQuery {
	return wq.WhereStatus(model.TaskStatusRunning)
}

func (wq *TaskQuery) WhereCompleted() *TaskQuery {
	statuses := []model.TaskStatus{
		model.TaskStatusSucceeded,
		model.TaskStatusFailed,
		model.TaskStatusCancelled,
	}
	return wq.WhereStatuses(statuses)
}

func (wq *TaskQuery) WhereActive() *TaskQuery {
	statuses := []model.TaskStatus{
		model.TaskStatusPending,
		model.TaskStatusRunning,
	}
	return wq.WhereStatuses(statuses)
}

func (wq *TaskQuery) WhereHasLabel(key string) *TaskQuery {
	wq.WhereFunc(func(w *model.Task) bool {
		_, exists := w.Labels[key]
		return exists
	})
	return wq
}

func (wq *TaskQuery) WhereLabelEquals(key, value string) *TaskQuery {
	wq.WhereFunc(func(w *model.Task) bool {
		v, exists := w.Labels[key]
		return exists && v == value
	})
	return wq
}

func (wq *TaskQuery) WhereLabelMatches(selector string) *TaskQuery {
	labelSelector, err := labels.Parse(selector)
	if err != nil {
		return wq
	}

	wq.WhereFunc(func(w *model.Task) bool {
		labelSet := labels.Set(w.Labels)
		return labelSelector.Matches(labelSet)
	})
	return wq
}

func (wq *TaskQuery) OrderByName(direction SortDirection) *TaskQuery {
	wq.OrderBy("Name", direction)
	return wq
}

func (wq *TaskQuery) OrderByCreateTime(direction SortDirection) *TaskQuery {
	wq.OrderBy("CreatedAt", direction)
	return wq
}

func (wq *TaskQuery) OrderByStartTime(direction SortDirection) *TaskQuery {
	wq.OrderBy("StartedAt", direction)
	return wq
}

func (wq *TaskQuery) OrderByPriority(direction SortDirection) *TaskQuery {
	wq.OrderBy("Priority", direction)
	return wq
}

func (wq *TaskQuery) OrderByStatus(direction SortDirection) *TaskQuery {
	wq.OrderBy("Status", direction)
	return wq
}

func (wq *TaskQuery) OrderByDuration(direction SortDirection) *TaskQuery {
	// wq.OrderByFunc(func(a, b *model.Task) bool {
	// 	aDuration := a.CalculateDuration()
	// 	bDuration := b.CalculateDuration()

	// 	if direction == SortAsc {
	// 		return aDuration < bDuration
	// 	}
	// 	return aDuration > bDuration
	// })
	return wq
}

func (wq *TaskQuery) Recent() *TaskQuery {
	return wq.OrderByCreateTime(SortDesc)
}

func (wq *TaskQuery) Oldest() *TaskQuery {
	return wq.OrderByCreateTime(SortAsc)
}

func (wq *TaskQuery) HighPriority() *TaskQuery {
	priorities := []model.TaskPriority{
		model.TaskPriorityHigh,
	}
	return wq.WherePriorities(priorities).OrderByPriority(SortDesc)
}

func (wq *TaskQuery) Clone() *TaskQuery {
	return &TaskQuery{
		QueryBuilder: wq.QueryBuilder.Clone(),
	}
}

func AllTasks() *TaskQuery {
	return NewTaskQuery()
}

func PendingTasks() *TaskQuery {
	return NewTaskQuery().WherePending()
}

func RunningTasks() *TaskQuery {
	return NewTaskQuery().WhereRunning()
}

func CompletedTasks() *TaskQuery {
	return NewTaskQuery().WhereCompleted()
}

func ActiveTasks() *TaskQuery {
	return NewTaskQuery().WhereActive()
}
