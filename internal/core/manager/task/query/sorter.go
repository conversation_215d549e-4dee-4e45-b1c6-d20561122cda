package query

import (
	"fmt"
	"reflect"
	"strings"
	"time"
)

type SortDirection string

const (
	SortAsc  SortDirection = "asc"
	SortDesc SortDirection = "desc"
)

type Sorter[T any] interface {
	Compare(a, b T) int
	String() string
}

type FieldSorter[T any] struct {
	Field     string
	Direction SortDirection
}

func NewFieldSorter[T any](field string, direction SortDirection) *FieldSorter[T] {
	return &FieldSorter[T]{
		Field:     field,
		Direction: direction,
	}
}

func (s *FieldSorter[T]) Compare(a, b T) int {
	aValue, err := GetFieldValue(a, s.Field)
	if err != nil {
		return 0
	}

	bValue, err := GetFieldValue(b, s.Field)
	if err != nil {
		return 0
	}

	result := s.compareValues(aValue, bValue)

	if s.Direction == SortDesc {
		result = -result
	}

	return result
}

func (s *FieldSorter[T]) String() string {
	return fmt.Sprintf("%s %s", s.<PERSON>, strings.ToUpper(string(s.Direction)))
}

func (s *FieldSorter[T]) compareValues(a, b interface{}) int {
	if a == nil && b == nil {
		return 0
	}
	if a == nil {
		return -1
	}
	if b == nil {
		return 1
	}

	if aTime, ok := a.(time.Time); ok {
		if bTime, ok := b.(time.Time); ok {
			if aTime.Before(bTime) {
				return -1
			} else if aTime.After(bTime) {
				return 1
			}
			return 0
		}
	}

	if aTimePtr, ok := a.(*time.Time); ok {
		if bTimePtr, ok := b.(*time.Time); ok {
			if aTimePtr == nil && bTimePtr == nil {
				return 0
			}
			if aTimePtr == nil {
				return -1
			}
			if bTimePtr == nil {
				return 1
			}
			if aTimePtr.Before(*bTimePtr) {
				return -1
			} else if aTimePtr.After(*bTimePtr) {
				return 1
			}
			return 0
		}
	}

	aVal := reflect.ValueOf(a)
	bVal := reflect.ValueOf(b)

	if aVal.Kind() >= reflect.Int && aVal.Kind() <= reflect.Float64 &&
		bVal.Kind() >= reflect.Int && bVal.Kind() <= reflect.Float64 {
		aFloat := aVal.Convert(reflect.TypeOf(float64(0))).Float()
		bFloat := bVal.Convert(reflect.TypeOf(float64(0))).Float()

		if aFloat < bFloat {
			return -1
		} else if aFloat > bFloat {
			return 1
		}
		return 0
	}

	if aStr, ok := a.(string); ok {
		if bStr, ok := b.(string); ok {
			return strings.Compare(aStr, bStr)
		}
	}

	if aBool, ok := a.(bool); ok {
		if bBool, ok := b.(bool); ok {
			if aBool == bBool {
				return 0
			}
			if aBool {
				return 1
			}
			return -1
		}
	}

	aStr := fmt.Sprintf("%v", a)
	bStr := fmt.Sprintf("%v", b)
	return strings.Compare(aStr, bStr)
}

type FuncSorter[T any] struct {
	Fn   func(a, b T) bool
	Name string
}

func NewFuncSorter[T any](fn func(a, b T) bool) *FuncSorter[T] {
	return &FuncSorter[T]{
		Fn:   fn,
		Name: "custom_function",
	}
}

func NewNamedFuncSorter[T any](name string, fn func(a, b T) bool) *FuncSorter[T] {
	return &FuncSorter[T]{
		Fn:   fn,
		Name: name,
	}
}

func (s *FuncSorter[T]) Compare(a, b T) int {
	if s.Fn(a, b) {
		return -1
	} else if s.Fn(b, a) {
		return 1
	}
	return 0
}

func (s *FuncSorter[T]) String() string {
	return fmt.Sprintf("FUNC(%s)", s.Name)
}

type MultiFieldSorter[T any] struct {
	Sorters []Sorter[T]
}

func NewMultiFieldSorter[T any](sorters ...Sorter[T]) *MultiFieldSorter[T] {
	return &MultiFieldSorter[T]{
		Sorters: sorters,
	}
}

func (s *MultiFieldSorter[T]) Compare(a, b T) int {
	for _, sorter := range s.Sorters {
		result := sorter.Compare(a, b)
		if result != 0 {
			return result
		}
	}
	return 0
}

func (s *MultiFieldSorter[T]) String() string {
	if len(s.Sorters) == 0 {
		return "EMPTY"
	}

	parts := make([]string, len(s.Sorters))
	for i, sorter := range s.Sorters {
		parts[i] = sorter.String()
	}

	return fmt.Sprintf("MULTI(%s)", strings.Join(parts, ", "))
}

type SortBuilder[T any] struct {
	sorters []Sorter[T]
}

func NewSortBuilder[T any]() *SortBuilder[T] {
	return &SortBuilder[T]{
		sorters: make([]Sorter[T], 0),
	}
}

func (sb *SortBuilder[T]) By(field string, direction SortDirection) *SortBuilder[T] {
	sorter := NewFieldSorter[T](field, direction)
	sb.sorters = append(sb.sorters, sorter)
	return sb
}

func (sb *SortBuilder[T]) ByFunc(fn func(a, b T) bool) *SortBuilder[T] {
	sorter := NewFuncSorter[T](fn)
	sb.sorters = append(sb.sorters, sorter)
	return sb
}

func (sb *SortBuilder[T]) Build() Sorter[T] {
	if len(sb.sorters) == 0 {
		return nil
	}
	if len(sb.sorters) == 1 {
		return sb.sorters[0]
	}
	return NewMultiFieldSorter[T](sb.sorters...)
}

func (sb *SortBuilder[T]) Reset() *SortBuilder[T] {
	sb.sorters = sb.sorters[:0]
	return sb
}

func SortByName[T any](direction SortDirection) *FieldSorter[T] {
	return NewFieldSorter[T]("Name", direction)
}

func SortByCreatedAt[T any](direction SortDirection) *FieldSorter[T] {
	return NewFieldSorter[T]("CreatedAt", direction)
}

func SortByUpdatedAt[T any](direction SortDirection) *FieldSorter[T] {
	return NewFieldSorter[T]("UpdateAt", direction)
}

func SortByPriority[T any](direction SortDirection) *FieldSorter[T] {
	return NewFieldSorter[T]("Priority", direction)
}

func SortByStatus[T any](direction SortDirection) *FieldSorter[T] {
	return NewFieldSorter[T]("Status", direction)
}

func SortByCreator[T any](direction SortDirection) *FieldSorter[T] {
	return NewFieldSorter[T]("Creator", direction)
}

func SortByQueuedDuration[T any](direction SortDirection) *FieldSorter[T] {
	return NewFieldSorter[T]("QueuedDuration", direction)
}

func SortByPodCount[T any](direction SortDirection) *FieldSorter[T] {
	return NewFieldSorter[T]("PodCount", direction)
}
