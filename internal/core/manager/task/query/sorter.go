package query

import (
	"fmt"
	"reflect"
	"strings"
	"time"
)

// SortDirection 排序方向
type SortDirection string

const (
	SortAsc  SortDirection = "asc"  // 升序
	SortDesc SortDirection = "desc" // 降序
)

// Sorter 排序器接口
type Sorter[T any] interface {
	Compare(a, b T) int // 返回 -1, 0, 1
	String() string
}

// FieldSorter 字段排序器
type FieldSorter[T any] struct {
	Field     string
	Direction SortDirection
}

// NewFieldSorter 创建字段排序器
func NewFieldSorter[T any](field string, direction SortDirection) *FieldSorter[T] {
	return &FieldSorter[T]{
		Field:     field,
		Direction: direction,
	}
}

// Compare 比较两个对象
func (s *FieldSorter[T]) Compare(a, b T) int {
	aValue, err := GetFieldValue(a, s.Field)
	if err != nil {
		return 0
	}
	
	bValue, err := GetFieldValue(b, s.<PERSON>)
	if err != nil {
		return 0
	}
	
	result := s.compareValues(aValue, bValue)
	
	if s.Direction == SortDesc {
		result = -result
	}
	
	return result
}

// String 字符串表示
func (s *FieldSorter[T]) String() string {
	return fmt.Sprintf("%s %s", s.Field, strings.ToUpper(string(s.Direction)))
}

// compareValues 比较值
func (s *FieldSorter[T]) compareValues(a, b interface{}) int {
	// 处理nil值
	if a == nil && b == nil {
		return 0
	}
	if a == nil {
		return -1
	}
	if b == nil {
		return 1
	}
	
	// 处理时间类型
	if aTime, ok := a.(time.Time); ok {
		if bTime, ok := b.(time.Time); ok {
			if aTime.Before(bTime) {
				return -1
			} else if aTime.After(bTime) {
				return 1
			}
			return 0
		}
	}
	
	// 处理指针时间类型
	if aTimePtr, ok := a.(*time.Time); ok {
		if bTimePtr, ok := b.(*time.Time); ok {
			if aTimePtr == nil && bTimePtr == nil {
				return 0
			}
			if aTimePtr == nil {
				return -1
			}
			if bTimePtr == nil {
				return 1
			}
			if aTimePtr.Before(*bTimePtr) {
				return -1
			} else if aTimePtr.After(*bTimePtr) {
				return 1
			}
			return 0
		}
	}
	
	// 处理数值类型
	aVal := reflect.ValueOf(a)
	bVal := reflect.ValueOf(b)
	
	if aVal.Kind() >= reflect.Int && aVal.Kind() <= reflect.Float64 &&
		bVal.Kind() >= reflect.Int && bVal.Kind() <= reflect.Float64 {
		aFloat := aVal.Convert(reflect.TypeOf(float64(0))).Float()
		bFloat := bVal.Convert(reflect.TypeOf(float64(0))).Float()
		
		if aFloat < bFloat {
			return -1
		} else if aFloat > bFloat {
			return 1
		}
		return 0
	}
	
	// 处理字符串类型
	if aStr, ok := a.(string); ok {
		if bStr, ok := b.(string); ok {
			return strings.Compare(aStr, bStr)
		}
	}
	
	// 处理布尔类型
	if aBool, ok := a.(bool); ok {
		if bBool, ok := b.(bool); ok {
			if aBool == bBool {
				return 0
			}
			if aBool {
				return 1
			}
			return -1
		}
	}
	
	// 默认使用字符串比较
	aStr := fmt.Sprintf("%v", a)
	bStr := fmt.Sprintf("%v", b)
	return strings.Compare(aStr, bStr)
}

// FuncSorter 函数排序器
type FuncSorter[T any] struct {
	Fn   func(a, b T) bool // 返回 a < b
	Name string
}

// NewFuncSorter 创建函数排序器
func NewFuncSorter[T any](fn func(a, b T) bool) *FuncSorter[T] {
	return &FuncSorter[T]{
		Fn:   fn,
		Name: "custom_function",
	}
}

// NewNamedFuncSorter 创建命名函数排序器
func NewNamedFuncSorter[T any](name string, fn func(a, b T) bool) *FuncSorter[T] {
	return &FuncSorter[T]{
		Fn:   fn,
		Name: name,
	}
}

// Compare 比较两个对象
func (s *FuncSorter[T]) Compare(a, b T) int {
	if s.Fn(a, b) {
		return -1
	} else if s.Fn(b, a) {
		return 1
	}
	return 0
}

// String 字符串表示
func (s *FuncSorter[T]) String() string {
	return fmt.Sprintf("FUNC(%s)", s.Name)
}

// MultiFieldSorter 多字段排序器
type MultiFieldSorter[T any] struct {
	Sorters []Sorter[T]
}

// NewMultiFieldSorter 创建多字段排序器
func NewMultiFieldSorter[T any](sorters ...Sorter[T]) *MultiFieldSorter[T] {
	return &MultiFieldSorter[T]{
		Sorters: sorters,
	}
}

// Compare 比较两个对象
func (s *MultiFieldSorter[T]) Compare(a, b T) int {
	for _, sorter := range s.Sorters {
		result := sorter.Compare(a, b)
		if result != 0 {
			return result
		}
	}
	return 0
}

// String 字符串表示
func (s *MultiFieldSorter[T]) String() string {
	if len(s.Sorters) == 0 {
		return "EMPTY"
	}
	
	parts := make([]string, len(s.Sorters))
	for i, sorter := range s.Sorters {
		parts[i] = sorter.String()
	}
	
	return fmt.Sprintf("MULTI(%s)", strings.Join(parts, ", "))
}

// SortBuilder 排序构建器
type SortBuilder[T any] struct {
	sorters []Sorter[T]
}

// NewSortBuilder 创建排序构建器
func NewSortBuilder[T any]() *SortBuilder[T] {
	return &SortBuilder[T]{
		sorters: make([]Sorter[T], 0),
	}
}

// By 添加字段排序
func (sb *SortBuilder[T]) By(field string, direction SortDirection) *SortBuilder[T] {
	sorter := NewFieldSorter[T](field, direction)
	sb.sorters = append(sb.sorters, sorter)
	return sb
}

// ByFunc 添加函数排序
func (sb *SortBuilder[T]) ByFunc(fn func(a, b T) bool) *SortBuilder[T] {
	sorter := NewFuncSorter[T](fn)
	sb.sorters = append(sb.sorters, sorter)
	return sb
}

// Build 构建排序器
func (sb *SortBuilder[T]) Build() Sorter[T] {
	if len(sb.sorters) == 0 {
		return nil
	}
	if len(sb.sorters) == 1 {
		return sb.sorters[0]
	}
	return NewMultiFieldSorter[T](sb.sorters...)
}

// Reset 重置构建器
func (sb *SortBuilder[T]) Reset() *SortBuilder[T] {
	sb.sorters = sb.sorters[:0]
	return sb
}

// 预定义的常用排序器

// SortByName 按名称排序
func SortByName[T any](direction SortDirection) *FieldSorter[T] {
	return NewFieldSorter[T]("Name", direction)
}

// SortByCreateTime 按创建时间排序
func SortByCreateTime[T any](direction SortDirection) *FieldSorter[T] {
	return NewFieldSorter[T]("CreateAt", direction)
}

// SortByUpdateTime 按更新时间排序
func SortByUpdateTime[T any](direction SortDirection) *FieldSorter[T] {
	return NewFieldSorter[T]("UpdateAt", direction)
}

// SortByPriority 按优先级排序
func SortByPriority[T any](direction SortDirection) *FieldSorter[T] {
	return NewFieldSorter[T]("Priority", direction)
}

// SortByStatus 按状态排序
func SortByStatus[T any](direction SortDirection) *FieldSorter[T] {
	return NewFieldSorter[T]("Status", direction)
}
