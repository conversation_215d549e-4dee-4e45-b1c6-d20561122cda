package query

import (
	"fmt"
	"math"
)

type Paginator[T any] struct {
	page       int32
	pageSize   int32
	total      int32
	totalPages int32
	items      []T
}

func NewPaginator[T any](page, pageSize int32) *Paginator[T] {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 1000 {
		pageSize = 1000
	}

	return &Paginator[T]{
		page:     page,
		pageSize: pageSize,
	}
}

func (p *Paginator[T]) Paginate(items []T) *PaginationResult[T] {
	p.total = int32(len(items))
	p.totalPages = int32(math.Ceil(float64(p.total) / float64(p.pageSize)))

	if p.totalPages == 0 {
		p.totalPages = 1
	}

	offset := (p.page - 1) * p.pageSize

	if offset >= p.total {
		p.items = []T{}
	} else {
		end := offset + p.pageSize
		if end > p.total {
			end = p.total
		}
		p.items = items[offset:end]
	}

	return &PaginationResult[T]{
		Items:      p.items,
		Page:       p.page,
		PageSize:   p.pageSize,
		Total:      p.total,
		TotalPages: p.totalPages,
		HasNext:    p.page < p.totalPages,
		HasPrev:    p.page > 1,
		NextPage:   p.getNextPage(),
		PrevPage:   p.getPrevPage(),
		StartIndex: offset + 1,
		EndIndex:   offset + int32(len(p.items)),
	}
}

func (p *Paginator[T]) getNextPage() int32 {
	if p.page < p.totalPages {
		return p.page + 1
	}
	return p.page
}

func (p *Paginator[T]) getPrevPage() int32 {
	if p.page > 1 {
		return p.page - 1
	}
	return p.page
}

type PaginationResult[T any] struct {
	Items      []T   `json:"items"`
	Page       int32 `json:"page"`
	PageSize   int32 `json:"page_size"`
	Total      int32 `json:"total"`
	TotalPages int32 `json:"total_pages"`
	HasNext    bool  `json:"has_next"`
	HasPrev    bool  `json:"has_prev"`
	NextPage   int32 `json:"next_page"`
	PrevPage   int32 `json:"prev_page"`
	StartIndex int32 `json:"start_index"`
	EndIndex   int32 `json:"end_index"`
}

func (pr *PaginationResult[T]) IsEmpty() bool {
	return len(pr.Items) == 0
}

func (pr *PaginationResult[T]) IsFirstPage() bool {
	return pr.Page == 1
}

func (pr *PaginationResult[T]) IsLastPage() bool {
	return pr.Page == pr.TotalPages
}

func (pr *PaginationResult[T]) GetPageInfo() string {
	if pr.Total == 0 {
		return "No items found"
	}
	return fmt.Sprintf("Showing %d-%d of %d items (Page %d of %d)",
		pr.StartIndex, pr.EndIndex, pr.Total, pr.Page, pr.TotalPages)
}

type CursorPaginator[T any] struct {
	pageSize  int32
	getCursor func(T) string
	compareFn func(T, T) bool
}

func NewCursorPaginator[T any](pageSize int32, getCursor func(T) string, compareFn func(T, T) bool) *CursorPaginator[T] {
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 1000 {
		pageSize = 1000
	}

	return &CursorPaginator[T]{
		pageSize:  pageSize,
		getCursor: getCursor,
		compareFn: compareFn,
	}
}

func (cp *CursorPaginator[T]) Paginate(items []T, cursor string, direction CursorDirection) *CursorResult[T] {
	var filteredItems []T

	if cursor == "" {
		filteredItems = items
	} else {
		for _, item := range items {
			itemCursor := cp.getCursor(item)

			if direction == CursorNext {
				if itemCursor > cursor {
					filteredItems = append(filteredItems, item)
				}
			} else {
				if itemCursor < cursor {
					filteredItems = append(filteredItems, item)
				}
			}
		}
	}

	hasMore := len(filteredItems) > int(cp.pageSize)
	if hasMore {
		filteredItems = filteredItems[:cp.pageSize]
	}

	var nextCursor, prevCursor string
	if len(filteredItems) > 0 {
		if direction == CursorNext || cursor == "" {
			nextCursor = cp.getCursor(filteredItems[len(filteredItems)-1])
			if len(filteredItems) > 1 {
				prevCursor = cp.getCursor(filteredItems[0])
			}
		} else {
			prevCursor = cp.getCursor(filteredItems[0])
			if len(filteredItems) > 1 {
				nextCursor = cp.getCursor(filteredItems[len(filteredItems)-1])
			}
		}
	}

	return &CursorResult[T]{
		Items:      filteredItems,
		NextCursor: nextCursor,
		PrevCursor: prevCursor,
		HasNext:    hasMore,
		HasPrev:    cursor != "",
		PageSize:   cp.pageSize,
		Count:      int32(len(filteredItems)),
	}
}

type CursorDirection string

const (
	CursorNext CursorDirection = "next"
	CursorPrev CursorDirection = "prev"
)

type CursorResult[T any] struct {
	Items      []T    `json:"items"`
	NextCursor string `json:"next_cursor"`
	PrevCursor string `json:"prev_cursor"`
	HasNext    bool   `json:"has_next"`
	HasPrev    bool   `json:"has_prev"`
	PageSize   int32  `json:"page_size"`
	Count      int32  `json:"count"`
}

func (cr *CursorResult[T]) IsEmpty() bool {
	return len(cr.Items) == 0
}

type OffsetPaginator[T any] struct {
	pageSize int32
	maxPages int32
}

func NewOffsetPaginator[T any](pageSize, maxPages int32) *OffsetPaginator[T] {
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 1000 {
		pageSize = 1000
	}
	if maxPages <= 0 {
		maxPages = 1000
	}

	return &OffsetPaginator[T]{
		pageSize: pageSize,
		maxPages: maxPages,
	}
}

func (op *OffsetPaginator[T]) Paginate(items []T, offset int32) *OffsetResult[T] {
	total := int32(len(items))

	if offset < 0 {
		offset = 0
	}
	if offset >= total {
		return &OffsetResult[T]{
			Items:    []T{},
			Offset:   offset,
			PageSize: op.pageSize,
			Total:    total,
			HasMore:  false,
		}
	}

	end := offset + op.pageSize
	if end > total {
		end = total
	}

	resultItems := items[offset:end]
	hasMore := end < total

	return &OffsetResult[T]{
		Items:    resultItems,
		Offset:   offset,
		PageSize: op.pageSize,
		Total:    total,
		HasMore:  hasMore,
		Count:    int32(len(resultItems)),
	}
}

type OffsetResult[T any] struct {
	Items    []T   `json:"items"`
	Offset   int32 `json:"offset"`
	PageSize int32 `json:"page_size"`
	Total    int32 `json:"total"`
	HasMore  bool  `json:"has_more"`
	Count    int32 `json:"count"`
}

func (or *OffsetResult[T]) IsEmpty() bool {
	return len(or.Items) == 0
}

func (or *OffsetResult[T]) GetNextOffset() int32 {
	if or.HasMore {
		return or.Offset + or.PageSize
	}
	return or.Offset
}

type PaginationConfig struct {
	DefaultPageSize int32 `json:"default_page_size"`
	MaxPageSize     int32 `json:"max_page_size"`
	MaxPages        int32 `json:"max_pages"`
	EnableCursor    bool  `json:"enable_cursor"`
	EnableOffset    bool  `json:"enable_offset"`
}

func NewPaginationConfig() *PaginationConfig {
	return &PaginationConfig{
		DefaultPageSize: 20,
		MaxPageSize:     1000,
		MaxPages:        1000,
		EnableCursor:    true,
		EnableOffset:    true,
	}
}

func (pc *PaginationConfig) ValidatePageSize(pageSize int32) int32 {
	if pageSize <= 0 {
		return pc.DefaultPageSize
	}
	if pageSize > pc.MaxPageSize {
		return pc.MaxPageSize
	}
	return pageSize
}

func (pc *PaginationConfig) ValidatePage(page int32) int32 {
	if page <= 0 {
		return 1
	}
	if page > pc.MaxPages {
		return pc.MaxPages
	}
	return page
}
