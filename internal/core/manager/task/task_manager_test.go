package task

import (
	"context"
	"fmt"
	"testing"
	"time"

	"transwarp.io/mlops/pipeline/internal/core/manager/task/cache"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
)

func TestGetComponents(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping long-running integration test in short mode")
	}

	ts := NewTaskManager()
	req := &model.ListTasksReq{}
	resp, err := ts.ListTasks(context.Background(), req)
	if err != nil {
		fmt.Println(err.Error())
	} else {
		fmt.Println(len(resp.Items))
	}
}

func TestTaskForest(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping long-running integration test in short mode")
	}

	tf := cache.NewTaskSnapshot()
	tf.Start(context.Background())
	time.Sleep(5 * time.Second)
	task := tf.GetTask("")
	if task == nil {
		t.<PERSON><PERSON>("task is nil")
	}
}
