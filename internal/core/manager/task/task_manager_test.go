package task

import (
	"context"
	"fmt"
	"testing"

	model "transwarp.io/mlops/pipeline/internal/core/model/task"
)

func TestGetComponents(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping long-running integration test in short mode")
	}

	ts := NewTaskManager()
	req := &model.ListTasksReq{}
	resp, err := ts.ListTasks(context.Background(), req)
	if err != nil {
		fmt.Println(err.Error())
	} else {
		fmt.Println(len(resp.Items))
	}
}
