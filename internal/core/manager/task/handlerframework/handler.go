package handler

import (
	"context"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	kubernetesclientschema "k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/tools/cache"

	"transwarp.io/mlops/pipeline/internal/core/consts"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
	"transwarp.io/mlops/pipeline/internal/util"

	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

type ResourceType struct {
	Group    string
	Version  string
	Resource string
	Kind     string
}

type Handler interface {
	Name() string
	ShouldHandle(obj runtime.Object) bool
	Handle(obj runtime.Object) (*model.Task, error)
	Start(ctx context.Context, handler Handler) error
	Stop() error
}

type BaseHandler struct {
	informer cache.SharedIndexInformer
	stopCh   chan struct{}

	onCreate func(obj runtime.Object, handler Handler)
	onUpdate func(oldObj, newObj runtime.Object, handler Handler)
	onDelete func(obj runtime.Object, handler Handler)
}

func NewBaseHandler(informer cache.SharedIndexInformer,
	onCreate func(obj runtime.Object, handler Handler),
	onUpdate func(oldObj, newObj runtime.Object, handler Handler),
	onDelete func(obj runtime.Object, handler Handler),
) *BaseHandler {
	return &BaseHandler{
		informer: informer,
		onCreate: onCreate,
		onUpdate: onUpdate,
		onDelete: onDelete,
		stopCh:   make(chan struct{}),
	}
}

func (h *BaseHandler) ShouldHandle(obj runtime.Object) bool {
	if !h.hasRequiredLabels(obj) {
		return false
	}

	if !h.isLLMOpsManagedNamespace(obj) {
		return false
	}

	return true
}

func (h *BaseHandler) hasRequiredLabels(obj runtime.Object) bool {
	metaObj, ok := obj.(metav1.Object)
	if !ok {
		return false
	}

	labels := metaObj.GetLabels()
	if labels == nil {
		return false
	}

	queueName, hasQueue := labels[consts.LabelKueueQueueNameKey]
	if !hasQueue || queueName == "" {
		return false
	}

	return true
}

func (h *BaseHandler) isLLMOpsManagedNamespace(obj runtime.Object) bool {
	metaObj, ok := obj.(metav1.Object)
	if !ok {
		return false
	}

	currentNamespace := util.GetCurrentNamespace()

	namespace := metaObj.GetNamespace()
	nsObj, err := client.MustGetK8sClient().GetNamespace(context.Background(), namespace)
	if err != nil || nsObj == nil {
		return false
	}
	if ns, ok := nsObj.Labels[consts.LabelLLMOpsManagedByKey]; ok && currentNamespace == ns {
		return true
	}
	return false
}

func (h *BaseHandler) Start(ctx context.Context, handler Handler) error {
	h.informer.AddEventHandler(cache.FilteringResourceEventHandler{
		FilterFunc: func(obj interface{}) bool {
			return handler.ShouldHandle(obj.(runtime.Object))
		},
		Handler: cache.ResourceEventHandlerFuncs{
			AddFunc: func(obj interface{}) {
				if runtimeObj, ok := obj.(runtime.Object); ok {
					if h.onCreate != nil {
						gvks, _, _ := kubernetesclientschema.Scheme.ObjectKinds(runtimeObj)
						runtimeObj.GetObjectKind().SetGroupVersionKind(gvks[0])
						h.onCreate(runtimeObj, handler)
					}
				}
			},
			UpdateFunc: func(oldObj, newObj interface{}) {
				if oldRuntimeObj, ok := oldObj.(runtime.Object); ok {
					if newRuntimeObj, ok := newObj.(runtime.Object); ok {
						if h.onUpdate != nil {
							h.onUpdate(oldRuntimeObj, newRuntimeObj, handler)
						}
					}
				}
			},
			DeleteFunc: func(obj interface{}) {
				if runtimeObj, ok := obj.(runtime.Object); ok {
					if h.onDelete != nil {
						h.onDelete(runtimeObj, handler)
					}
				}
			},
		},
	})

	go h.informer.Run(h.stopCh)

	if !cache.WaitForCacheSync(ctx.Done(), h.informer.HasSynced) {
		zlog.Warnf("Cache for %s synced failed.", handler.Name())
	}

	zlog.SugarInfof("Started %s handler", handler.Name())
	return nil
}

func (h *BaseHandler) Stop() error {
	close(h.stopCh)
	return nil
}

func HandleResourceCreate(obj runtime.Object, handler Handler) {
	task, err := handler.Handle(obj)
	if err != nil {
		zlog.SugarErrorf("Failed to handle task creation: %v", err)
	}
	zlog.SugarInfof("Succeeded to handle task creation, task id: %s, task_name: %s", task.ID, task.Name)
}

func HandleResourceUpdate(oldObj, newObj runtime.Object, handler Handler) {
	zlog.SugarDebugf("Resource updated", "object", newObj)
}

func HandleResourceDelete(obj runtime.Object, handler Handler) {
	zlog.SugarDebugf("Resource deleted", "object", obj)
}
