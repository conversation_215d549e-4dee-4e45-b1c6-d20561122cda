package handlerframework

import (
	"crypto/md5"
	"fmt"
	"strings"

	"k8s.io/apimachinery/pkg/api/meta"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/restmapper"

	"transwarp.io/mlops/pipeline/internal/core/consts"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
)

type TaskGenerator interface {
	GenerateTask(obj runtime.Object) (*model.Task, error)
	SaveTask(task *model.Task) error
}

type DefaultTaskGenerator struct {
	mapper meta.RESTMapper
}

func NewDefaultTaskGenerator() *DefaultTaskGenerator {
	dte := &DefaultTaskGenerator{}

	k8sclient := client.MustGetK8sClient()
	groupResources, err := restmapper.GetAPIGroupResources(k8sclient.Clientset.DiscoveryClient)
	if err != nil {
		panic(err)
	}
	dte.mapper = restmapper.NewDiscoveryRESTMapper(groupResources)

	return dte
}

func (dte *DefaultTaskGenerator) generateTaskID(obj runtime.Object) string {
	metaObj, ok := obj.(metav1.Object)
	if !ok {
		return ""
	}

	source := fmt.Sprintf("%s/%s/%s",
		metaObj.GetNamespace(),
		metaObj.GetName(),
		metaObj.GetUID())

	hash := md5.Sum([]byte(source))
	return fmt.Sprintf("task-%s-%x", strings.ToLower(obj.GetObjectKind().GroupVersionKind().Kind), hash)[:36]
}

func (dte *DefaultTaskGenerator) GenerateTask(obj runtime.Object) (*model.Task, error) {
	metaObj, ok := obj.(metav1.Object)
	if !ok {
		return nil, fmt.Errorf("object does not implement metav1.Object")
	}

	labels := metaObj.GetLabels()
	if labels == nil {
		labels = make(map[string]string)
	}

	gvk := obj.GetObjectKind().GroupVersionKind()
	m, err := dte.mapper.RESTMapping(gvk.GroupKind(), gvk.Version)
	if err != nil {
		return nil, fmt.Errorf("failed to get REST mapping: %w", err)
	}
	gvr := m.Resource

	return &model.Task{
		ID:          dte.generateTaskID(obj),
		Name:        metaObj.GetName(),
		Type:        model.TaskTypeCustom,
		Priority:    model.TaskPriorityMedium,
		Description: metaObj.GetAnnotations()[consts.AnnotationTaskDescriptionKey],
		Creator:     metaObj.GetAnnotations()[consts.AnnotationTaskCreatorKey],
		CreatedAt:   metaObj.GetCreationTimestamp().Time,
		UpdatedAt:   metaObj.GetCreationTimestamp().Time,
		Status:      model.TaskStatusPending,
		StartedAt:   nil,
		EndedAt:     nil,

		TenantID:    metaObj.GetAnnotations()[consts.AnnotationTaskTenantIDKey],
		TenantName:  metaObj.GetAnnotations()[consts.AnnotationTaskTenantNameKey],
		ProjectName: metaObj.GetAnnotations()[consts.AnnotationTaskProjectNameKey],
		ProjectID:   metaObj.GetAnnotations()[consts.AnnotationTaskProjectIDKey],

		// TODO
		Resources:    &model.Resources{},
		RunningNodes: make([]*model.Node, 0),
		// TODO
		Extra: make(map[string]string),
		// Extra:        metaObj.GetAnnotations()[consts.AnnotationTaskExtraKey],

		SubTasks:  make([]*model.SubTask, 0),
		QueueName: labels[consts.LabelKueueQueueNameKey],

		GVK: gvk,
		GVR: gvr,

		Namespace:   metaObj.GetNamespace(),
		Labels:      labels,
		Annotations: metaObj.GetAnnotations(),
	}, nil
}

func (dtg *DefaultTaskGenerator) SaveTask(task *model.Task) error {
	// TODO save to db
	return nil
}
