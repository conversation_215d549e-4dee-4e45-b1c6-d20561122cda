package cache

import (
	"context"
	"fmt"
	"sync"
	"time"

	corev1 "k8s.io/api/core/v1"
	kueuev1beta1 "sigs.k8s.io/kueue/apis/kueue/v1beta1"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

const (
	DefaultQueueCapacity = 10000
)

const (
	initialRetryDelay = 5 * time.Second
	maxRetryDelay     = 5 * time.Minute
	maxRetries        = 5
)

type Event interface {
	name() string
	handle(tf *TaskForest) error
	getID() string
	// Event
	// triggeredResourceID
	// getResourceID() string
}

type Retryable interface {
	GetRetries() int
	IncrementRetries()
	GetLastTryTime() time.Time
	SetLastTryTime(t time.Time)
	ShouldRetry() bool
	GetNextRetryDelay() time.Duration
}

type BaseEvent struct {
	CreatedAt *time.Time

	Retries     int
	LastTryTime time.Time
}

func (be *BaseEvent) GetRetries() int {
	return be.Retries
}

func (be *BaseEvent) IncrementRetries() {
	be.Retries++
}

func (be *BaseEvent) GetLastTryTime() time.Time {
	return be.LastTryTime
}

func (be *BaseEvent) SetLastTryTime(t time.Time) {
	be.LastTryTime = t
}

func (be *BaseEvent) ShouldRetry() bool {
	return be.Retries < maxRetries
}

func (be *BaseEvent) GetNextRetryDelay() time.Duration {
	if be.Retries <= 0 {
		return initialRetryDelay
	}
	delay := initialRetryDelay * time.Duration(1<<uint(be.Retries-1))
	if delay > maxRetryDelay {
		delay = maxRetryDelay
	}
	return delay
}

type podAddEvent struct {
	BaseEvent
	Pod *corev1.Pod
}

func (e *podAddEvent) name() string {
	return "podAddEvent"
}
func (e *podAddEvent) handle(tf *TaskForest) error {
	if err, ok := tf.AddKubernetesPod(e.Pod, e.CreatedAt); err != nil {
		return err
	} else if !ok {
		return fmt.Errorf("Failed to add pod %s to task forest's tree, maybe not found task tree, retry later.", e.getID())
	}
	return nil
}
func (e *podAddEvent) getID() string {
	if e.Pod == nil {
		return "unknown-pod-id"
	}
	return fmt.Sprintf("%s/%s", e.Pod.Namespace, e.Pod.Name)
}

type podUpdateEvent struct {
	BaseEvent
	OldPod *corev1.Pod
	NewPod *corev1.Pod
}

func (e *podUpdateEvent) name() string {
	return "podUpdateEvent"
}
func (e *podUpdateEvent) handle(tf *TaskForest) error {
	if err, ok := tf.UpdateKubernetesPod(e.OldPod, e.NewPod, e.CreatedAt); err != nil {
		return err
	} else if !ok {
		return fmt.Errorf("Failed to update pod %s to task forest's tree, maybe not found task tree, retry later.", e.getID())
	}
	return nil
}
func (e *podUpdateEvent) getID() string {
	if e.OldPod == nil {
		return "unknown-pod-id"
	}
	return fmt.Sprintf("%s/%s", e.OldPod.Namespace, e.OldPod.Name)
}

type podDeleteEvent struct {
	BaseEvent
	Pod *corev1.Pod
}

func (e *podDeleteEvent) name() string {
	return "podDeleteEvent"
}
func (e *podDeleteEvent) handle(tf *TaskForest) error {
	if err, ok := tf.DeleteKubernetesPod(e.Pod, e.CreatedAt); err != nil {
		return err
	} else if !ok {
		return fmt.Errorf("Failed to delete pod %s to task forest's tree, maybe not found task tree, retry later.", e.getID())
	}
	return nil
}
func (e *podDeleteEvent) getID() string {
	if e.Pod == nil {
		return "unknown-pod-id"
	}
	return fmt.Sprintf("%s/%s", e.Pod.Namespace, e.Pod.Name)
}

type kueueWorkloadAddEvent struct {
	BaseEvent
	Workload *kueuev1beta1.Workload
}

func (e *kueueWorkloadAddEvent) name() string {
	return "workloadAddEvent"
}

func (e *kueueWorkloadAddEvent) handle(tf *TaskForest) error {
	// if err not nil, requeue
	if err, _ := tf.AddKueueWorkload(e.Workload, e.CreatedAt); err != nil {
		return err
	}
	return nil
}

func (e *kueueWorkloadAddEvent) getID() string {
	if e.Workload == nil {
		return "unknown-workload-id"
	}
	return fmt.Sprintf("%s/%s", e.Workload.Namespace, e.Workload.Name)
}

type kueueWorkloadUpdateEvent struct {
	BaseEvent
	OldWorkload *kueuev1beta1.Workload
	NewWorkload *kueuev1beta1.Workload
}

func (e *kueueWorkloadUpdateEvent) name() string {
	return "workloadUpdateEvent"
}

func (e *kueueWorkloadUpdateEvent) handle(tf *TaskForest) error {
	if err, ok := tf.UpdateKueueWorkload(e.OldWorkload, e.NewWorkload, e.CreatedAt); err != nil {
		return err
	} else if !ok {
		return fmt.Errorf("Failed to update workload %s to task forest's tree, retry later.", e.getID())
	}
	return nil
}
func (e *kueueWorkloadUpdateEvent) getID() string {
	if e.OldWorkload == nil {
		return "unknown-workload-id"
	}
	return fmt.Sprintf("%s/%s", e.OldWorkload.Namespace, e.OldWorkload.Name)
}

type kueueWorkloadDeleteEvent struct {
	BaseEvent
	Workload *kueuev1beta1.Workload
}

func (e *kueueWorkloadDeleteEvent) name() string {
	return "workloadDeleteEvent"
}
func (e *kueueWorkloadDeleteEvent) handle(tf *TaskForest) error {
	if err, ok := tf.DeleteKueueWorkload(e.Workload, e.CreatedAt); err != nil {
		return err
	} else if !ok {
		return fmt.Errorf("Failed to delete workload %s to task forest's tree, retry later.", e.getID())
	}
	return nil
}
func (e *kueueWorkloadDeleteEvent) getID() string {
	if e.Workload == nil {
		return "unknown-workload-id"
	}
	return fmt.Sprintf("%s/%s", e.Workload.Namespace, e.Workload.Name)
}

type EventQueue struct {
	queue chan interface{}
}

func NewEventQueue(cap int) *EventQueue {
	if cap < 100 {
		cap = DefaultQueueCapacity
	}
	return &EventQueue{
		queue: make(chan interface{}, cap),
	}
}

func (eq *EventQueue) Enqueue(event interface{}) {
	eq.queue <- event
}

type EventHandler struct {
	workerCount int
	workerWg    sync.WaitGroup

	stopCh chan struct{}

	eventQueue *EventQueue

	taskForest *TaskForest
}

func NewEventHandler(workerCount int, queue *EventQueue, taskForest *TaskForest) *EventHandler {
	return &EventHandler{
		workerCount: workerCount,
		eventQueue:  queue,

		taskForest: taskForest,
	}
}

func (eh *EventHandler) StartEventLoop(ctx context.Context) error {
	for i := 0; i < eh.workerCount; i++ {
		eh.workerWg.Add(1)
		go func(workerID int) {
			defer eh.workerWg.Done()
			zlog.SugarInfof("Event worker %d started.", workerID)
			for {
				select {
				case e := <-eh.eventQueue.queue:
					event := e.(Event)
					zlog.SugarInfof("Event worker %d started to handle %s event id: %s.", workerID, event.name(), event.getID())
					if err := event.handle(eh.taskForest); err != nil {
						eh.handleFailure(event)
					}
				case <-eh.stopCh:
					zlog.SugarInfof("Event worker %d received stop signal. Exiting.", workerID)
					return
				case <-ctx.Done():
					zlog.SugarInfof("Event worker %d context cancelled. Exiting.", workerID)
					return
				}
			}
		}(i)
	}
	return nil
}

func (eh *EventHandler) Stop() error {
	close(eh.stopCh)
	return nil
}

func (eh *EventHandler) handleFailure(event Event) {
	if retryableEvent, ok := event.(Retryable); ok && retryableEvent.ShouldRetry() {
		retryableEvent.IncrementRetries()
		delay := retryableEvent.GetNextRetryDelay()
		zlog.SugarWarnf("Event %s (ID: %s) will retry in %v (retries: %d)", event.name(), event.getID(), delay, retryableEvent.GetRetries())
		go func() {
			select {
			case <-time.After(delay):
				eh.eventQueue.Enqueue(event)
			case <-eh.stopCh:
				zlog.SugarWarnf("Discarding retry for event %s (ID: %s) due to shutdown.", event.name(), event.getID())
			}
		}()
	} else {
		zlog.SugarErrorf("Event %s (ID: %s) failed permanently after max retries or not retryable.", event.name(), event.getID())
		// 报警或进入死信队列
	}
}
