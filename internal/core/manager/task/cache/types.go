package cache

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	kueuev1beta1 "sigs.k8s.io/kueue/apis/kueue/v1beta1"
)

type TaskSnapshot struct {
	tasks         []*TaskTree
	dynamicClient dynamic.Interface
}

type TaskTree struct {
	root *TaskRoot
}

type TaskRoot struct {
	gvr       schema.GroupVersionResource
	gvk       schema.GroupVersionKind
	refs      []metav1.Object
	workloads []*TaskWorkloadNode
}

type TaskWorkloadNode struct {
	gvr      schema.GroupVersionResource
	ref      *kueuev1beta1.Workload
	children []*TaskNode
}

// deployment -> replicaset -> pod
// statefulset -> replicaset -> pod
// job -> pod
// lws -> statefulset -> replicaset -> pod
// ...
type TaskNode struct {
	gvr      schema.GroupVersionResource
	children []*TaskNode
	ref      metav1.Object

	isPod bool
}
