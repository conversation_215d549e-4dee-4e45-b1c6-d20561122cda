package cache

import (
	"strings"

	corev1 "k8s.io/api/core/v1"
	modeltask "transwarp.io/mlops/pipeline/internal/core/model/task"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

func (tn *TaskNode) replaceOldTaskNode(oldNode, newNode *TaskNode) bool {
	if tn.children == nil || len(tn.children) == 0 {
		return false
	}

	for i, node := range tn.children {
		if node.ref.GetUID() == oldNode.ref.GetUID() {
			if newNode.lastTransitionTime.Before(*node.lastTransitionTime) {
				zlog.SugarWarnf("Skip update pod %s because new transition time %v is before old transition time %v",
					oldNode.ref.GetName(), newNode.lastTransitionTime, node.lastTransitionTime)
				return true
			}
			tn.children[i] = newNode
			return true
		}
		if node.replaceOldTaskNode(oldNode, newNode) {
			return true
		}
	}
	return false
}

func (tn *TaskNode) deleteTaskNode(node *TaskNode) bool {
	if tn.children == nil || len(tn.children) == 0 {
		return false
	}

	for i, child := range tn.children {
		if child.ref.GetUID() == node.ref.GetUID() {
			tn.children = append(tn.children[:i], tn.children[i+1:]...)
			return true
		}
		if child.deleteTaskNode(node) {
			return true
		}
	}
	return false
}

func (tn *TaskNode) hasLeafPod() bool {
	if tn.isPod {
		return true
	}
	if tn.children == nil || len(tn.children) == 0 {
		return false
	}
	for _, child := range tn.children {
		if child.hasLeafPod() {
			return true
		}
	}
	return false
}

func (tn *TaskNode) getAllPods() ([]*modeltask.Pod, error) {
	pods := make([]*modeltask.Pod, 0)
	if tn.isPod {
		p := tn.ref.(*corev1.Pod)
		pod := &modeltask.Pod{
			Name:      p.GetName(),
			Namespace: p.GetNamespace(),
			NodeName:  p.Spec.NodeName,
			Phase:     p.Status.Phase,
			HostIP:    p.Status.HostIP,
		}
		if p.Status.StartTime != nil {
			pod.StartedAt = &p.Status.StartTime.Time
		}

		containers := make([]*modeltask.Container, 0)
		for _, c := range p.Spec.Containers {
			container := &modeltask.Container{
				Name:  c.Name,
				Image: c.Image,
			}
			container.Resources = tn.getContainerResources(c)
			containers = append(containers, container)
		}
		pod.Containers = containers

		totalResources := &modeltask.Resources{
			Requests: modeltask.ResourceSpec{},
			Limits:   modeltask.ResourceSpec{},
		}
		for _, c := range pod.Containers {
			totalResources.Add(c.Resources)
		}

		pod.Resources = totalResources

		pods = append(pods, pod)
	} else if tn.children != nil && len(tn.children) > 0 {
		for _, node := range tn.children {
			childPods, err := node.getAllPods()
			if err != nil {
				return nil, err
			}
			pods = append(pods, childPods...)
		}
	}
	return pods, nil
}

func (tn *TaskNode) getContainerResources(container corev1.Container) *modeltask.Resources {
	fixName := func(name string) string {
		n := strings.TrimSuffix(name, ".com")
		return strings.TrimSuffix(n, ".ai")
	}
	requestsGPU := make(map[string]*modeltask.GPUResource)
	for key, quantity := range container.Resources.Requests {
		items := strings.Split(string(key), "/")
		if len(items) < 2 {
			continue
		}
		name := fixName(items[0])
		item := items[1]
		if _, ok := requestsGPU[name]; !ok {
			requestsGPU[name] = &modeltask.GPUResource{
				Name:   name,
				Count:  0,
				Cores:  0,
				Memory: "0Mi",
			}
		}
		if strings.Contains(item, "mem") {
			requestsGPU[name].Memory = quantity.String()
		} else if strings.Contains(item, "core") {
			requestsGPU[name].Cores = quantity.Value()
		} else {
			requestsGPU[name].Count = float64(quantity.Value())
		}
	}

	limitsGPU := make(map[string]*modeltask.GPUResource)
	for key, quantity := range container.Resources.Requests {
		items := strings.Split(string(key), "/")
		if len(items) < 2 {
			continue
		}
		name := fixName(items[0])
		item := items[1]
		if _, ok := limitsGPU[name]; !ok {
			limitsGPU[name] = &modeltask.GPUResource{
				Name:   name,
				Count:  0,
				Cores:  0,
				Memory: "0Mi",
			}
		}
		if strings.Contains(item, "mem") {
			limitsGPU[name].Memory = quantity.String()
		} else if strings.Contains(item, "core") {
			limitsGPU[name].Cores = quantity.Value()
		} else {
			limitsGPU[name].Count = float64(quantity.Value())
		}
	}

	reqGPUs := make([]*modeltask.GPUResource, 0)
	for _, gpu := range requestsGPU {
		if gpu.Cores != 0 {
			gpu.Count = (gpu.Count * float64(gpu.Cores)) / 100.0
		}
		reqGPUs = append(reqGPUs, gpu)
	}
	limitGPUs := make([]*modeltask.GPUResource, 0)
	for _, gpu := range limitsGPU {
		if gpu.Cores != 0 {
			gpu.Count = (gpu.Count * float64(gpu.Cores)) / 100.0
		}
		limitGPUs = append(limitGPUs, gpu)
	}

	return &modeltask.Resources{
		Requests: modeltask.ResourceSpec{
			CPU:    container.Resources.Requests.Cpu().String(),
			Memory: container.Resources.Requests.Memory().String(),
			GPU:    reqGPUs,
		},
		Limits: modeltask.ResourceSpec{
			CPU:    container.Resources.Limits.Cpu().String(),
			Memory: container.Resources.Limits.Memory().String(),
			GPU:    limitGPUs,
		},
		Used: &modeltask.ResourceSpec{
			CPU:    "0",
			Memory: "0",
			GPU:    make([]*modeltask.GPUResource, 0),
		},
	}
}
