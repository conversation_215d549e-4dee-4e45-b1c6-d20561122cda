package cache

import (
	"context"
	"fmt"
	"strings"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/tools/cache"
	kueuev1beta1 "sigs.k8s.io/kueue/apis/kueue/v1beta1"

	"transwarp.io/mlops/pipeline/internal/core/consts"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
	"transwarp.io/mlops/pipeline/internal/util"

	modeltask "transwarp.io/mlops/pipeline/internal/core/model/task"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

var (
	workloadGVR = kueuev1beta1.GroupVersion.WithResource("workloads")

	podGVK = corev1.SchemeGroupVersion.WithKind("Pod")
)

func NewTaskSnapshot() *TaskSnapshot {
	config, err := util.GetKubeConfig()
	if err != nil {
		panic(err)
	}
	dynamicClient, err := dynamic.NewForConfig(config)
	if err != nil {
		panic(err)
	}
	return &TaskSnapshot{
		dynamicClient: dynamicClient,
	}
}

func (ts *TaskSnapshot) Start(ctx context.Context) error {
	client.MustGetKueueClient().AddWorkloadEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc: func(obj interface{}) {
			kwl := obj.(*kueuev1beta1.Workload)
			zlog.SugarInfof("Workload added: %s/%s", kwl.Namespace, kwl.Name)
			ts.HandleWorkloadAdd(obj.(*kueuev1beta1.Workload))
		},
		UpdateFunc: func(oldObj, newObj interface{}) {
			// tf.HandleWorkloadUpdate(oldObj.(*kueuev1beta1.Workload), newObj.(*kueuev1beta1.Workload))
		},
		DeleteFunc: func(obj interface{}) {
			ts.HandleWorkloadDelete(obj.(*kueuev1beta1.Workload))
		},
	})

	client.MustGetK8sClient().AddPodWatchHandler(cache.FilteringResourceEventHandler{
		FilterFunc: func(obj interface{}) bool {
			metaObj, ok := obj.(metav1.Object)
			if !ok {
				return false
			}

			labels := metaObj.GetLabels()
			if consts.HasKueueManagedLabel(labels) || consts.HasKueuePodsetLabel(labels) {
				zlog.SugarInfof("Pod added: %s/%s", metaObj.GetNamespace(), metaObj.GetName())
				return true
			}
			return false
		},
		Handler: cache.ResourceEventHandlerFuncs{
			AddFunc: func(obj interface{}) {
				if runtimeObj, ok := obj.(runtime.Object); ok {
					runtimeObj.GetObjectKind().SetGroupVersionKind(podGVK)
					ts.HandlePodAdd(runtimeObj.(*corev1.Pod))
				}
			},
			UpdateFunc: func(oldObj, newObj interface{}) {
				// tf.HandlePodUpdate(oldObj.(*corev1.Pod), newObj.(*corev1.Pod))
			},
			DeleteFunc: func(obj interface{}) {
				if runtimeObj, ok := obj.(runtime.Object); ok {
					runtimeObj.GetObjectKind().SetGroupVersionKind(podGVK)
					ts.HandlePodDelete(obj.(*corev1.Pod))
				}
			},
		},
	})

	zlog.SugarInfof("Init task snapshot successfully.")
	return nil
}

func (ts *TaskSnapshot) GetAllTasks() ([]*modeltask.Task, error) {
	tasks := make([]*modeltask.Task, 0)
	for _, task := range ts.tasks {
		zlog.SugarInfof("To task: %s", task.root.refs[0].GetName())
		tasks = append(tasks, task.ToTask())
	}
	return tasks, nil
}

func (ts *TaskSnapshot) GetTask(id string) *modeltask.Task {
	for _, task := range ts.tasks {
		for _, ref := range task.root.refs {
			if string(ref.GetUID()) == id {
				return task.ToTask()
			}
		}
	}
	return nil
}

func (ts *TaskSnapshot) HandleWorkloadAdd(kw *kueuev1beta1.Workload) {
	// find task tree
	// if found, add workload to task tree
	// if not found, new task tree

	// first build a  new tree, and then merge tree into
	twl, err := ts.buildTaskWorkloadNodeFromKueueWorkload(kw)
	if err != nil {
		return
	}

	ts.upsertTaskWorkloadNode(twl)
}
func (ts *TaskSnapshot) HandleWorkloadDelete(kw *kueuev1beta1.Workload) {
	// find task tree
	// if found, remove workload from task tree
	// if not found, write error msg?
	for _, tree := range ts.tasks {
		for i, twl := range tree.root.workloads {
			if twl.ref.GetUID() == kw.GetUID() {
				tree.root.workloads = append(tree.root.workloads[:i], tree.root.workloads[i+1:]...)
			}
		}
	}
}
func (ts *TaskSnapshot) HandlePodAdd(pod *corev1.Pod) {
	// find task tree
	// if found, add pod to task tree
	// if not found, new task tree ?
	treeNode, err := ts.buildTaskNodeFromPod(pod)
	if err != nil {
		return
	}

	ts.upsertTaskNode(treeNode)

	zlog.SugarInfof("Handle pod add: %s/%s", pod.GetNamespace(), pod.GetName())
}
func (ts *TaskSnapshot) HandlePodDelete(pod *corev1.Pod) {
	// find task tree
	// if found, remove pod from task tree
	// if not found, write error msg?
	for _, tree := range ts.tasks {
		for _, twl := range tree.root.workloads {
			for i, node := range twl.children {
				if node.ref.GetUID() == pod.GetUID() {
					twl.children = append(twl.children[:i], twl.children[i+1:]...)
					return
				}
				if ts.deletePodFromSubTaskTree(node, pod) {
					return
				}
			}
		}
	}
}

func (ts *TaskSnapshot) upsertTaskWorkloadNode(twl *TaskWorkloadNode) {
	allAncestors, err := ts.findAllAncestor(twl.ref)
	if err != nil {
		zlog.SugarErrorf("Failed to find all ancestors: %v", err)
		return
	}

	for _, ancestor := range allAncestors {
		if targetTree, ok := ts.findTree(ancestor.GetUID()); ok {
			ts.mergeTaskWorload(targetTree, twl)
			return
		}
	}
	// new tree
	newTree := &TaskTree{
		root: &TaskRoot{
			refs:      allAncestors,
			workloads: []*TaskWorkloadNode{twl},
		},
	}
	ts.tasks = append(ts.tasks, newTree)
}

func (ts *TaskSnapshot) upsertTaskNode(node *TaskNode) {
	if targetTree, ok := ts.findTree(node.ref.GetUID()); ok {
		ts.mergetTaskNode(targetTree, node)
		return
	}
}

func (ts *TaskSnapshot) findTree(uid types.UID) (*TaskTree, bool) {
	// if not found return nil
	for _, task := range ts.tasks {
		for _, ref := range task.root.refs {
			if ref.GetUID() == uid {
				return task, true
			}
		}
	}
	return nil, false
}

func (ts *TaskSnapshot) mergeTaskWorload(targetTree *TaskTree, twl *TaskWorkloadNode) {
	// merge tree into targetTree
	targetTree.root.workloads = append(targetTree.root.workloads, twl)
}

func (ts *TaskSnapshot) mergetTaskNode(targetTree *TaskTree, node *TaskNode) {
	// merge tree into targetTree workload
	for _, twl := range targetTree.root.workloads {
		if ts.belongToWorkload(twl.ref, node) {
			twl.children = append(twl.children, node)
			return
		}
	}
}

func (tf *TaskSnapshot) belongToWorkload(kwl *kueuev1beta1.Workload, node *TaskNode) bool {
	for _, ownerRef := range kwl.OwnerReferences {
		if node.ref.GetUID() == ownerRef.UID {
			return true
		}
		if node.children != nil && len(node.children) > 0 {
			for _, child := range node.children {
				if tf.belongToWorkload(kwl, child) {
					return true
				}
			}
		}
	}
	return false
}

func (tf *TaskSnapshot) deletePodFromSubTaskTree(subTree *TaskNode, pod *corev1.Pod) bool {
	for i, child := range subTree.children {
		if child.ref.GetUID() == pod.GetUID() {
			subTree.children = append(subTree.children[:i], subTree.children[i+1:]...)
			return true
		}
		if tf.deletePodFromSubTaskTree(child, pod) {
			return true
		}
	}
	return false
}

func (tf *TaskSnapshot) existsInSubTree(subTree *TaskNode, target *TaskNode) bool {
	if subTree.ref.GetUID() == target.ref.GetUID() {
		return true
	}
	for _, child := range subTree.children {
		if tf.existsInSubTree(child, target) {
			return true
		}
	}
	return false
}

func (ts *TaskSnapshot) buildTaskWorkloadNodeFromKueueWorkload(obj metav1.Object) (*TaskWorkloadNode, error) {
	twl := &TaskWorkloadNode{
		gvr:      workloadGVR,
		ref:      obj.(*kueuev1beta1.Workload),
		children: make([]*TaskNode, 0),
	}

	return twl, nil
}

func (ts *TaskSnapshot) buildTaskNodeFromPod(obj metav1.Object) (*TaskNode, error) {
	// find ancestor
	// build task tree
	gvr, _ := ts.inferGVRFromObject(obj)
	node := &TaskNode{
		gvr:      gvr,
		children: nil,
		ref:      obj,
		isPod:    true,
	}

	namespace := obj.GetNamespace()
	for {
		ownerRefs := node.ref.GetOwnerReferences()
		if len(ownerRefs) == 0 {
			break
		}

		ownerRef := ownerRefs[0]
		name := ownerRef.Name
		kind := ownerRef.Kind

		parentGVR, err := ts.inferGVRFromOwnerRef(ownerRef)
		if err != nil {
			zlog.SugarErrorf("Failed to infer GVR from owner reference: %v", err)
			return nil, err
		}

		parentObj, err := ts.getObjectFromOwnerRef(namespace, ownerRef)
		if err != nil {
			return nil, fmt.Errorf("failed to get %s %s/%s: %w", kind, namespace, name, err)
		}

		parentNode := &TaskNode{
			gvr:      parentGVR,
			ref:      parentObj,
			children: []*TaskNode{node},
		}
		node = parentNode
	}

	return node, nil
}

func (ts *TaskSnapshot) getObjectFromOwnerRef(namespace string, ownerRef metav1.OwnerReference) (metav1.Object, error) {
	name := ownerRef.Name
	kind := ownerRef.Kind

	gvr, err := ts.inferGVRFromOwnerRef(ownerRef)
	if err != nil {
		zlog.SugarErrorf("Failed to infer GVR from owner reference: %v", err)
		return nil, err
	}

	obj, err := ts.dynamicClient.Resource(gvr).Namespace(namespace).Get(context.Background(), name, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get %s %s/%s: %w", kind, namespace, name, err)
	}

	return obj, nil
}

func (ts *TaskSnapshot) findAllAncestor(obj metav1.Object) ([]metav1.Object, error) {
	ancestors := make([]metav1.Object, 0)
	namespace := obj.GetNamespace()

	for _, ref := range obj.GetOwnerReferences() {
		ancestor, err := ts.findAncestorFromOwnerRef(namespace, ref)
		if err != nil {
			return nil, err
		}
		ancestors = append(ancestors, ancestor)
	}

	return ancestors, nil
}

func (ts *TaskSnapshot) findAncestorFromOwnerRef(namespace string, ownerRef metav1.OwnerReference) (metav1.Object, error) {
	var ancestor metav1.Object
	for {
		name := ownerRef.Name
		kind := ownerRef.Kind

		gvr, err := ts.inferGVRFromOwnerRef(ownerRef)
		if err != nil {
			zlog.SugarErrorf("Failed to infer GVR from owner reference: %v", err)
			return nil, err
		}

		obj, err := ts.dynamicClient.Resource(gvr).Namespace(namespace).Get(context.Background(), name, metav1.GetOptions{})
		if err != nil {
			return nil, fmt.Errorf("failed to get %s %s/%s: %w", kind, namespace, name, err)
		}
		ancestor = obj

		if len(obj.GetOwnerReferences()) == 0 {
			break
		}
		ownerRef = obj.GetOwnerReferences()[0]
	}

	return ancestor, nil
}

func (ts *TaskSnapshot) findAnyAncestor(obj metav1.Object) (metav1.Object, error) {
	ancestor := obj
	namespace := obj.GetNamespace()
	for {
		ownerRefs := ancestor.GetOwnerReferences()
		if len(ownerRefs) == 0 {
			break
		}

		ownerRef := ownerRefs[0]
		name := ownerRef.Name
		kind := ownerRef.Kind

		gvr, err := ts.inferGVRFromOwnerRef(ownerRef)
		if err != nil {
			zlog.SugarErrorf("Failed to infer GVR from owner reference: %v", err)
			return nil, err
		}

		obj, err := ts.dynamicClient.Resource(gvr).Namespace(namespace).Get(context.Background(), name, metav1.GetOptions{})
		if err != nil {
			return nil, fmt.Errorf("failed to get %s %s/%s: %w", kind, namespace, name, err)
		}
		ancestor = obj
	}

	return ancestor, nil
}

func (ts *TaskSnapshot) inferGVRFromOwnerRef(ownerRef metav1.OwnerReference) (schema.GroupVersionResource, error) {
	gv, err := schema.ParseGroupVersion(ownerRef.APIVersion)
	if err != nil {
		return schema.GroupVersionResource{}, fmt.Errorf("failed to parse APIVersion %s: %w", ownerRef.APIVersion, err)
	}

	gvk := gv.WithKind(ownerRef.Kind)
	return ts.inferGVRFromGVK(gvk), nil
}

func (ts *TaskSnapshot) inferGVRFromObject(obj metav1.Object) (schema.GroupVersionResource, error) {
	gvk := obj.(runtime.Object).GetObjectKind().GroupVersionKind()
	return ts.inferGVRFromGVK(gvk), nil
}

func (ts *TaskSnapshot) inferGVRFromGVK(gv schema.GroupVersionKind) schema.GroupVersionResource {
	resource := ""
	switch gv.Kind {
	case "Pod":
		resource = "pods"
	case "Deployment":
		resource = "deployments"
	case "ReplicaSet":
		resource = "replicasets"
	case "StatefulSet":
		resource = "statefulsets"
	case "DaemonSet":
		resource = "daemonsets"
	case "Job":
		resource = "jobs"
	case "CronJob":
		resource = "cronjobs"
	case "Workload":
		resource = "workloads"
	case "JobSet":
		resource = "jobsets"
	case "LeaderWorkerSet":
		resource = "leaderworkersets"
	case "Workflow":
		resource = "workflows"
	case "CronWorkflow":
		resource = "cronworkflows"
	case "RayJob":
		resource = "rayjobs"
	case "RayCluster":
		resource = "rayclusters"
	case "AppWrapper":
		resource = "appwrappers"
	default:
		resource = strings.ToLower(gv.Kind) + "s"
		zlog.SugarWarnf("Warning: Using heuristic for resource name for Kind '%s'. Actual resource name might differ.\n", gv.Kind)
	}

	return gv.GroupVersion().WithResource(resource)
}
