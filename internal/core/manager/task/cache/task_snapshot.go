package cache

import (
	"context"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/tools/cache"
	kueuev1beta1 "sigs.k8s.io/kueue/apis/kueue/v1beta1"

	"transwarp.io/mlops/pipeline/internal/core/consts"
	modeltask "transwarp.io/mlops/pipeline/internal/core/model/task"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
	ptr "transwarp.io/mlops/pipeline/pkg/util/ptr"
)

type TaskSnapshot struct {
	eventHandler *EventHandler
	eventQueue   *EventQueue

	taskForest *TaskForest
}

func NewTaskCache() *TaskSnapshot {
	taskForest := NewTaskForest()
	eventQueue := NewEventQueue(10000)
	return &TaskSnapshot{
		eventHandler: NewEventHandler(1, eventQueue, taskForest),
		eventQueue:   eventQueue,

		taskForest: taskForest,
	}
}

func (tc *TaskSnapshot) ListTasks() ([]*modeltask.Task, error) {
	return tc.taskForest.ListTasks()
}

func (tc *TaskSnapshot) Start(ctx context.Context) error {
	tc.eventHandler.StartEventLoop(ctx)

	client.MustGetKueueClient().AddWorkloadEventHandler(cache.FilteringResourceEventHandler{
		FilterFunc: func(obj interface{}) bool {
			// TODO need add filter
			return true
		},
		Handler: cache.ResourceEventHandlerFuncs{
			AddFunc: func(obj interface{}) {
				kwl := obj.(*kueuev1beta1.Workload)
				zlog.SugarInfof("Kueue workload added: %s/%s", kwl.Namespace, kwl.Name)
				tc.eventQueue.Enqueue(&kueueWorkloadAddEvent{
					Workload: kwl,
					BaseEvent: BaseEvent{
						CreatedAt:   ptr.ToPtr(time.Now()),
						Retries:     0,
						LastTryTime: time.Now(),
					},
				})
			},
			UpdateFunc: func(oldObj, newObj interface{}) {
				oldkwl := oldObj.(*kueuev1beta1.Workload)
				newkwl := newObj.(*kueuev1beta1.Workload)
				tc.eventQueue.Enqueue(&kueueWorkloadUpdateEvent{
					OldWorkload: oldkwl,
					NewWorkload: newkwl,
					BaseEvent: BaseEvent{
						CreatedAt:   ptr.ToPtr(time.Now()),
						Retries:     0,
						LastTryTime: time.Now(),
					},
				})
			},
			DeleteFunc: func(obj interface{}) {
				kwl := obj.(*kueuev1beta1.Workload)
				zlog.SugarInfof("Kueue workload deleted: %s/%s", kwl.Namespace, kwl.Name)
				tc.eventQueue.Enqueue(&kueueWorkloadDeleteEvent{
					Workload: kwl,
					BaseEvent: BaseEvent{
						CreatedAt:   ptr.ToPtr(time.Now()),
						Retries:     0,
						LastTryTime: time.Now(),
					},
				})
			},
		},
	})

	client.MustGetK8sClient().AddPodWatchHandler(cache.FilteringResourceEventHandler{
		FilterFunc: func(obj interface{}) bool {
			metaObj, ok := obj.(metav1.Object)
			if !ok {
				return false
			}
			// is current llmops instance managed namespace

			labels := metaObj.GetLabels()
			if consts.HasKueueManagedLabel(labels) || consts.HasKueuePodsetLabel(labels) {
				return true
			}
			return false
		},
		Handler: cache.ResourceEventHandlerFuncs{
			AddFunc: func(obj interface{}) {
				if runtimeObj, ok := obj.(runtime.Object); ok {
					runtimeObj.GetObjectKind().SetGroupVersionKind(podGVK)
					pod := runtimeObj.(*corev1.Pod)
					tc.eventQueue.Enqueue(&podAddEvent{
						Pod: pod,
						BaseEvent: BaseEvent{
							CreatedAt:   ptr.ToPtr(time.Now()),
							Retries:     0,
							LastTryTime: time.Now(),
						},
					})
				}
			},
			UpdateFunc: func(oldObj, newObj interface{}) {
				if runtimeObj, ok := oldObj.(runtime.Object); ok {
					runtimeObj.GetObjectKind().SetGroupVersionKind(podGVK)
				}
				if runtimeObj, ok := newObj.(runtime.Object); ok {
					runtimeObj.GetObjectKind().SetGroupVersionKind(podGVK)
				}
				oldPod := oldObj.(*corev1.Pod)
				newPod := newObj.(*corev1.Pod)
				tc.eventQueue.Enqueue(&podUpdateEvent{
					OldPod: oldPod,
					NewPod: newPod,
					BaseEvent: BaseEvent{
						CreatedAt:   ptr.ToPtr(time.Now()),
						Retries:     0,
						LastTryTime: time.Now(),
					},
				})
			},
			DeleteFunc: func(obj interface{}) {
				if runtimeObj, ok := obj.(runtime.Object); ok {
					pod := runtimeObj.(*corev1.Pod)
					tc.eventQueue.Enqueue(&podDeleteEvent{
						Pod: pod,
						BaseEvent: BaseEvent{
							CreatedAt:   ptr.ToPtr(time.Now()),
							Retries:     0,
							LastTryTime: time.Now(),
						},
					})
				}
			},
		},
	})

	zlog.SugarInfof("Init task cache successfully.")
	return nil
}
