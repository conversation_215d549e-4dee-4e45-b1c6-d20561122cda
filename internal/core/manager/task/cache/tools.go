package cache

import (
	"context"
	"fmt"
	"strings"
	"sync"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	"transwarp.io/mlops/pipeline/internal/util"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

var (
	dynamicClient dynamic.Interface
	toolsOnce     sync.Once
)

func mustGetDynamicClient() dynamic.Interface {
	if dynamicClient == nil {
		toolsOnce.Do(func() {
			config, err := util.GetKubeConfig()
			if err != nil {
				panic(err)
			}
			dynamicClient, err = dynamic.NewForConfig(config)
			if err != nil {
				panic(err)
			}
		})
	}
	return dynamicClient
}

func getObjectFromOwnerRef(namespace string, ownerRef metav1.OwnerReference) (metav1.Object, error) {
	name := ownerRef.Name
	kind := ownerRef.Kind

	gvr, err := inferGVRFromOwnerRef(ownerRef)
	if err != nil {
		zlog.SugarErrorf("Failed to infer GVR from owner reference: %v", err)
		return nil, err
	}

	obj, err := mustGetDynamicClient().Resource(gvr).Namespace(namespace).Get(context.Background(), name, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get %s %s/%s: %w", kind, namespace, name, err)
	}

	return obj, nil
}

func findAllAncestor(obj metav1.Object) ([]metav1.Object, error) {
	ancestors := make([]metav1.Object, 0)
	namespace := obj.GetNamespace()

	for _, ref := range obj.GetOwnerReferences() {
		ancestor, err := findAncestorFromOwnerRef(namespace, ref)
		if err != nil {
			return nil, err
		}
		ancestors = append(ancestors, ancestor)
	}

	return ancestors, nil
}

func findAncestorFromOwnerRef(namespace string, ownerRef metav1.OwnerReference) (metav1.Object, error) {
	var ancestor metav1.Object
	for {
		name := ownerRef.Name
		kind := ownerRef.Kind

		gvr, err := inferGVRFromOwnerRef(ownerRef)
		if err != nil {
			zlog.SugarErrorf("Failed to infer GVR from owner reference: %v", err)
			return nil, err
		}

		obj, err := mustGetDynamicClient().Resource(gvr).Namespace(namespace).Get(context.Background(), name, metav1.GetOptions{})
		if err != nil {
			return nil, fmt.Errorf("failed to get %s %s/%s: %w", kind, namespace, name, err)
		}
		ancestor = obj

		if len(obj.GetOwnerReferences()) == 0 {
			break
		}
		ownerRef = obj.GetOwnerReferences()[0]
	}

	return ancestor, nil
}

func findAnyAncestor(obj metav1.Object) (metav1.Object, error) {
	ancestor := obj
	namespace := obj.GetNamespace()
	for {
		ownerRefs := ancestor.GetOwnerReferences()
		if len(ownerRefs) == 0 {
			break
		}

		ownerRef := ownerRefs[0]
		name := ownerRef.Name
		kind := ownerRef.Kind

		gvr, err := inferGVRFromOwnerRef(ownerRef)
		if err != nil {
			zlog.SugarErrorf("Failed to infer GVR from owner reference: %v", err)
			return nil, err
		}

		obj, err := mustGetDynamicClient().Resource(gvr).Namespace(namespace).Get(context.Background(), name, metav1.GetOptions{})
		if err != nil {
			return nil, fmt.Errorf("failed to get %s %s/%s: %w", kind, namespace, name, err)
		}
		ancestor = obj
	}

	return ancestor, nil
}

func inferGVRFromOwnerRef(ownerRef metav1.OwnerReference) (schema.GroupVersionResource, error) {
	gv, err := schema.ParseGroupVersion(ownerRef.APIVersion)
	if err != nil {
		return schema.GroupVersionResource{}, fmt.Errorf("failed to parse APIVersion %s: %w", ownerRef.APIVersion, err)
	}

	gvk := gv.WithKind(ownerRef.Kind)
	return inferGVRFromGVK(gvk), nil
}

func inferGVRFromObject(obj metav1.Object) schema.GroupVersionResource {
	gvk := obj.(runtime.Object).GetObjectKind().GroupVersionKind()
	return inferGVRFromGVK(gvk)
}
func inferGVKFromObject(obj metav1.Object) schema.GroupVersionKind {
	return obj.(runtime.Object).GetObjectKind().GroupVersionKind()
}

func inferGVRFromGVK(gv schema.GroupVersionKind) schema.GroupVersionResource {
	resource := ""
	switch gv.Kind {
	case "Pod":
		resource = "pods"
	case "Deployment":
		resource = "deployments"
	case "ReplicaSet":
		resource = "replicasets"
	case "StatefulSet":
		resource = "statefulsets"
	case "DaemonSet":
		resource = "daemonsets"
	case "Job":
		resource = "jobs"
	case "CronJob":
		resource = "cronjobs"
	case "Workload":
		resource = "workloads"
	case "JobSet":
		resource = "jobsets"
	case "LeaderWorkerSet":
		resource = "leaderworkersets"
	case "Workflow":
		resource = "workflows"
	case "CronWorkflow":
		resource = "cronworkflows"
	case "RayJob":
		resource = "rayjobs"
	case "RayCluster":
		resource = "rayclusters"
	case "AppWrapper":
		resource = "appwrappers"
	case "EventBus":
		resource = "eventbus"
	default:
		resource = strings.ToLower(gv.Kind) + "s"
		zlog.SugarWarnf("Warning: Using heuristic for resource name %s for Kind '%s'. Actual resource name might differ.", resource, gv.Kind)
	}

	return gv.GroupVersion().WithResource(resource)
}
