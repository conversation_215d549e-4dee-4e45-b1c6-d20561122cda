package cache

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	kueuev1beta1 "sigs.k8s.io/kueue/apis/kueue/v1beta1"
	modeltask "transwarp.io/mlops/pipeline/internal/core/model/task"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

func (twln *TaskWorkloadNode) replaceOldTaskNode(oldNode, newNode *TaskNode) bool {
	for i, node := range twln.children {
		if node.ref.GetUID() == oldNode.ref.GetUID() {
			if newNode.lastTransitionTime.Before(*node.lastTransitionTime) {
				zlog.SugarWarnf("Skip update pod %s because new transition time %v is before old transition time %v",
					oldNode.ref.GetName(), newNode.lastTransitionTime, node.lastTransitionTime)
				return true
			}
			twln.children[i] = newNode
			return true
		}
		if node.replaceOldTaskNode(oldNode, newNode) {
			return true
		}
	}
	return false
}

func (twln *TaskWorkloadNode) belongToTaskWorkloadNode(node *TaskNode) bool {
	for _, ownerRef := range twln.ref.OwnerReferences {
		if node.ref.GetUID() == ownerRef.UID {
			return true
		}
		if node.children != nil && len(node.children) > 0 {
			for _, child := range node.children {
				if twln.belongToTaskWorkloadNode(child) {
					return true
				}
			}
		}
	}
	return false
}
func (twln *TaskWorkloadNode) deleteTaskNode(node *TaskNode) bool {
	for i, n := range twln.children {
		if node.ref.GetUID() == n.ref.GetUID() {
			twln.children = append(twln.children[:i], twln.children[i+1:]...)
			return true
		}
		if n.deleteTaskNode(node) {
			if !n.hasLeafPod() {
				twln.children = append(twln.children[:i], twln.children[i+1:]...)
			}
			return true
		}
	}
	return false
}

func (twn *TaskWorkloadNode) toSubTask() (*modeltask.SubTask, error) {
	pods, err := twn.getAllPods()
	if err != nil {
		return nil, err
	}

	subTaskResources := &modeltask.Resources{
		Requests: modeltask.ResourceSpec{},
		Limits:   modeltask.ResourceSpec{},
	}

	for _, pod := range pods {
		if pod.Resources != nil {
			subTaskResources.Add(pod.Resources)
		}
	}

	kwl := twn.ref
	subTask := &modeltask.SubTask{
		ID:          string(kwl.UID),
		Name:        kwl.Name,
		WorkloadRef: kwl,
		CreatedAt:   kwl.CreationTimestamp.Time,

		Resources: subTaskResources,
		Pods:      pods,
	}
	if kwl.Status.Conditions != nil && len(kwl.Status.Conditions) > 0 {
		for _, condition := range kwl.Status.Conditions {
			if condition.Status == metav1.ConditionTrue && condition.Type == kueuev1beta1.WorkloadAdmitted {
				subTask.StartedAt = &condition.LastTransitionTime.Time
			}
			if condition.Status == metav1.ConditionTrue && condition.Type == kueuev1beta1.WorkloadFinished {
				subTask.EndedAt = &condition.LastTransitionTime.Time
			}
			subTask.Status = modeltask.SubTaskStatus(condition.Type)
		}
	}
	return subTask, nil
}

func (twn *TaskWorkloadNode) getAllPods() ([]*modeltask.Pod, error) {
	pods := make([]*modeltask.Pod, 0)
	for _, node := range twn.children {
		childPods, err := node.getAllPods()
		if err != nil {
			return nil, err
		}
		pods = append(pods, childPods...)
	}
	return pods, nil
}
