package cache

import (
	"sync"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"transwarp.io/mlops/pipeline/internal/core/consts"
	modeltask "transwarp.io/mlops/pipeline/internal/core/model/task"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

func NewTaskTree(refs []metav1.Object) *TaskTree {
	return &TaskTree{
		root: &TaskRoot{
			refs:      refs,
			workloads: make([]*TaskWorkloadNode, 0),
		},
		mu: sync.Mutex{},
	}
}

// TODO FIXED LIUSEN
func (tt *TaskTree) AddTaskWorkloadNode(node *TaskWorkloadNode) {
	tt.mu.Lock()
	defer tt.mu.Unlock()
	tt.root.workloads = append(tt.root.workloads, node)
}

// TODO FIXED LIUSEN
func (tt *TaskTree) UpdateTaskWorkloadNode(oldNode, newNode *TaskWorkloadNode) (error, bool) {
	tt.mu.Lock()
	defer tt.mu.Unlock()
	for i, node := range tt.root.workloads {
		if node.ref == oldNode.ref {
			if newNode.lastTransitionTime.Before(*node.lastTransitionTime) {
				zlog.SugarWarnf("Skip update workload %s because new transition time %v is before old transition time %v", oldNode.ref.GetName(), newNode.lastTransitionTime, node.lastTransitionTime)
				return nil, true
			}
			for _, child := range node.children {
				newNode.children = append(newNode.children, child)
			}
			tt.root.workloads[i] = newNode
			return nil, true
		}
	}

	return nil, false
}

// try to delete workload node if found in tree
// nil, true meaning successful
// nil, false meaning not found
// err, false meaning throw error when do delete
// err, true no meaning
func (tt *TaskTree) TryDeleteTaskWorkloadNode(node *TaskWorkloadNode) (error, bool) {
	tt.mu.Lock()
	defer tt.mu.Unlock()
	for i, twln := range tt.root.workloads {
		if twln.ref.GetUID() == node.ref.GetUID() {
			tt.root.workloads = append(tt.root.workloads[:i], tt.root.workloads[i+1:]...)
			return nil, true
		}
	}
	return nil, false
}

func (tt *TaskTree) AddTaskNode(node *TaskNode) (error, bool) {
	tt.mu.Lock()
	defer tt.mu.Unlock()
	for _, twln := range tt.root.workloads {
		if twln.belongToTaskWorkloadNode(node) {
			twln.children = append(twln.children, node)
			return nil, true
		}
	}
	return nil, false
}

func (tt *TaskTree) UpdateTaskNode(oldNode, newNode *TaskNode) (error, bool) {
	tt.mu.Lock()
	defer tt.mu.Unlock()
	for _, twln := range tt.root.workloads {
		if twln.belongToTaskWorkloadNode(oldNode) {
			if twln.replaceOldTaskNode(oldNode, newNode) {
				return nil, true
			}
			return nil, false
		}
	}
	return nil, false
}

func (tt *TaskTree) TryDeleteTaskNode(node *TaskNode) (error, bool) {
	tt.mu.Lock()
	defer tt.mu.Unlock()
	for _, twln := range tt.root.workloads {
		if twln.belongToTaskWorkloadNode(node) {
			if twln.deleteTaskNode(node) {
				return nil, true
			}
			return nil, false
		}
	}
	return nil, false
}

func (tt *TaskTree) ToTask() *modeltask.Task {
	tt.mu.Lock()
	defer tt.mu.Unlock()

	subTasks := make([]*modeltask.SubTask, 0)
	for _, twn := range tt.root.workloads {
		subTask, err := twn.toSubTask()
		if err != nil {
			zlog.Warnf("Failed to convert workload to subtask: %v", err)
			continue
		}
		subTasks = append(subTasks, subTask)
	}

	totalResources := &modeltask.Resources{
		Requests: modeltask.ResourceSpec{},
		Limits:   modeltask.ResourceSpec{},
	}
	for _, subTask := range subTasks {
		if subTask.Resources != nil {
			totalResources.Add(subTask.Resources)
		}
	}
	runningNodeNames := make([]string, 0)
	podCount := 0
	for _, subTask := range subTasks {
		runningNodeNames = append(runningNodeNames, subTask.RunningNodeNames()...)
		podCount += len(subTask.Pods)
	}

	if tt.root == nil || tt.root.workloads == nil || len(tt.root.workloads) == 0 {
		zlog.SugarWarnf("Failed to convert task tree to task: root is nil or workloads is nil or workloads is empty.")
		return nil
	}

	kwl := tt.root.workloads[0].ref
	ref := tt.root.refs[0]
	task := &modeltask.Task{
		ID:          string(kwl.OwnerReferences[0].UID),
		Name:        ref.GetAnnotations()[consts.AnnotationTaskNameKey],
		Type:        modeltask.ToTaskType(ref.GetAnnotations()[consts.AnnotationTaskTypeKey]),
		Creator:     ref.GetAnnotations()[consts.AnnotationTaskCreatorKey],
		Description: ref.GetAnnotations()[consts.AnnotationTaskDescriptionKey],
		CreatedAt:   ref.GetCreationTimestamp().Time,
		UpdatedAt:   ref.GetCreationTimestamp().Time,
		Status:      modeltask.TaskStatusPending,

		TenantID:    ref.GetAnnotations()[consts.AnnotationTaskTenantIDKey],
		TenantName:  ref.GetAnnotations()[consts.AnnotationTaskTenantNameKey],
		ProjectID:   ref.GetAnnotations()[consts.AnnotationTaskProjectIDKey],
		ProjectName: ref.GetAnnotations()[consts.AnnotationTaskProjectNameKey],

		RefSourceID: func(metaObj metav1.Object) string {
			if sourceID, ok := metaObj.GetAnnotations()[consts.AnnotationTaskRefSourceIDKey]; ok {
				return sourceID
			} else if sourceID, ok := metaObj.GetLabels()[consts.LabelTaskRefSourceIDKey]; ok {
				return sourceID
			} else {
				return ""
			}
		}(ref),

		Priority: modeltask.ToTaskPriority(kwl.Spec.Priority),

		QueueName: string(kwl.Spec.QueueName),

		Resources: totalResources,
		NodeNames: runningNodeNames,
		SubTasks:  subTasks,
		PodCount:  podCount,

		Namespace: kwl.Namespace,
		GVK:       inferGVKFromObject(ref),
	}

	if kwl.Status.Admission != nil {
		task.ClusterQueueName = string(kwl.Status.Admission.ClusterQueue)
	}
	// update task status
	task.UpdateTaskStatus().
		UpdateTaskStartedAt().
		UpdateTaskEndedAt().
		UpdateQueueDuration()

	return task
}
