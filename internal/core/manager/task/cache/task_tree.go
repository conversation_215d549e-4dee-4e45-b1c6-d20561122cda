package cache

import (
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	kueuev1beta1 "sigs.k8s.io/kueue/apis/kueue/v1beta1"

	"transwarp.io/mlops/pipeline/internal/core/consts"
	modeltask "transwarp.io/mlops/pipeline/internal/core/model/task"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

func (tt *TaskTree) ToTask() *modeltask.Task {
	subTasks := make([]*modeltask.SubTask, 0)
	for _, twn := range tt.root.workloads {
		subTask, err := twn.toSubTask()
		if err != nil {
			zlog.Warnf("Failed to convert workload to subtask: %v", err)
			continue
		}
		subTasks = append(subTasks, subTask)
	}

	kwl := tt.root.workloads[0].ref
	ref := tt.root.refs[0]
	task := &modeltask.Task{
		ID:          string(kwl.OwnerReferences[0].UID),
		Name:        ref.GetAnnotations()[consts.AnnotationTaskNameKey],
		Type:        modeltask.ToTaskType(ref.GetAnnotations()[consts.AnnotationTaskTypeKey]),
		Creator:     ref.GetAnnotations()[consts.AnnotationTaskCreatorKey],
		Description: ref.GetAnnotations()[consts.AnnotationTaskDescriptionKey],
		CreatedAt:   ref.GetCreationTimestamp().Time,
		UpdatedAt:   ref.GetCreationTimestamp().Time,
		Status:      modeltask.TaskStatusPending,

		TenantID:   ref.GetAnnotations()[consts.AnnotationTaskTenantIDKey],
		TenantName: ref.GetAnnotations()[consts.AnnotationTaskTenantNameKey],

		ProjectID:   ref.GetAnnotations()[consts.AnnotationTaskProjectIDKey],
		ProjectName: ref.GetAnnotations()[consts.AnnotationTaskProjectNameKey],
		Priority:    modeltask.ToTaskPriority(kwl.Spec.Priority),

		QueueName: string(kwl.Spec.QueueName),

		Resources: nil,
		// TODO
		//Status:    nil,

		SubTasks: subTasks,

		Namespace: kwl.Namespace,
	}

	if kwl.Status.Admission != nil {
		task.ClusterQueueName = string(kwl.Status.Admission.ClusterQueue)
	}
	// update task status
	task.UpdateTaskStatus().UpdateTaskStartedAt().UpdateTaskEndedAt()

	return task
}

func (twn *TaskWorkloadNode) toSubTask() (*modeltask.SubTask, error) {
	pods, err := twn.getAllPods()
	if err != nil {
		return nil, err
	}
	kwl := twn.ref
	subTask := &modeltask.SubTask{
		ID:           string(kwl.UID),
		Name:         kwl.Name,
		ParentTaskID: "",
		WorkloadRef:  kwl,
		CreatedAt:    kwl.CreationTimestamp.Time,

		// TODO
		Resources: nil,
		Pods:      pods,
	}
	if kwl.Status.Conditions != nil && len(kwl.Status.Conditions) > 0 {
		for _, condition := range kwl.Status.Conditions {
			if condition.Status == metav1.ConditionTrue && condition.Type == kueuev1beta1.WorkloadAdmitted {
				subTask.StartedAt = &condition.LastTransitionTime.Time
			}
			if condition.Status == metav1.ConditionTrue && condition.Type == kueuev1beta1.WorkloadFinished {
				subTask.EndedAt = &condition.LastTransitionTime.Time
			}
			subTask.Status = modeltask.SubTaskStatus(condition.Type)
		}
	}
	return subTask, nil
}

func (twn *TaskWorkloadNode) getAllPods() ([]*modeltask.Pod, error) {
	pods := make([]*modeltask.Pod, 0)
	for _, node := range twn.children {
		childPods, err := node.getAllPods()
		if err != nil {
			return nil, err
		}
		pods = append(pods, childPods...)
	}
	return pods, nil
}

func (tn *TaskNode) getAllPods() ([]*modeltask.Pod, error) {
	pods := make([]*modeltask.Pod, 0)
	if tn.isPod {
		p := tn.ref.(*corev1.Pod)
		pod := &modeltask.Pod{
			Name:      p.GetName(),
			Namespace: p.GetNamespace(),
			NodeName:  p.Spec.NodeName,
			Phase:     p.Status.Phase,
			HostIP:    p.Status.HostIP,
		}
		if p.Status.StartTime != nil {
			pod.StartedAt = &p.Status.StartTime.Time
		}

		containers := make([]*modeltask.Container, 0)
		for _, c := range p.Spec.Containers {
			container := &modeltask.Container{
				Name:  c.Name,
				Image: c.Image,
			}
			container.Resources = &modeltask.Resources{
				Requests: modeltask.ResourceSpec{
					CPU:    c.Resources.Requests.Cpu().String(),
					Memory: c.Resources.Requests.Memory().String(),
				},
				Limits: modeltask.ResourceSpec{
					CPU:    c.Resources.Limits.Cpu().String(),
					Memory: c.Resources.Limits.Memory().String(),
				},
			}
			containers = append(containers, container)
		}
		pod.Containers = containers

		pod.Resources = tn.aggregateContainerResources(p.Spec.Containers)

		pods = append(pods, pod)
	} else if tn.children != nil && len(tn.children) > 0 {
		for _, node := range tn.children {
			childPods, err := node.getAllPods()
			if err != nil {
				return nil, err
			}
			pods = append(pods, childPods...)
		}
	}
	return pods, nil
}

func (tn *TaskNode) aggregateContainerResources(containers []corev1.Container) *modeltask.Resources {
	totalRequests := corev1.ResourceList{}
	totalLimits := corev1.ResourceList{}

	for _, c := range containers {
		for name, quantity := range c.Resources.Requests {
			if current, exists := totalRequests[name]; exists {
				current.Add(quantity)
				totalRequests[name] = current
			} else {
				totalRequests[name] = quantity.DeepCopy()
			}
		}
		for name, quantity := range c.Resources.Limits {
			if current, exists := totalLimits[name]; exists {
				current.Add(quantity)
				totalLimits[name] = current
			} else {
				totalLimits[name] = quantity.DeepCopy()
			}
		}
	}

	return &modeltask.Resources{
		Requests: modeltask.ResourceSpec{
			CPU:    totalRequests.Cpu().String(),
			Memory: totalRequests.Memory().String(),
			// GPU:       totalRequests.Name("nvidia.com/gpu").String(),
			// GPUMemory: totalRequests.Name("nvidia.com/gpu-memory").String(),
		},
		Limits: modeltask.ResourceSpec{
			CPU:    totalLimits.Cpu().String(),
			Memory: totalLimits.Memory().String(),
			// GPU:       totalLimits.Name("nvidia.com/gpu").String(),
			// GPUMemory: totalLimits.Name("nvidia.com/gpu-memory").String(),
		},
	}
}
