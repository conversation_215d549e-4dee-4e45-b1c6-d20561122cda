package task

import (
	"context"
	"fmt"
	"strings"
	"time"

	"transwarp.io/mlops/pipeline/internal/core/manager/task/query"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

type TaskManager struct {
	kueueClient   *client.KueueClient
	queryExecutor *query.QueryExecutor[*model.Task]
	dataProvider  *TaskDataProvider
}

func NewTaskManager() *TaskManager {
	kueueClient := client.MustGetKueueClient()
	dataProvider := NewTaskDataProvider(kueueClient)

	executorConfig := &query.ExecutorConfig{
		QueryTimeout:       300 * time.Second,
		SlowQueryThreshold: 10 * time.Second,
		MaxConcurrency:     20,
	}

	queryExecutor := query.NewQueryExecutor[*model.Task](dataProvider, executorConfig)

	return &TaskManager{
		kueueClient:   kueueClient,
		queryExecutor: queryExecutor,
		dataProvider:  dataProvider,
	}
}

func (ts *TaskManager) ListTasks(ctx context.Context, req *model.ListTasksReq) (*model.ListTasksResp, error) {
	zlog.Info(fmt.Sprintf("Listing tasks with request: %+v", req))

	queryBuilder := query.NewTaskQuery()
	ts.applyFilters(queryBuilder, req)
	ts.applySorting(queryBuilder, req)
	if req.Page > 0 && req.PageSize > 0 {
		queryBuilder.Limit(req.Page, req.PageSize)
	}

	var result *query.QueryResult[*model.Task]
	var err error

	result, err = ts.queryExecutor.Execute(ctx, queryBuilder.QueryBuilder)

	if err != nil {
		return nil, fmt.Errorf("failed to execute task query: %w", err)
	}

	return &model.ListTasksResp{
		Items:      result.Items,
		Total:      result.Total,
		Page:       result.Page,
		PageSize:   result.PageSize,
		TotalPages: result.TotalPages,
	}, nil
}

func (ts *TaskManager) listTasks(ctx context.Context) ([]*model.Task, error) {
	return ts.dataProvider.GetData(ctx)
}

func (ts *TaskManager) applyFilters(queryBuilder *query.TaskQuery, req *model.ListTasksReq) {
	if len(req.ProjectIDs) > 0 {
		queryBuilder.WhereProjectIDs(req.ProjectIDs)
	}
	if len(req.TenantIDs) > 0 {
		queryBuilder.WhereTenantIDs(req.TenantIDs)
	}

	if len(req.Status) > 0 {
		statuses := make([]model.TaskStatus, len(req.Status))
		for i, s := range req.Status {
			statuses[i] = model.TaskStatus(s)
		}
		queryBuilder.WhereStatuses(statuses)
	}

	if len(req.Types) > 0 {
		types := make([]model.TaskType, len(req.Types))
		for i, t := range req.Types {
			types[i] = model.TaskType(t)
		}
		queryBuilder.WhereTypes(types)
	}

	if len(req.Priorities) > 0 {
		priorities := make([]model.TaskPriority, len(req.Priorities))
		for i, p := range req.Priorities {
			priorities[i] = model.TaskPriority(p)
		}
		queryBuilder.WherePriorities(priorities)
	}

	if req.CreateAtStart != nil {
		startTime := time.Unix(*req.CreateAtStart, 0)
		queryBuilder.WhereCreatedAfter(startTime)
	}

	if req.CreateAtEnd != nil {
		endTime := time.Unix(*req.CreateAtEnd, 0)
		queryBuilder.WhereCreatedBefore(endTime)
	}

	if req.SearchKeyword != "" {
		queryBuilder.WhereKeywordSearch(req.SearchKeyword)
	}
}

func (ts *TaskManager) applySorting(queryBuilder *query.TaskQuery, req *model.ListTasksReq) {
	sortBy := req.SortBy
	if sortBy == "" {
		sortBy = "create_at"
	}

	sortOrder := req.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}

	direction := query.SortAsc
	if sortOrder == "desc" {
		direction = query.SortDesc
	}

	switch strings.ToLower(sortBy) {
	case "name":
		queryBuilder.OrderByName(direction)
	case "create_at":
		queryBuilder.OrderByCreateTime(direction)
	case "start_at":
		queryBuilder.OrderByStartTime(direction)
	case "priority":
		queryBuilder.OrderByPriority(direction)
	case "status":
		queryBuilder.OrderByStatus(direction)
	default:
		queryBuilder.OrderByCreateTime(direction)
	}
}

type Pod struct {
	Name      string
	Namespace string
	UID       string
	Status    string
}

func (ts *TaskManager) findPodsByTaskID(ctx context.Context, id string) ([]*model.Pod, error) {
	task, err := ts.GetTask(ctx, id)
	if err != nil {
		return nil, err
	}
	pods := make([]*model.Pod, 0)
	for _, subTask := range task.SubTasks {
		pods = append(pods, subTask.Pods...)
	}

	return pods, nil
}

// TODO
func (ts *TaskManager) CreateTask(ctx context.Context, id string) error {
	return nil
}

// TODO
func (ts *TaskManager) DeleteTask(ctx context.Context, id string) error {
	return nil
}

func (ts *TaskManager) GetTask(ctx context.Context, id string) (*model.Task, error) {
	tasks, err := ts.listTasks(ctx)
	if err != nil {
		return nil, err
	}
	for _, task := range tasks {
		if task.ID == id {
			return task, nil
		}
	}
	return nil, fmt.Errorf("Not found task by id: %s", id)
}

func (ts *TaskManager) QueryTasks(ctx context.Context, queryBuilder *query.TaskQuery) (*query.QueryResult[*model.Task], error) {
	return ts.queryExecutor.Execute(ctx, queryBuilder.QueryBuilder)
}

func (ts *TaskManager) CountTasks(ctx context.Context, queryBuilder *query.TaskQuery) (int32, error) {
	return ts.queryExecutor.Count(ctx, queryBuilder.QueryBuilder)
}

func (ts *TaskManager) ExistsTask(ctx context.Context, queryBuilder *query.TaskQuery) (bool, error) {
	return ts.queryExecutor.Exists(ctx, queryBuilder.QueryBuilder)
}
