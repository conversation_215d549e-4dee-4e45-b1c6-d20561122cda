package task

import (
	"context"
	"fmt"
	"strings"
	"time"

	"k8s.io/client-go/dynamic"

	"transwarp.io/mlops/pipeline/internal/core/manager/task/query"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
	"transwarp.io/mlops/pipeline/internal/util"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

type TaskManager struct {
	queryExecutor *query.QueryExecutor[*model.Task]
	dataProvider  *TaskDataProvider
	dynamicClient dynamic.Interface
}

func NewTaskManager() *TaskManager {
	kueueClient := client.MustGetKueueClient()
	dataProvider := NewTaskDataProvider(kueueClient)

	// 创建动态客户端
	config, err := util.GetKubeConfig()
	if err != nil {
		panic(fmt.Sprintf("failed to get kube config: %v", err))
	}
	dynamicClient, err := dynamic.NewForConfig(config)
	if err != nil {
		panic(fmt.Sprintf("failed to create dynamic client: %v", err))
	}

	executorConfig := &query.ExecutorConfig{
		QueryTimeout:       300 * time.Second,
		SlowQueryThreshold: 10 * time.Second,
		MaxConcurrency:     20,
	}

	queryExecutor := query.NewQueryExecutor[*model.Task](dataProvider, executorConfig)

	return &TaskManager{
		queryExecutor: queryExecutor,
		dataProvider:  dataProvider,
		dynamicClient: dynamicClient,
	}
}

func (ts *TaskManager) ListTasks(ctx context.Context, req *model.ListTasksReq) (*model.ListTasksResp, error) {
	zlog.Info(fmt.Sprintf("Listing tasks with request: %+v", req))

	queryBuilder := query.NewTaskQuery()
	ts.applyFilters(queryBuilder, req)
	ts.applySorting(queryBuilder, req)
	if req.Page > 0 && req.PageSize > 0 {
		queryBuilder.Limit(req.Page, req.PageSize)
	}

	var result *query.QueryResult[*model.Task]
	var err error

	result, err = ts.queryExecutor.Execute(ctx, queryBuilder.QueryBuilder)

	if err != nil {
		return nil, fmt.Errorf("failed to execute task query: %w", err)
	}

	return &model.ListTasksResp{
		Items:      result.Items,
		Total:      result.Total,
		Page:       result.Page,
		PageSize:   result.PageSize,
		TotalPages: result.TotalPages,
	}, nil
}

func (ts *TaskManager) listTasks(ctx context.Context) ([]*model.Task, error) {
	return ts.dataProvider.GetData(ctx)
}

func (ts *TaskManager) applyFilters(queryBuilder *query.TaskQuery, req *model.ListTasksReq) {
	if len(req.ProjectIDs) > 0 {
		queryBuilder.WhereProjectIDs(req.ProjectIDs)
	}
	if len(req.TenantIDs) > 0 {
		queryBuilder.WhereTenantIDs(req.TenantIDs)
	}

	if len(req.Status) > 0 {
		statuses := make([]model.TaskStatus, len(req.Status))
		for i, s := range req.Status {
			statuses[i] = model.TaskStatus(s)
		}
		queryBuilder.WhereStatuses(statuses)
	}

	if len(req.Types) > 0 {
		types := make([]model.TaskType, len(req.Types))
		for i, t := range req.Types {
			types[i] = model.TaskType(t)
		}
		queryBuilder.WhereTypes(types)
	}

	if len(req.Priorities) > 0 {
		priorities := make([]model.TaskPriority, len(req.Priorities))
		for i, p := range req.Priorities {
			priorities[i] = model.TaskPriority(p)
		}
		queryBuilder.WherePriorities(priorities)
	}

	if req.CreatedAtStart != nil {
		startTime := time.Unix(*req.CreatedAtStart, 0)
		queryBuilder.WhereCreatedAfter(startTime)
	}

	if req.CreatedAtEnd != nil {
		endTime := time.Unix(*req.CreatedAtEnd, 0)
		queryBuilder.WhereCreatedBefore(endTime)
	}

	if req.SearchKeyword != "" {
		queryBuilder.WhereKeywordSearch(req.SearchKeyword)
	}
}

func (ts *TaskManager) applySorting(queryBuilder *query.TaskQuery, req *model.ListTasksReq) {
	sortBy := req.SortBy
	if sortBy == "" {
		sortBy = "create_at"
	}

	sortOrder := req.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}

	direction := query.SortAsc
	if sortOrder == "desc" {
		direction = query.SortDesc
	}

	switch strings.ToLower(sortBy) {
	case "name":
		queryBuilder.OrderByName(direction)
	case "create_at":
		queryBuilder.OrderByCreateTime(direction)
	case "start_at":
		queryBuilder.OrderByStartTime(direction)
	case "priority":
		queryBuilder.OrderByPriority(direction)
	case "status":
		queryBuilder.OrderByStatus(direction)
	default:
		queryBuilder.OrderByCreateTime(direction)
	}
}

func (ts *TaskManager) CreateTaskFromYAML(ctx context.Context, yamlData []byte) (*model.Task, error) {
	// 	var obj unstructured.Unstructured
	// 	if err := yaml.Unmarshal(yamlData, &obj); err != nil {
	// 		return nil, fmt.Errorf("failed to parse YAML: %w", err)
	// 	}
	//
	// 	// 验证资源是否有必需的标签
	// 	if err := ts.validateResourceLabels(&obj); err != nil {
	// 		return nil, fmt.Errorf("resource validation failed: %w", err)
	// 	}
	//
	// 	// 获取GVR
	// 	gvr, err := ts.getGVRFromObject(&obj)
	// 	if err != nil {
	// 		return nil, fmt.Errorf("failed to get GVR: %w", err)
	// 	}
	//
	// 	// 创建资源到Kubernetes
	// 	_, err = ts.dynamicClient.Resource(gvr).Namespace(obj.GetNamespace()).Create(ctx, &obj, metav1.CreateOptions{})
	// 	if err != nil {
	// 		return nil, fmt.Errorf("failed to create resource in Kubernetes: %w", err)
	// 	}
	//
	// 	zlog.SugarInfof("Successfully created task %s from YAML")
	return nil, nil
}

// // TODO
func (ts *TaskManager) DeleteTask(ctx context.Context, id string) error {
	return nil
}

func (ts *TaskManager) GetTask(ctx context.Context, id string) (*model.Task, error) {
	// 	tasks, err := ts.listTasks(ctx)
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// 	for _, task := range tasks {
	// 		if task.ID == id {
	// 			return task, nil
	// 		}
	// 	}
	return nil, fmt.Errorf("Not found task by id: %s", id)
}

//	func (ts *TaskManager) QueryTasks(ctx context.Context, queryBuilder *query.TaskQuery) (*query.QueryResult[*model.Task], error) {
//		return ts.queryExecutor.Execute(ctx, queryBuilder.QueryBuilder)
//	}
//
//	func (ts *TaskManager) CountTasks(ctx context.Context, queryBuilder *query.TaskQuery) (int32, error) {
//		return ts.queryExecutor.Count(ctx, queryBuilder.QueryBuilder)
//	}
//
//	func (ts *TaskManager) ExistsTask(ctx context.Context, queryBuilder *query.TaskQuery) (bool, error) {
//		return ts.queryExecutor.Exists(ctx, queryBuilder.QueryBuilder)
//	}
func (ts *TaskManager) SuspendTask(ctx context.Context, taskID string) (*model.Task, error) {
	return nil, nil
}

// // RerunTask 重新运行任务
func (ts *TaskManager) RerunTask(ctx context.Context, taskID string) (*model.Task, error) {
	// 	// 获取原始任务
	// 	originalTask, err := ts.GetTask(ctx, taskID)
	// 	if err != nil {
	// 		return nil, fmt.Errorf("failed to get original task: %w", err)
	// 	}
	//
	// 	// 检查任务状态是否允许重新运行
	// 	if !ts.canRerunTask(originalTask) {
	// 		return nil, fmt.Errorf("task %s cannot be rerun, current status: %s", taskID, originalTask.Status)
	// 	}
	//
	// 	// 获取原始资源
	// 	gvr := schema.GroupVersionResource{
	// 		Group:    originalTask.GVR.Group,
	// 		Version:  originalTask.GVR.Version,
	// 		Resource: originalTask.GVR.Resource,
	// 	}
	//
	// 	// 删除原有资源（如果存在）
	// 	err = ts.dynamicClient.Resource(gvr).Namespace(originalTask.Namespace).Delete(ctx, originalTask.Name, metav1.DeleteOptions{})
	// 	if err != nil && !apierrors.IsNotFound(err) {
	// 		return nil, fmt.Errorf("failed to delete original resource: %w", err)
	// 	}
	//
	// 	// 等待资源删除完成
	// 	time.Sleep(2 * time.Second)
	//
	// 	// 重新创建资源
	// 	newObj := &unstructured.Unstructured{}
	// 	newObj.SetGroupVersionKind(schema.GroupVersionKind{
	// 		Group:   originalTask.GVK.Group,
	// 		Version: originalTask.GVK.Version,
	// 		Kind:    originalTask.GVK.Kind,
	// 	})
	// 	newObj.SetName(originalTask.Name)
	// 	newObj.SetNamespace(originalTask.Namespace)
	// 	newObj.SetLabels(originalTask.Labels)
	// 	newObj.SetAnnotations(originalTask.Annotations)
	//
	// 	// 创建新资源
	// 	_, err = ts.dynamicClient.Resource(gvr).Namespace(originalTask.Namespace).Create(ctx, newObj, metav1.CreateOptions{})
	// 	if err != nil {
	// 		return nil, fmt.Errorf("failed to recreate resource: %w", err)
	// 	}
	//
	// 	zlog.SugarInfof("Successfully rerun task %s, new task ID: %s", taskID)
	return nil, nil
}

//
// func (ts *TaskManager) validateResourceLabels(obj *unstructured.Unstructured) error {
// 	labels := obj.GetLabels()
// 	if labels == nil {
// 		return fmt.Errorf("resource must have labels")
// 	}
//
// 	queueName, hasQueue := labels["kueue.x-k8s.io/queue-name"]
// 	if !hasQueue || queueName == "" {
// 		return fmt.Errorf("resource must have 'kueue.x-k8s.io/queue-name' label")
// 	}
//
// 	namespace := obj.GetNamespace()
// 	if namespace == "" {
// 		return fmt.Errorf("resource must have a namespace")
// 	}
//
// 	// 验证命名空间是否被管理
// 	ns, err := ts.k8sClient.GetNamespace(context.Background(), namespace)
// 	if err != nil {
// 		return fmt.Errorf("failed to get namespace %s: %w", namespace, err)
// 	}
//
// 	nsLabels := ns.GetLabels()
// 	if nsLabels == nil {
// 		return fmt.Errorf("namespace %s must have labels", namespace)
// 	}
//
// 	managedBy, hasManaged := nsLabels["llmops.transwarp.io/managed-by"]
// 	if !hasManaged || managedBy != "llmops" {
// 		return fmt.Errorf("namespace %s is not managed by llmops", namespace)
// 	}
//
// 	return nil
// }
//
// // canRerunTask 检查任务是否可以重新运行
// func (ts *TaskManager) canRerunTask(task *model.Task) bool {
// 	// 只有非运行中的任务才能重新运行
// 	switch task.Status {
// 	case model.TaskStatusRunning:
// 		return false
// 	case model.TaskStatusPending:
// 		return false
// 	case model.TaskStatusSucceeded, model.TaskStatusFailed, model.TaskStatusCancelled:
// 		return true
// 	default:
// 		return false
// 	}
// }
//
// // generateTaskID 生成任务ID
// func (ts *TaskManager) generateTaskID(obj *unstructured.Unstructured) string {
// 	// 简化版本，使用名称和时间戳生成ID
// 	return fmt.Sprintf("task-%s-%d", obj.GetName(), time.Now().Unix())
// }
//
// // getResourceFromKind 从Kind获取Resource名称
// func (ts *TaskManager) getResourceFromKind(kind string) string {
// 	resourceMap := map[string]string{
// 		"Job":             "jobs",
// 		"CronJob":         "cronjobs",
// 		"Pod":             "pods",
// 		"Deployment":      "deployments",
// 		"StatefulSet":     "statefulsets",
// 		"ReplicaSet":      "replicasets",
// 		"DaemonSet":       "daemonsets",
// 		"RayCluster":      "rayclusters",
// 		"RayJob":          "rayjobs",
// 		"LeaderWorkerSet": "leaderworkersets",
// 		"AppWrapper":      "appwrappers",
// 		"JobSet":          "jobsets",
// 	}
//
// 	if resource, exists := resourceMap[kind]; exists {
// 		return resource
// 	}
//
// 	// 默认使用小写复数形式
// 	return strings.ToLower(kind) + "s"
// }
//
// // inferTaskTypeFromKind 从Kind推断任务类型
// func (ts *TaskManager) inferTaskTypeFromKind(kind string) model.TaskType {
// 	switch kind {
// 	case "Job", "CronJob":
// 		return model.TaskTypeTraining
// 	case "RayCluster", "RayJob":
// 		return model.TaskTypeTraining
// 	case "Deployment", "StatefulSet":
// 		return model.TaskTypeInference
// 	default:
// 		return model.TaskTypeCustom
// 	}
// }
//
