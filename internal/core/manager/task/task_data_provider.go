package task

import (
	"context"
	"fmt"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/dynamic"
	kueuev1beta1 "sigs.k8s.io/kueue/apis/kueue/v1beta1"

	"github.com/patrickmn/go-cache"

	consts "transwarp.io/mlops/pipeline/internal/core/consts"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
	"transwarp.io/mlops/pipeline/internal/util"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

type TaskDataProvider struct {
	kueueClient   *client.KueueClient
	dynamicClient dynamic.Interface

	k8sClient *client.K8sClientset

	workloadAncestorCache *cache.Cache
}

func NewTaskDataProvider(kueueClient *client.KueueClient) *TaskDataProvider {
	config, err := util.GetKubeConfig()
	if err != nil {
		panic(err)
	}
	dynamicClient, err := dynamic.NewForConfig(config)
	if err != nil {
		panic(err)
	}
	workloadAncestorCache := cache.New(15*time.Minute, 20*time.Minute)
	return &TaskDataProvider{
		kueueClient:           kueueClient,
		dynamicClient:         dynamicClient,
		workloadAncestorCache: workloadAncestorCache,
		k8sClient:             client.MustGetK8sClient(),
	}
}

func (tdp *TaskDataProvider) GetData(ctx context.Context) ([]*model.Task, error) {
	kueueWorkloads, err := tdp.kueueClient.ListWorkloads("")
	if err != nil {
		return nil, fmt.Errorf("failed to list kueue workloads: %w", err)
	}

	tasks, err := tdp.workloadsToTasks(kueueWorkloads)
	if err != nil {
		return nil, err
	}

	return tasks, nil
}

func (tdp *TaskDataProvider) workloadsToTasks(kws []*kueuev1beta1.Workload) ([]*model.Task, error) {
	// from ownerreference find ancestor step by step  using k8s informer reduce api server call
	// 1 find ancestor for each workload
	// 2 group workload by ancestor
	// 3 wl to task useing anceston annotations
	workloadAncestors := make(map[*kueuev1beta1.Workload]metav1.Object)
	for _, kw := range kws {
		obj, err := tdp.findOriginAncestor(kw)
		if err != nil {
			zlog.SugarErrorf("failed to find ancestor for workload %s/%s: %v", kw.Namespace, kw.Name, err)
			continue
		}
		workloadAncestors[kw] = obj
	}

	subTasks, err := tdp.workloadsToSubTasks(kws)
	if err != nil {
		return nil, err
	}

	tasks := make([]*model.Task, 0, len(kws))
	duplicateAncestor := make(map[types.UID]struct{})
	for kw, ancestor := range workloadAncestors {
		uid := ancestor.GetUID()
		if _, exists := duplicateAncestor[uid]; exists {
			zlog.SugarInfof("duplicate ancestor for workload %s/%s", kw.Namespace, kw.Name)
			continue
		}
		duplicateAncestor[uid] = struct{}{}
		task := &model.Task{
			ID:          string(kw.OwnerReferences[0].UID),
			Name:        ancestor.GetAnnotations()[consts.AnnotationTaskNameKey],
			Type:        model.TaskTypeCustom,
			Creator:     ancestor.GetAnnotations()[consts.AnnotationTaskCreatorKey],
			CreateAt:    ancestor.GetCreationTimestamp().Time,
			UpdateAt:    ancestor.GetCreationTimestamp().Time,
			Description: ancestor.GetAnnotations()[consts.AnnotationTaskDescriptionKey],

			TenantID:   ancestor.GetAnnotations()[consts.AnnotationTaskTenantIDKey],
			TenantName: ancestor.GetAnnotations()[consts.AnnotationTaskTenantNameKey],

			ProjectID:   ancestor.GetAnnotations()[consts.AnnotationTaskProjectIDKey],
			ProjectName: ancestor.GetAnnotations()[consts.AnnotationTaskProjectNameKey],
			Priority:    model.TaskPriority(*kw.Spec.Priority),

			QueueName:        string(kw.Spec.QueueName),
			ClusterQueueName: string(kw.Status.Admission.ClusterQueue),

			Resources: nil,
			// TODO
			//Status:    nil,

			SubTasks: nil,

			Namespace: kw.Namespace,
		}

		for _, subTask := range subTasks {
			subTaskAncestor, err := tdp.findOriginAncestor(subTask.WorkloadRef)
			if err != nil {
				return nil, err
			}
			if subTaskAncestor.GetUID() == uid {
				subTask.ParentTaskID = task.ID
				task.SubTasks = append(task.SubTasks, subTask)
			}
		}

		tasks = append(tasks, task)
	}

	return tasks, nil
}
func (tdp *TaskDataProvider) workloadsToSubTasks(kws []*kueuev1beta1.Workload) ([]*model.SubTask, error) {
	subtasks := make([]*model.SubTask, 0, len(kws))
	for _, kw := range kws {
		subTask, err := tdp.workloadToSubTask(kw)
		if err != nil {
			zlog.SugarErrorf("failed to convert workload to subtask: %v", err)
			continue
		}

		subtasks = append(subtasks, subTask)
	}
	return subtasks, nil
}

func (tdp *TaskDataProvider) workloadToSubTask(kw *kueuev1beta1.Workload) (*model.SubTask, error) {
	pods := make([]*model.Pod, 0)
	originPods, err := tdp.findPodsByWorkload(kw)
	if err != nil {
		return nil, err
	}
	for _, originPod := range originPods {
		pod := &model.Pod{
			Name:      originPod.Name,
			Namespace: originPod.Namespace,
			// TODO
			Resources: nil,
		}
		pods = append(pods, pod)
	}
	subTask := &model.SubTask{
		ID:           string(kw.UID),
		Name:         kw.Name,
		ParentTaskID: "",
		WorkloadRef:  kw,
		Status:       model.SubTaskPending,
		CreateAt:     kw.CreationTimestamp.Time,

		// TODO
		Resources: nil,
		Pods:      pods,
	}
	return subTask, nil
}

func (tdp *TaskDataProvider) findPodsByWorkload(kw *kueuev1beta1.Workload) ([]*corev1.Pod, error) {
	// if owner reference contain pod directly, use it
	// else scan pods with the same owner reference
	pods := make([]*corev1.Pod, 0)
	ownerRefs := kw.OwnerReferences
	if len(ownerRefs) == 0 {
		return pods, nil
	}

	for _, ownerRef := range ownerRefs {
		if ownerRef.Kind == "Pod" {
			pod, err := tdp.k8sClient.GetPod(context.Background(), kw.Namespace, ownerRef.Name)
			if err != nil {
				return nil, err
			}
			pods = append(pods, pod)
		}
	}

	if len(pods) == 0 {
		allPods, err := tdp.k8sClient.ListPods(context.Background(), kw.Namespace)
		if err != nil {
			return nil, err
		}
		kwOwner, err := tdp.findOriginAncestor(kw)
		if err != nil {
			return nil, err
		}

		for _, pod := range allPods {
			podOwner, err := tdp.findOriginAncestor(pod)
			if err != nil {
				return nil, err
			}
			if kwOwner.GetUID() == podOwner.GetUID() {
				pods = append(pods, pod)
			}
		}
	}

	return pods, nil
}

func sumQuantities(quantities []resource.Quantity) resource.Quantity {
	total := resource.Quantity{}
	for i := range quantities {
		total.Add(quantities[i])
	}
	return total
}

func (tdp *TaskDataProvider) podResourcestoTaskResources(podTemplateSpec *corev1.PodTemplateSpec) (*model.Resources, error) {
	var totalCPURequests, totalCPULimits []resource.Quantity
	var totalMemoryRequests, totalMemoryLimits []resource.Quantity

	gpuRequests := make(map[string]resource.Quantity)
	gpuLimits := make(map[string]resource.Quantity)

	for _, container := range podTemplateSpec.Spec.Containers {
		for resName, quantity := range container.Resources.Requests {
			switch resName {
			case corev1.ResourceCPU:
				totalCPURequests = append(totalCPURequests, quantity)
			case corev1.ResourceMemory:
				totalMemoryRequests = append(totalMemoryRequests, quantity)
			default:
				currentGPURequest, exists := gpuRequests[string(resName)]
				if exists {
					currentGPURequest.Add(quantity)
					gpuRequests[string(resName)] = currentGPURequest
				} else {
					gpuRequests[string(resName)] = quantity
				}
			}
		}

		for resName, quantity := range container.Resources.Limits {
			switch resName {
			case corev1.ResourceCPU:
				totalCPULimits = append(totalCPULimits, quantity)
			case corev1.ResourceMemory:
				totalMemoryLimits = append(totalMemoryLimits, quantity)
			default:
				currentGPULimit, exists := gpuLimits[string(resName)]
				if exists {
					currentGPULimit.Add(quantity)
					gpuLimits[string(resName)] = currentGPULimit
				} else {
					gpuLimits[string(resName)] = quantity
				}
			}
		}
	}

	cpuRequestTotal := sumQuantities(totalCPURequests)
	memoryRequestTotal := sumQuantities(totalMemoryRequests)
	cpuLimitTotal := sumQuantities(totalCPULimits)
	memoryLimitTotal := sumQuantities(totalMemoryLimits)

	var gpuRequestsList []*model.GPUResource
	for name, quantity := range gpuRequests {
		gpuRequestsList = append(gpuRequestsList, &model.GPUResource{
			Name:      name,
			AliasName: name,
			Count:     quantity.String(),
			Memory:    "",
		})
	}

	var gpuLimitsList []*model.GPUResource
	for name, quantity := range gpuLimits {
		gpuLimitsList = append(gpuLimitsList, &model.GPUResource{
			Name:      name,
			AliasName: name,
			Count:     quantity.String(),
			Memory:    "",
		})
	}

	resources := &model.Resources{
		Requests: model.ResourceSpec{
			CPU:    cpuRequestTotal.String(),
			Memory: memoryRequestTotal.String(),
			GPU:    gpuRequestsList,
		},
		Limits: model.ResourceSpec{
			CPU:    cpuLimitTotal.String(),
			Memory: memoryLimitTotal.String(),
			GPU:    gpuLimitsList,
		},
		Used: nil,
	}

	return resources, nil
}

func (tdp *TaskDataProvider) findOriginAncestor(obj metav1.Object) (metav1.Object, error) {
	cacheKey := string(obj.GetUID())
	if value, found := tdp.workloadAncestorCache.Get(cacheKey); found {
		return value.(metav1.Object), nil
	}

	currentObj := obj
	namespace := obj.GetNamespace()

	for {
		ownerRefs := currentObj.GetOwnerReferences()
		if len(ownerRefs) == 0 {
			break
		}

		ownerRef := ownerRefs[0]
		name := ownerRef.Name
		kind := ownerRef.Kind

		gv, err := schema.ParseGroupVersion(ownerRef.APIVersion)
		if err != nil {
			return nil, fmt.Errorf("failed to parse API version %s: %w", ownerRef.APIVersion, err)
		}

		gvr := schema.GroupVersionResource{
			Group:   gv.Group,
			Version: gv.Version,
			Resource: func(kind string) string {
				switch kind {
				case "Job":
					return "jobs"
				case "Deployment":
					return "deployments"
				case "Pod":
					return "pods"
				case "StatefulSet":
					return "statefulsets"
				case "DaemonSet":
					return "daemonsets"
				case "ReplicaSet":
					return "replicasets"
				default:
					return strings.ToLower(kind) + "s"
				}
			}(kind),
		}
		switch gvr.Resource {
		case "pods":
			obj, err := tdp.k8sClient.GetPod(context.Background(), namespace, name)
			if err != nil {
				return nil, fmt.Errorf("failed to get %s %s/%s: %w", kind, namespace, name, err)
			}
			currentObj = obj
		case "replicasets":
			obj, err := tdp.k8sClient.GetReplicaSet(context.Background(), namespace, name)
			currentObj = obj
			if err != nil {
				return nil, fmt.Errorf("failed to get %s %s/%s: %w", kind, namespace, name, err)
			}
			currentObj = obj
		case "deployments":
			obj, err := tdp.k8sClient.GetDeployment(context.Background(), namespace, name)
			if err != nil {
				return nil, fmt.Errorf("failed to get %s %s/%s: %w", kind, namespace, name, err)
			}
			currentObj = obj
		case "statefulsets":
			obj, err := tdp.k8sClient.GetStatefulSet(context.Background(), namespace, name)
			if err != nil {
				return nil, fmt.Errorf("failed to get %s %s/%s: %w", kind, namespace, name, err)
			}
			currentObj = obj
		default:
			obj, err := tdp.dynamicClient.Resource(gvr).Namespace(namespace).Get(context.Background(), name, metav1.GetOptions{})
			if err != nil {
				return nil, fmt.Errorf("failed to get %s %s/%s: %w", kind, namespace, name, err)
			}
			currentObj = obj
		}
	}

	tdp.workloadAncestorCache.Set(cacheKey, currentObj, cache.DefaultExpiration)
	return currentObj, nil
}

func (tdp *TaskDataProvider) Count(ctx context.Context) (int32, error) {
	tasks, err := tdp.GetData(ctx)
	if err != nil {
		return 0, err
	}
	return int32(len(tasks)), nil
}
