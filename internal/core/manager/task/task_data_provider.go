package task

import (
	"context"

	taskcache "transwarp.io/mlops/pipeline/internal/core/manager/task/cache"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
)

type TaskDataProvider struct {
	taskSnapshot *taskcache.TaskSnapshot
}

func NewTaskDataProvider(kueueClient *client.KueueClient) *TaskDataProvider {
	taskSnapshot := taskcache.NewTaskCache()
	if err := taskSnapshot.Start(context.Background()); err != nil {
		panic(err)
	}
	return &TaskDataProvider{
		taskSnapshot: taskSnapshot,
	}
}

func (tdp *TaskDataProvider) GetData(ctx context.Context) ([]*model.Task, error) {
	return tdp.taskSnapshot.ListTasks()
}

func (tdp *TaskDataProvider) Count(ctx context.Context) (int32, error) {
	tasks, err := tdp.taskSnapshot.ListTasks()
	if err != nil {
		return 0, err
	}
	return int32(len(tasks)), nil
}
