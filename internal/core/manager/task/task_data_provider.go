package task

import (
	"context"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"

	taskcache "transwarp.io/mlops/pipeline/internal/core/manager/task/cache"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
)

type TaskDataProvider struct {
	taskSnapshot *taskcache.TaskSnapshot
}

func NewTaskDataProvider(kueueClient *client.KueueClient) *TaskDataProvider {
	taskSnapshot := taskcache.NewTaskSnapshot()
	if err := taskSnapshot.Start(context.Background()); err != nil {
		panic(err)
	}
	return &TaskDataProvider{
		taskSnapshot: taskSnapshot,
	}
}

func (tdp *TaskDataProvider) GetData(ctx context.Context) ([]*model.Task, error) {
	return tdp.taskSnapshot.GetAllTasks()
}

func sumQuantities(quantities []resource.Quantity) resource.Quantity {
	total := resource.Quantity{}
	for i := range quantities {
		total.Add(quantities[i])
	}
	return total
}

func (tdp *TaskDataProvider) podResourcestoTaskResources(podTemplateSpec *corev1.PodTemplateSpec) (*model.Resources, error) {
	var totalCPURequests, totalCPULimits []resource.Quantity
	var totalMemoryRequests, totalMemoryLimits []resource.Quantity

	gpuRequests := make(map[string]resource.Quantity)
	gpuLimits := make(map[string]resource.Quantity)

	for _, container := range podTemplateSpec.Spec.Containers {
		for resName, quantity := range container.Resources.Requests {
			switch resName {
			case corev1.ResourceCPU:
				totalCPURequests = append(totalCPURequests, quantity)
			case corev1.ResourceMemory:
				totalMemoryRequests = append(totalMemoryRequests, quantity)
			default:
				currentGPURequest, exists := gpuRequests[string(resName)]
				if exists {
					currentGPURequest.Add(quantity)
					gpuRequests[string(resName)] = currentGPURequest
				} else {
					gpuRequests[string(resName)] = quantity
				}
			}
		}

		for resName, quantity := range container.Resources.Limits {
			switch resName {
			case corev1.ResourceCPU:
				totalCPULimits = append(totalCPULimits, quantity)
			case corev1.ResourceMemory:
				totalMemoryLimits = append(totalMemoryLimits, quantity)
			default:
				currentGPULimit, exists := gpuLimits[string(resName)]
				if exists {
					currentGPULimit.Add(quantity)
					gpuLimits[string(resName)] = currentGPULimit
				} else {
					gpuLimits[string(resName)] = quantity
				}
			}
		}
	}

	cpuRequestTotal := sumQuantities(totalCPURequests)
	memoryRequestTotal := sumQuantities(totalMemoryRequests)
	cpuLimitTotal := sumQuantities(totalCPULimits)
	memoryLimitTotal := sumQuantities(totalMemoryLimits)

	var gpuRequestsList []*model.GPUResource
	for name, quantity := range gpuRequests {
		gpuRequestsList = append(gpuRequestsList, &model.GPUResource{
			Name:      name,
			AliasName: name,
			Count:     quantity.String(),
			Memory:    "",
		})
	}

	var gpuLimitsList []*model.GPUResource
	for name, quantity := range gpuLimits {
		gpuLimitsList = append(gpuLimitsList, &model.GPUResource{
			Name:      name,
			AliasName: name,
			Count:     quantity.String(),
			Memory:    "",
		})
	}

	resources := &model.Resources{
		Requests: model.ResourceSpec{
			CPU:    cpuRequestTotal.String(),
			Memory: memoryRequestTotal.String(),
			GPU:    gpuRequestsList,
		},
		Limits: model.ResourceSpec{
			CPU:    cpuLimitTotal.String(),
			Memory: memoryLimitTotal.String(),
			GPU:    gpuLimitsList,
		},
		Used: nil,
	}

	return resources, nil
}

func (tdp *TaskDataProvider) Count(ctx context.Context) (int32, error) {
	tasks, err := tdp.taskSnapshot.GetAllTasks()
	if err != nil {
		return 0, err
	}
	return int32(len(tasks)), nil
}
