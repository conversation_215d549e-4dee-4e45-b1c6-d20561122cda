component_advanced_config: {}
component_source:
  template_type: 1
container:
  cmds:
  - python3
  - batch-task/batch_task.py
  args:
  - --service-id
  - '{{inputs.parameters.service-id}}'
  - --port
  - '{{inputs.parameters.port}}'
  - --endpoint
  - '{{inputs.parameters.endpoint}}'
  - --workers
  - '{{inputs.parameters.workers}}'
  image: batch-task
  mount_paths:
  - mount_path: /data/nfs/
    volume_name: sfs-volume
  resource_requirements:
    resource_limit:
      cpu: 2000
      memory: 2048
    resource_request:
      cpu: 1000
      memory: 1024
  security_context: {}
desc: 批量预测
desc_en: batch prediction
id: C00002
inputs:
  input: /artifact/input0
name: 批量预测
name_en: batch prediction
outputs:
  output: /artifact/output0
params:
  service-id: ''
  port: ''
  endpoint: ''
  workers: '2'
volume:
- name: sfs-volume
  volume_type: 3
