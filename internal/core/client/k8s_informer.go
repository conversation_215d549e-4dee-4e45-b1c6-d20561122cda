package client

import (
	"context"
	"encoding/json"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/informers"
	applisterv1 "k8s.io/client-go/listers/apps/v1"
	corelisterv1 "k8s.io/client-go/listers/core/v1"
	"k8s.io/client-go/tools/cache"
	"sigs.k8s.io/yaml"
	"strings"
	"time"
)

const (
	Pod      = "Pod"
	Workflow = "Workflow"
)

type K8sInformerClient struct {
	PodLister       corelisterv1.PodLister
	DeployLister    applisterv1.DeploymentLister
	SvcLister       corelisterv1.ServiceLister
	EventLister     corelisterv1.EventLister
	ConfigMapLister corelisterv1.ConfigMapLister
	PodInformer     cache.SharedInformer
}

func (k *K8sInformerClient) Init() error {
	sharedInformerFactory := informers.NewSharedInformerFactory(K8sClient.Client, time.Minute*10)
	k.PodInformer = sharedInformerFactory.Core().V1().Pods().Informer()
	k.PodLister = sharedInformerFactory.Core().V1().Pods().Lister()
	k.SvcLister = sharedInformerFactory.Core().V1().Services().Lister()
	k.DeployLister = sharedInformerFactory.Apps().V1().Deployments().Lister()
	k.EventLister = sharedInformerFactory.Core().V1().Events().Lister()
	k.ConfigMapLister = sharedInformerFactory.Core().V1().ConfigMaps().Lister()
	stopCh := make(chan struct{})
	sharedInformerFactory.Start(stopCh)
	return nil
}

func (k *K8sInformerClient) AddWatchHandler(handler cache.ResourceEventHandler) {
	k.PodInformer.AddEventHandler(handler)
}

func (k *K8sInformerClient) GetPodInfo(ctx context.Context, namespace string, podName string) ([]byte, error) {
	pod, err := k.PodLister.Pods(namespace).Get(podName)
	if err != nil {
		return nil, err
	}
	return pod2Yaml(pod)
}

func (k *K8sInformerClient) GetConfigMapInfo(ctx context.Context, namespace string, cmName string) (*v1.ConfigMap, error) {
	cm, err := k.ConfigMapLister.ConfigMaps(namespace).Get(cmName)
	if err != nil {
		return nil, err
	}
	return cm, nil
}

func pod2Yaml(pod *v1.Pod) ([]byte, error) {
	json, _ := json.Marshal(pod)
	yaml, _ := yaml.JSONToYAML(json)
	return yaml, nil
}

func (k *K8sInformerClient) GetPodEvents(ctx context.Context, namespace, runId, podName string) ([]*v1.Event, error) {
	res := make([]*v1.Event, 0)
	event, err := k.EventLister.Events(namespace).List(labels.Everything())
	if err != nil {
		return nil, err
	}
	for _, e := range event {
		if !checkEventKind(e, runId, podName) {
			continue
		}
		res = append(res, e)
	}
	return res, nil
}

func checkEventKind(event *v1.Event, runId, podName string) bool {
	if event == nil {
		return false
	}

	// workflow
	if len(runId) != 0 && Workflow == event.InvolvedObject.Kind && strings.Contains(event.InvolvedObject.Name, runId) {
		return true
	}

	// pod
	if len(podName) != 0 && Pod == event.InvolvedObject.Kind && strings.Contains(event.InvolvedObject.Name, podName) {
		return true
	}

	// pod workflow
	if len(runId) != 0 && Pod == event.InvolvedObject.Kind && strings.Contains(event.InvolvedObject.Name, runId) {
		return true
	}

	return false
}

func (k *K8sInformerClient) GetServiceByName(serviceName, namespace string) (*v1.Service, error) {
	svc, err := k.SvcLister.Services(namespace).Get(serviceName)
	if err != nil {
		return nil, err
	}
	return svc, nil
}

func event2Yaml(event []*v1.Event) ([]byte, error) {
	json, _ := json.Marshal(event)
	yaml, _ := yaml.JSONToYAML(json)
	return yaml, nil
}
