package core

import (
	"context"
	"fmt"

	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	"transwarp.io/mlops/mlops-std/stderr"
	"transwarp.io/mlops/pipeline/internal/core/client"
	"transwarp.io/mlops/pipeline/internal/core/model"
	"transwarp.io/mlops/pipeline/internal/dao"
)

type IPipelineVersionManager interface {
	CreatePipelineVersion(ctx context.Context, pipelineVersionReq *model.PipelineVersion) (string, error)
	UpdatePipelineVersion(ctx context.Context, pipelineVersionReq *model.PipelineVersion) (string, error)
	GetPipelineVersion(ctx context.Context, id string) (*model.PipelineVersion, error)
	DeletePipelineVersion(ctx context.Context, id string) error
	DeletePipelineVersions(ctx context.Context, ids []string) error
	ListPipelineVersions(ctx context.Context, pipelineIds ...string) ([]*pb.PipelineVersion, error)
	StartPipelineVersion(ctx context.Context, id string) (string, error)
	StopPipelineVersion(ctx context.Context, id string) error
	OncePipelineVersion(ctx context.Context, id string) (string, error)
	ExportPipelineVersionYaml(ctx context.Context, id string) (*pb.ExportPipelineVersionRsp, error)
	CreatePipelineVersionByYaml(ctx context.Context, req *pb.CreatePipelineVersionByYamlReq) (string, error)
	CheckPipelineVersionNameUnique(ctx context.Context, name string, pipelineId string) error
}

type PipelineVersionManager struct{}

func NewPipelineVersionManager() *PipelineVersionManager {
	return &PipelineVersionManager{}
}

func (m *PipelineVersionManager) CheckPipelineVersionNameUnique(ctx context.Context, name string, pipelineId string) error {
	count, err := dao.PipelineVersionDAOInst.CountByNameAndPipelineId(name, pipelineId)
	if err != nil {
		return err
	}
	if count > 0 {
		return stderr.PipelineVersionNameExist.Error()
	}
	return nil
}

func (m *PipelineVersionManager) GetPipelineVersion(ctx context.Context, id string) (*model.PipelineVersion, error) {
	entity, err := dao.PipelineVersionDAOInst.GetById(id)
	if err != nil {
		return nil, err
	}
	pipelineVersion := (&model.PipelineVersion{}).FromModel(entity)

	err = m.completePipelineVersionWithState(ctx, pipelineVersion)
	return pipelineVersion, err
}

func (m *PipelineVersionManager) completePipelineVersionWithState(ctx context.Context, pipelineVersion *model.PipelineVersion) error {
	if pipelineVersion.SchedulingStyle == pb.PipelineVersion_IMMEDIATE {
		return nil
	}
	pipelineVersion.TimingConfig.Status = pb.TimingConfig_Disable

	jobs, err := dao.JobDAOInst.ListByNameAndEnabled(ctx, pipelineVersion.Id, true)
	if err != nil {
		return err
	}
	if len(jobs) > 0 {
		pipelineVersion.TimingConfig.Status = pb.TimingConfig_Enable
	}
	return nil
}

func (m *PipelineVersionManager) completePipelineVersionsWithState(ctx context.Context, versions []*pb.PipelineVersion) error {
	jobs, err := dao.JobDAOInst.ListByEnabled(ctx, true)
	if err != nil {
		return err
	}
	jobStates := map[string]bool{}
	for _, job := range jobs {
		jobStates[job.DisplayName] = true
	}
	for _, version := range versions {
		if jobStates[version.Id] {
			version.TimingConfig.Status = pb.TimingConfig_Enable
		}
	}
	return nil
}

func (m *PipelineVersionManager) CreatePipelineVersion(ctx context.Context, pipelineVersion *model.PipelineVersion) (string, error) {
	size, err := dao.PipelineVersionDAOInst.CountByNameAndPipelineId(pipelineVersion.Name, pipelineVersion.PipelineId)
	if err != nil {
		return "", err
	}
	if size > 0 {
		return "", stderr.PipelineVersionNameExist.Error()
	}
	_, err = PipelineMgr.GetPipeline(ctx, pipelineVersion.PipelineId)
	if err != nil {
		return "", err
	}

	pipelineVersionId, err := dao.PipelineVersionDAOInst.Create(pipelineVersion.ToModel())
	if err != nil {
		return "", err
	}
	return pipelineVersionId, nil
}

func (m *PipelineVersionManager) UpdatePipelineVersion(ctx context.Context, pipelineVersion *model.PipelineVersion) (string, error) {
	_, err := dao.PipelineVersionDAOInst.GetById(pipelineVersion.Id)
	if err != nil {
		return "", err
	}
	_, err = PipelineMgr.GetPipeline(ctx, pipelineVersion.PipelineId)
	if err != nil {
		return "", err
	}

	if pipelineVersion.SchedulingStyle == pb.PipelineVersion_IMMEDIATE {
		err = m.StopPipelineVersion(ctx, pipelineVersion.Id)
		if err != nil {
			return "", err
		}
	}

	id, err := dao.PipelineVersionDAOInst.Save(pipelineVersion.ToModel())
	if pipelineVersion.SchedulingStyle == pb.PipelineVersion_TIMING {
		_, err = m.StartPipelineVersion(ctx, pipelineVersion.Id)
		if err != nil {
			return "", err
		}
	}
	if err != nil {
		return "", err
	}
	return id, nil
}

func (m *PipelineVersionManager) ListPipelineVersions(ctx context.Context, pipelineIds ...string) ([]*pb.PipelineVersion, error) {
	entities, err := dao.PipelineVersionDAOInst.List(pipelineIds...)
	if err != nil {
		return nil, err
	}

	versions := make([]*pb.PipelineVersion, 0)
	for _, entry := range entities {
		version := (&model.PipelineVersion{}).FromModel(entry)
		versions = append(versions, version.ToPb())
	}

	err = m.completePipelineVersionsWithState(ctx, versions)
	if err != nil {
		return nil, err
	}
	return versions, nil
}

func (m *PipelineVersionManager) DeletePipelineVersion(ctx context.Context, id string) error {
	_, err := m.GetPipelineVersion(ctx, id)
	if err != nil {
		return err
	}
	runs, _, err := RunMgr.ListRun(ctx, "", &pb.ListRunsReq_Filter{
		SourceId: []string{id},
	}, &pb.PageReq{})
	if err != nil {
		return fmt.Errorf("list pipeline version runs: %w", err)
	}
	// 运行中的不能删除
	for _, run := range runs {
		if run.Status == pb.Run_Running {
			return stderr.DeleteRunningPipelineTask.Error()
		}
	}
	// 删除 job
	jobs, err := dao.JobDAOInst.ListByName(ctx, id)
	if err != nil {
		return err
	}
	ids := make([]string, 0, len(jobs))
	for _, job := range jobs {
		ids = append(ids, job.UUID)
	}
	err = client.PipelineClientInst.DeletePipelineJob(ctx, ids)
	if err != nil {
		return err
	}
	// 删除 runs
	for _, run := range runs {
		err = RunMgr.DeleteRun(ctx, run.Id)
		if err != nil {
			return err
		}
	}
	return dao.PipelineVersionDAOInst.Delete(id)
}

func (m *PipelineVersionManager) DeletePipelineVersions(ctx context.Context, ids []string) error {
	for _, id := range ids {
		_, err := dao.PipelineVersionDAOInst.GetById(id)
		if err != nil {
			return err
		}
	}
	runs, _, err := RunMgr.ListRun(ctx, "", &pb.ListRunsReq_Filter{
		SourceId: ids,
	}, &pb.PageReq{})
	if err != nil {
		return fmt.Errorf("list pipeline version runs: %w", err)
	}
	// 运行中的不能删除
	for _, run := range runs {
		if run.Status == pb.Run_Running {
			return stderr.DeleteRunningPipelineTask.Error()
		}
	}
	// 删除 job
	jobs, err := dao.JobDAOInst.ListByName(ctx, ids...)
	if err != nil {
		return err
	}
	jobids := make([]string, 0, len(jobs))
	for _, job := range jobs {
		jobids = append(jobids, job.UUID)
	}
	err = client.PipelineClientInst.DeletePipelineJob(ctx, jobids)
	if err != nil {
		return err
	}
	// 删除 runs
	for _, run := range runs {
		err = RunMgr.DeleteRun(ctx, run.Id)
		if err != nil {
			return err
		}
	}
	return dao.PipelineVersionDAOInst.DeleteByIds(ids)
}

func (m *PipelineVersionManager) CreatePipelineVersionByYaml(ctx context.Context, req *pb.CreatePipelineVersionByYamlReq) (string, error) {
	pipelineVersion := &model.PipelineVersion{}
	pipelineVersion, err := pipelineVersion.FromYaml(ctx, []byte(req.CreatePipelineVersionByYamlBody.Yaml))
	if err != nil {
		return "", err
	}
	pipelineVersion.Name = req.CreatePipelineVersionByYamlBody.Name
	pipelineVersion.Desc = req.CreatePipelineVersionByYamlBody.Desc
	pipelineVersion.PipelineId = req.CreatePipelineVersionByYamlBody.PipelineId
	return m.CreatePipelineVersion(ctx, pipelineVersion)
}

func (m *PipelineVersionManager) ExportPipelineVersionYaml(ctx context.Context, id string) (*pb.ExportPipelineVersionRsp, error) {
	pipelineVersion, err := m.GetPipelineVersion(ctx, id)
	if err != nil {
		return nil, err
	}
	pipeline, err := PipelineMgr.GetPipeline(ctx, pipelineVersion.PipelineId)
	if err != nil {
		return nil, err
	}
	project, err := client.CasCli.GetProjectDetailById(ctx, pipeline.ProjectId)
	if err != nil {
		return nil, err
	}
	yaml, err := pipelineVersion.ToYaml(ctx, pipeline, project.TenantUID)
	if err != nil {
		return nil, err
	}
	return &pb.ExportPipelineVersionRsp{
		PipelineVersionFile: yaml,
		FileName:            pipelineVersion.Name,
	}, nil
}

func (m *PipelineVersionManager) OncePipelineVersion(ctx context.Context, id string) (string, error) {
	version, err := m.GetPipelineVersion(ctx, id)
	if err != nil {
		return "", err
	}
	pipeline, err := PipelineMgr.GetPipeline(ctx, version.PipelineId)
	if err != nil {
		return "", err
	}
	run := &model.Run{
		ProjectId: pipeline.ProjectId,
		TaskSource: &pb.TaskSource{
			SourceType: pb.TaskSourceType_PIPELINE,
			SourceId:   version.Id,
			SourceName: version.Name,
			SourceInfo: "",
		},
		TaskFlow: version.TaskFlow,
	}
	runId, err := RunMgr.SubmitRun(ctx, run)
	if err != nil {
		return "", err
	}
	return runId, nil
}

func (m *PipelineVersionManager) StartPipelineVersion(ctx context.Context, id string) (string, error) {
	pipelineVersion, err := m.GetPipelineVersion(ctx, id)
	if err != nil {
		return "", err
	}
	pipeline, err := PipelineMgr.GetPipeline(ctx, pipelineVersion.PipelineId)
	if err != nil {
		return "", err
	}
	if pipelineVersion.SchedulingStyle != pb.PipelineVersion_TIMING || pipelineVersion.TimingConfig == nil {
		return "", stderr.StartImmediatePipelineVersion.Error()
	}
	err = m.StopPipelineVersion(ctx, pipelineVersion.Id)
	if err != nil {
		return "", err
	}
	experiment, err := getOrCreateExperiment(ctx, pipeline.ProjectId)
	if err != nil {
		return "", err
	}
	project, err := client.CasCli.GetProjectDetailById(ctx, pipeline.ProjectId)
	if err != nil {
		return "", err
	}
	return client.PipelineClientInst.StartPipelineVersion(ctx, pipeline, pipelineVersion, experiment, project.TenantUID)
}

func (m *PipelineVersionManager) StopPipelineVersion(ctx context.Context, id string) error {
	pipelineVersion, err := m.GetPipelineVersion(ctx, id)
	if err != nil {
		return err
	}
	if pipelineVersion.SchedulingStyle != pb.PipelineVersion_TIMING || pipelineVersion.TimingConfig == nil {
		return nil
	}
	jobs, err := dao.JobDAOInst.ListByName(ctx, id)
	if err != nil {
		return err
	}

	disableIds := make([]string, 0)
	for _, job := range jobs {
		disableIds = append(disableIds, job.UUID)
	}
	err = client.PipelineClientInst.DisablePipelineJob(ctx, disableIds)
	if err != nil {
		return err
	}

	jobRunningCount, err := dao.JobDAOInst.CountRunningRun()
	if err != nil {
		return err
	}
	deleteIds := make([]string, 0)
	for _, job := range jobs {
		if jobRunningCount[job.UUID] == 0 {
			deleteIds = append(deleteIds, job.UUID)
		}
	}
	err = client.PipelineClientInst.DeletePipelineJob(ctx, deleteIds)
	if err != nil {
		return err
	}
	return nil
}
