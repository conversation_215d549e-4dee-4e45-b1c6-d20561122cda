component_advanced_config: {}
component_source:
  template_type: 1
container:
  cmds:
  - python3
  - corpus-storage/corpus_storage.py
  args:
  - --task_template_id
  - '{{inputs.parameters.task_template_id}}'
  - --asset-id
  - '{{inputs.parameters.asset_id}}'
  - --asset-version-id
  - '{{inputs.parameters.asset_version_id}}'
  image: corpus-storage
  mount_paths:
  - mount_path: /data/nfs/
    volume_name: sfs-volume
  resource_requirements:
    resource_limit:
      cpu: 2000
      memory: 2048
    resource_request:
      cpu: 1000
      memory: 1024
  security_context: {}
desc: 知识加工
desc_en: knowledge processing
id: C00004
inputs:
  task_template_id: /artifact/input0
name: 知识加工
name_en: knowledge processing
outputs:
  task_id: /artifact/output0
params:
  task_template_id: ''
event_params:
  asset_id: 'body.assetId'
  asset_version_id: 'body.versionId'
volume:
- name: sfs-volume
  volume_type: 3
