annotations: null
component_advanced_config:
  enable_cached: false
  parallelism: 0
  retry_strategy: null
  service_account_name: ""
component_source:
  source_id: ""
  source_info: ""
  source_name: ""
  template_type: 1
container:
  args: []
  cmds: null
  container_type: 0
  envs: null
  image: ""
  image_pull_policy: 0
  lifecycle: null
  livenessProbe: null
  mount_paths: null
  name: ""
  ports: null
  readinessProbe: null
  resource_id: null
  resource_requirements: null
  security_context:
    privileged: false
    run_as_user: 0
desc: 自定义组件
desc_en: batch prediction
host_network: false
id: C00000
inputs: null
labels: null
name: 自定义组件
name_en: custom component
node_selector: null
outputs: null
params: null
use_defined: true
volume: null
