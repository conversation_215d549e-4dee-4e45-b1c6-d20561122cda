package core

import (
	"transwarp.io/mlops/pipeline/internal/core/task"
)

var PipelineMgr IPipelineManager
var PipelineVersionMgr IPipelineVersionManager
var ComponentMgr IComponentManager
var RunMgr IRunManager
var TaskManager task.TaskManager

func Init() {
	PipelineMgr = NewPipelineManager()
	PipelineVersionMgr = NewPipelineVersionManager()
	RunMgr = NewRunManager()
	ComponentMgr = NewComponentManager()

	TaskManager = *task.NewTaskManager()
}
