package task

import (
	"context"
	"fmt"

	"transwarp.io/mlops/mlops-std/stdlog"
	"transwarp.io/mlops/pipeline/internal/core/task/model"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
)

type TaskManager struct {
	kclient *client.KueueClient
}

func NewTaskManager() *TaskManager {
	return &TaskManager{
		kclient: client.NewKueueClient(),
	}
}

// ListWorkloads 获取工作负载列表
func (ws *TaskManager) ListTasks(ctx context.Context, req *model.ListTasksReq) (*model.ListTasksResp, error) {
	stdlog.Info(fmt.Sprintf("Listing workloads with request: %+v", req))

	// 获取Kueue Workloads
	kueueWorkloads, err := ws.kclient.ListWorkloads("")
	if err != nil {
		return nil, fmt.Errorf("failed to list kueue workloads: %w", err)
	}

	var tasks []*Task
	for _, kw := range kueueWorkloads {
		workload := FromKueueWorkload(kw)

		// 应用过滤器
		// if ws.matchesFilter(workload, req) {
		// 	workloads = append(workloads, workload)
		// }
	}

	// // 排序
	// ws.sortWorkloads(workloads, req.SortBy, req.SortOrder)

	total := len(tasks)

	start := int((req.Page - 1) * req.PageSize)
	end := start + int(req.PageSize)

	if start >= len(tasks) {
		tasks = []*model.Task{}
	} else {
		if end > len(tasks) {
			end = len(tasks)
		}
		tasks = tasks[start:end]
	}

	return &model.ListTasksResp{
		Items:      tasks,
		Total:      int32(total),
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: int32((total + int(req.PageSize) - 1) / int(req.PageSize)),
	}, nil
}
