package query

import (
	"k8s.io/apimachinery/pkg/labels"
)

// QueryOptions 查询选项
type QueryOptions struct {
	// 分页
	Page     int32
	PageSize int32

	// 排序
	SortBy    string
	SortOrder string // asc/desc

	// 过滤
	Filters []Filter

	// 标签选择器
	LabelSelector labels.Selector
}

// Filter 过滤接口
type Filter interface {
	Apply(tasks []*Task) []*Task
}

// 常用过滤器实现
type StatusFilter struct {
	Status []string
}

type ProjectFilter struct {
	ProjectName string
	ProjectID   string
}

type TypeFilter struct {
	Types []string
}

type TimeRangeFilter struct {
	Start int64
	End   int64
}
