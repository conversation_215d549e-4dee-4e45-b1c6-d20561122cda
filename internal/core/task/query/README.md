# Kueue Workload 查询框架

这是一个灵活、高效的工作负载查询框架，类似于数据库查询的方式，支持复杂的条件组合、分页、排序和缓存。

## 特性

- 🔍 **灵活的查询条件组合** - 支持AND/OR逻辑组合
- 📄 **多种分页方式** - 支持偏移分页、游标分页
- 🔄 **多字段排序** - 支持多字段组合排序
- 🏷️ **标签选择器** - 支持Kubernetes风格的标签查询
- ⚡ **查询缓存** - 内置多级缓存机制
- 📊 **性能监控** - 查询指标和慢查询检测
- 🧪 **易于测试** - 支持单元测试和集成测试
- 🎯 **类型安全** - 基于Go泛型的类型安全设计

## 快速开始

### 基础查询

```go
// 查询所有运行中的工作负载
runningWorkloads := query.NewWorkloadQuery().
    WhereRunning().
    OrderByCreateTime(query.SortDesc)

result, err := workloadService.QueryWorkloads(ctx, runningWorkloads)
```

### 复杂过滤

```go
// 查询高优先级的GPU训练任务
gpuTrainingJobs := query.NewWorkloadQuery().
    WhereType(workload.WorkloadTypeTraining).
    WhereHasGPU().
    HighPriority().
    Recent()

result, err := workloadService.QueryWorkloads(ctx, gpuTrainingJobs)
```

### 时间范围查询

```go
// 查询最近24小时内创建的工作负载
yesterday := time.Now().Add(-24 * time.Hour)
recentWorkloads := query.NewWorkloadQuery().
    WhereCreatedAfter(yesterday).
    OrderByCreateTime(query.SortDesc).
    Limit(1, 10)

result, err := workloadService.QueryWorkloads(ctx, recentWorkloads)
```

### 分页查询

```go
// 分页查询
pageSize := int32(20)
page := int32(1)

pagedQuery := query.NewWorkloadQuery().
    WhereProject("ml-platform").
    OrderByCreateTime(query.SortDesc).
    Limit(page, pageSize)

result, err := workloadService.QueryWorkloads(ctx, pagedQuery)
fmt.Printf("第%d页，共%d页，总计%d条\n", result.Page, result.TotalPages, result.Total)
```

## 查询构建器 API

### 基础过滤方法

```go
query := query.NewWorkloadQuery()

// 命名空间过滤
query.WhereNamespace("default")

// 项目过滤
query.WhereProject("ml-platform")
query.WhereProjects([]string{"project1", "project2"})

// 状态过滤
query.WhereStatus(workload.StatusRunning)
query.WhereStatuses([]workload.WorkloadStatus{
    workload.StatusRunning,
    workload.StatusPending,
})

// 类型过滤
query.WhereType(workload.WorkloadTypeTraining)
query.WhereTypes([]workload.WorkloadType{
    workload.WorkloadTypeTraining,
    workload.WorkloadTypeInference,
})

// 优先级过滤
query.WherePriority(workload.PriorityHigh)
query.WherePriorities([]workload.WorkloadPriority{
    workload.PriorityHigh,
    workload.PriorityUrgent,
})

// 创建者过滤
query.WhereCreator("<EMAIL>")

// 队列过滤
query.WhereQueueName("gpu-queue")
```

### 时间范围过滤

```go
// 创建时间过滤
query.WhereCreatedAfter(time.Now().Add(-24 * time.Hour))
query.WhereCreatedBefore(time.Now())
query.WhereCreatedBetween(startTime, endTime)

// 开始时间过滤
query.WhereStartedAfter(time.Now().Add(-1 * time.Hour))
query.WhereStartedBefore(time.Now())
query.WhereStartedBetween(startTime, endTime)
```

### 状态相关过滤

```go
// 预定义状态过滤器
query.WherePending()     // 等待中
query.WhereRunning()     // 运行中
query.WhereCompleted()   // 已完成（成功或失败）
query.WhereActive()      // 活跃的（未完成）
query.WhereTerminated() // 已终止
```

### 资源相关过滤

```go
// 资源过滤
query.WhereHasGPU()                    // 有GPU资源
query.WhereCPUGreaterThan(4000)        // CPU > 4核
query.WhereMemoryGreaterThan(8*1024)   // 内存 > 8GB
```

### 标签过滤

```go
// 标签过滤
query.WhereHasLabel("environment")
query.WhereLabelEquals("environment", "production")
query.WhereLabelMatches("environment in (production,staging)")
```

### 搜索过滤

```go
// 关键词搜索
query.WhereNameContains("training")
query.WhereDescriptionContains("model")
query.WhereKeywordSearch("pytorch")  // 在名称和描述中搜索
```

### 排序方法

```go
// 预定义排序
query.OrderByName(query.SortAsc)
query.OrderByCreateTime(query.SortDesc)
query.OrderByStartTime(query.SortAsc)
query.OrderByPriority(query.SortDesc)
query.OrderByStatus(query.SortAsc)
query.OrderByDuration(query.SortDesc)

// 便捷方法
query.Recent()      // 最近创建的
query.Oldest()      // 最早创建的
query.HighPriority() // 高优先级
```

### 自定义过滤和排序

```go
// 自定义过滤器
query.WhereFunc(func(wl *workload.Workload) bool {
    return wl.CalculateDuration() > time.Hour
})

// 自定义排序
query.OrderByFunc(func(a, b *workload.Workload) bool {
    return a.CalculateDuration() > b.CalculateDuration()
})
```

## 预定义查询

```go
// 预定义查询方法
allWorkloads := query.AllWorkloads()
pendingWorkloads := query.PendingWorkloads()
runningWorkloads := query.RunningWorkloads()
completedWorkloads := query.CompletedWorkloads()
activeWorkloads := query.ActiveWorkloads()
recentWorkloads := query.RecentWorkloads(10)
projectWorkloads := query.WorkloadsByProject("ml-platform")
creatorWorkloads := query.WorkloadsByCreator("<EMAIL>")
queueWorkloads := query.WorkloadsByQueue("gpu-queue")
gpuWorkloads := query.GPUWorkloads()
```

## 复合查询

```go
// 使用复合过滤器
statusFilter := query.NewCompositeFilter[*workload.Workload](
    query.LogicOr,
    query.NewFieldFilter[*workload.Workload]("Status", query.OpEqual, workload.StatusRunning),
    query.NewFieldFilter[*workload.Workload]("Status", query.OpEqual, workload.StatusPending),
)

complexQuery := query.NewWorkloadQuery().
    Where(statusFilter).
    WhereType(workload.WorkloadTypeTraining).
    OrderByPriority(query.SortDesc)
```

## 统计查询

```go
// 统计数量
count, err := workloadService.CountWorkloads(ctx, query)

// 检查存在性
exists, err := workloadService.ExistsWorkload(ctx, query)

// 获取第一个
first, err := workloadService.FindFirstWorkload(ctx, query)
```

## 性能优化

### 缓存配置

```go
// 配置查询执行器
config := &query.ExecutorConfig{
    EnableCache:        true,
    CacheTTL:          5 * time.Minute,
    MaxCacheSize:      1000,
    QueryTimeout:      30 * time.Second,
    EnableMetrics:     true,
    SlowQueryThreshold: 1 * time.Second,
    MaxConcurrency:    10,
}
```

### 查询指标

```go
// 获取查询指标
metrics := workloadService.GetQueryMetrics()
fmt.Printf("总查询数: %d\n", metrics.TotalQueries)
fmt.Printf("缓存命中率: %.2f%%\n", float64(metrics.CacheHits)/float64(metrics.TotalQueries)*100)
fmt.Printf("平均查询时间: %s\n", metrics.AverageTime)

// 获取缓存统计
cacheStats := workloadService.GetCacheStats()
fmt.Printf("缓存命中率: %.2f%%\n", cacheStats.HitRate*100)
```

## 最佳实践

1. **使用预定义查询** - 优先使用预定义的查询方法
2. **合理使用缓存** - 对于频繁查询的数据启用缓存
3. **避免过度分页** - 限制页大小，避免一次查询过多数据
4. **使用索引字段排序** - 优先使用有索引的字段进行排序
5. **监控慢查询** - 定期检查慢查询并优化
6. **合理设置超时** - 为查询设置合理的超时时间

## 扩展性

框架支持轻松扩展新的过滤器和排序器：

```go
// 自定义过滤器
type CustomFilter[T any] struct {
    // 自定义字段
}

func (cf *CustomFilter[T]) Match(item T) bool {
    // 自定义匹配逻辑
    return true
}

func (cf *CustomFilter[T]) String() string {
    return "custom_filter"
}
```

这个查询框架提供了强大而灵活的工作负载查询能力，支持复杂的业务场景，同时保持了良好的性能和可维护性。
