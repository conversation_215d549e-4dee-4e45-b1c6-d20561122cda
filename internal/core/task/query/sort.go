package query

import (
	"transwarp.io/mlops/pipeline/internal/core/task/model"
)

type TaskSorter struct {
	tasks     []*model.Task
	sortBy    string
	sortOrder string
}

func (s *TaskSorter) Len() int      { return len(s.tasks) }
func (s *TaskSorter) Swap(i, j int) { s.tasks[i], s.tasks[j] = s.tasks[j], s.tasks[i] }
func (s *TaskSorter) Less(i, j int) bool {
	if s.sortOrder == "desc" {
		i, j = j, i
	}

	switch s.sortBy {
	case "create_at":
		return s.tasks[i].CreateAt.Before(s.tasks[j].CreateAt)
	case "priority":
		return s.tasks[i].Priority < s.tasks[j].Priority
	default:
		return s.tasks[i].Name < s.tasks[j].Name
	}
}
