package query

import (
	"container/list"
	"sync"
	"time"
)

// MemoryCache 内存缓存实现
type MemoryCache[T any] struct {
	maxSize   int
	items     map[string]*cacheItem[T]
	lruList   *list.List
	mu        sync.RWMutex
	hits      int64
	misses    int64
	evictions int64
}

// cacheItem 缓存项
type cacheItem[T any] struct {
	key        string
	value      *QueryResult[T]
	expireTime time.Time
	element    *list.Element
}

// NewMemoryCache 创建内存缓存
func NewMemoryCache[T any](maxSize int) *MemoryCache[T] {
	if maxSize <= 0 {
		maxSize = 1000
	}
	
	return &MemoryCache[T]{
		maxSize: maxSize,
		items:   make(map[string]*cacheItem[T]),
		lruList: list.New(),
	}
}

// Get 获取缓存项
func (mc *MemoryCache[T]) Get(key string) (*QueryResult[T], bool) {
	mc.mu.Lock()
	defer mc.mu.Unlock()
	
	item, exists := mc.items[key]
	if !exists {
		mc.misses++
		return nil, false
	}
	
	// 检查是否过期
	if time.Now().After(item.expireTime) {
		mc.removeItem(item)
		mc.misses++
		return nil, false
	}
	
	// 移动到LRU列表前端
	mc.lruList.MoveToFront(item.element)
	mc.hits++
	
	return item.value, true
}

// Set 设置缓存项
func (mc *MemoryCache[T]) Set(key string, result *QueryResult[T], ttl time.Duration) {
	mc.mu.Lock()
	defer mc.mu.Unlock()
	
	expireTime := time.Now().Add(ttl)
	
	// 如果已存在，更新
	if existingItem, exists := mc.items[key]; exists {
		existingItem.value = result
		existingItem.expireTime = expireTime
		mc.lruList.MoveToFront(existingItem.element)
		return
	}
	
	// 检查是否需要驱逐
	if len(mc.items) >= mc.maxSize {
		mc.evictLRU()
	}
	
	// 创建新项
	element := mc.lruList.PushFront(key)
	item := &cacheItem[T]{
		key:        key,
		value:      result,
		expireTime: expireTime,
		element:    element,
	}
	
	mc.items[key] = item
}

// Delete 删除缓存项
func (mc *MemoryCache[T]) Delete(key string) {
	mc.mu.Lock()
	defer mc.mu.Unlock()
	
	if item, exists := mc.items[key]; exists {
		mc.removeItem(item)
	}
}

// Clear 清空缓存
func (mc *MemoryCache[T]) Clear() {
	mc.mu.Lock()
	defer mc.mu.Unlock()
	
	mc.items = make(map[string]*cacheItem[T])
	mc.lruList.Init()
}

// Stats 获取缓存统计
func (mc *MemoryCache[T]) Stats() CacheStats {
	mc.mu.RLock()
	defer mc.mu.RUnlock()
	
	total := mc.hits + mc.misses
	hitRate := float64(0)
	if total > 0 {
		hitRate = float64(mc.hits) / float64(total)
	}
	
	return CacheStats{
		Hits:      mc.hits,
		Misses:    mc.misses,
		Size:      len(mc.items),
		MaxSize:   mc.maxSize,
		HitRate:   hitRate,
		Evictions: mc.evictions,
	}
}

// evictLRU 驱逐最近最少使用的项
func (mc *MemoryCache[T]) evictLRU() {
	element := mc.lruList.Back()
	if element != nil {
		key := element.Value.(string)
		if item, exists := mc.items[key]; exists {
			mc.removeItem(item)
			mc.evictions++
		}
	}
}

// removeItem 移除缓存项
func (mc *MemoryCache[T]) removeItem(item *cacheItem[T]) {
	delete(mc.items, item.key)
	mc.lruList.Remove(item.element)
}

// CleanupExpired 清理过期项
func (mc *MemoryCache[T]) CleanupExpired() {
	mc.mu.Lock()
	defer mc.mu.Unlock()
	
	now := time.Now()
	var expiredKeys []string
	
	for key, item := range mc.items {
		if now.After(item.expireTime) {
			expiredKeys = append(expiredKeys, key)
		}
	}
	
	for _, key := range expiredKeys {
		if item, exists := mc.items[key]; exists {
			mc.removeItem(item)
		}
	}
}

// StartCleanupTimer 启动清理定时器
func (mc *MemoryCache[T]) StartCleanupTimer(interval time.Duration) {
	ticker := time.NewTicker(interval)
	go func() {
		for range ticker.C {
			mc.CleanupExpired()
		}
	}()
}

// TTLCache 带TTL的缓存
type TTLCache[T any] struct {
	cache    map[string]*ttlItem[T]
	mu       sync.RWMutex
	stopChan chan struct{}
}

// ttlItem TTL缓存项
type ttlItem[T any] struct {
	value      *QueryResult[T]
	expireTime time.Time
}

// NewTTLCache 创建TTL缓存
func NewTTLCache[T any]() *TTLCache[T] {
	cache := &TTLCache[T]{
		cache:    make(map[string]*ttlItem[T]),
		stopChan: make(chan struct{}),
	}
	
	// 启动清理协程
	go cache.cleanup()
	
	return cache
}

// Get 获取缓存项
func (tc *TTLCache[T]) Get(key string) (*QueryResult[T], bool) {
	tc.mu.RLock()
	defer tc.mu.RUnlock()
	
	item, exists := tc.cache[key]
	if !exists {
		return nil, false
	}
	
	if time.Now().After(item.expireTime) {
		return nil, false
	}
	
	return item.value, true
}

// Set 设置缓存项
func (tc *TTLCache[T]) Set(key string, result *QueryResult[T], ttl time.Duration) {
	tc.mu.Lock()
	defer tc.mu.Unlock()
	
	tc.cache[key] = &ttlItem[T]{
		value:      result,
		expireTime: time.Now().Add(ttl),
	}
}

// Delete 删除缓存项
func (tc *TTLCache[T]) Delete(key string) {
	tc.mu.Lock()
	defer tc.mu.Unlock()
	
	delete(tc.cache, key)
}

// Clear 清空缓存
func (tc *TTLCache[T]) Clear() {
	tc.mu.Lock()
	defer tc.mu.Unlock()
	
	tc.cache = make(map[string]*ttlItem[T])
}

// Stats 获取缓存统计
func (tc *TTLCache[T]) Stats() CacheStats {
	tc.mu.RLock()
	defer tc.mu.RUnlock()
	
	return CacheStats{
		Size: len(tc.cache),
	}
}

// Stop 停止缓存
func (tc *TTLCache[T]) Stop() {
	close(tc.stopChan)
}

// cleanup 清理过期项
func (tc *TTLCache[T]) cleanup() {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			tc.cleanupExpired()
		case <-tc.stopChan:
			return
		}
	}
}

// cleanupExpired 清理过期项
func (tc *TTLCache[T]) cleanupExpired() {
	tc.mu.Lock()
	defer tc.mu.Unlock()
	
	now := time.Now()
	for key, item := range tc.cache {
		if now.After(item.expireTime) {
			delete(tc.cache, key)
		}
	}
}

// MultiLevelCache 多级缓存
type MultiLevelCache[T any] struct {
	l1Cache QueryCache[T] // L1缓存（内存）
	l2Cache QueryCache[T] // L2缓存（可以是Redis等）
	mu      sync.RWMutex
}

// NewMultiLevelCache 创建多级缓存
func NewMultiLevelCache[T any](l1Cache, l2Cache QueryCache[T]) *MultiLevelCache[T] {
	return &MultiLevelCache[T]{
		l1Cache: l1Cache,
		l2Cache: l2Cache,
	}
}

// Get 获取缓存项
func (mlc *MultiLevelCache[T]) Get(key string) (*QueryResult[T], bool) {
	// 先从L1缓存获取
	if result, found := mlc.l1Cache.Get(key); found {
		return result, true
	}
	
	// 从L2缓存获取
	if mlc.l2Cache != nil {
		if result, found := mlc.l2Cache.Get(key); found {
			// 回写到L1缓存
			mlc.l1Cache.Set(key, result, 5*time.Minute)
			return result, true
		}
	}
	
	return nil, false
}

// Set 设置缓存项
func (mlc *MultiLevelCache[T]) Set(key string, result *QueryResult[T], ttl time.Duration) {
	// 设置到L1缓存
	mlc.l1Cache.Set(key, result, ttl)
	
	// 设置到L2缓存
	if mlc.l2Cache != nil {
		mlc.l2Cache.Set(key, result, ttl)
	}
}

// Delete 删除缓存项
func (mlc *MultiLevelCache[T]) Delete(key string) {
	mlc.l1Cache.Delete(key)
	if mlc.l2Cache != nil {
		mlc.l2Cache.Delete(key)
	}
}

// Clear 清空缓存
func (mlc *MultiLevelCache[T]) Clear() {
	mlc.l1Cache.Clear()
	if mlc.l2Cache != nil {
		mlc.l2Cache.Clear()
	}
}

// Stats 获取缓存统计
func (mlc *MultiLevelCache[T]) Stats() CacheStats {
	l1Stats := mlc.l1Cache.Stats()
	if mlc.l2Cache != nil {
		l2Stats := mlc.l2Cache.Stats()
		return CacheStats{
			Hits:      l1Stats.Hits + l2Stats.Hits,
			Misses:    l1Stats.Misses + l2Stats.Misses,
			Size:      l1Stats.Size + l2Stats.Size,
			MaxSize:   l1Stats.MaxSize + l2Stats.MaxSize,
			HitRate:   (l1Stats.HitRate + l2Stats.HitRate) / 2,
			Evictions: l1Stats.Evictions + l2Stats.Evictions,
		}
	}
	return l1Stats
}
