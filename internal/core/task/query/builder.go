package query

import (
	"fmt"
	"reflect"
	"sort"
	"strings"
	"time"

	"k8s.io/apimachinery/pkg/labels"
)

// QueryBuilder 通用查询构建器
type QueryBuilder[T any] struct {
	filters    []Filter[T]
	sorters    []Sorter[T]
	pagination *Pagination
	selector   labels.Selector
	options    *QueryOptions
}

// NewQueryBuilder 创建新的查询构建器
func NewQueryBuilder[T any]() *QueryBuilder[T] {
	return &QueryBuilder[T]{
		filters: make([]Filter[T], 0),
		sorters: make([]Sorter[T], 0),
		options: &QueryOptions{
			EnableCache: true,
			CacheTTL:    5 * time.Minute,
		},
	}
}

// Where 添加过滤条件
func (qb *QueryBuilder[T]) Where(filter Filter[T]) *QueryBuilder[T] {
	qb.filters = append(qb.filters, filter)
	return qb
}

// WhereField 添加字段过滤条件
func (qb *QueryBuilder[T]) WhereField(field string, operator Operator, value interface{}) *QueryBuilder[T] {
	filter := NewFieldFilter[T](field, operator, value)
	return qb.Where(filter)
}

// WhereIn 添加IN条件
func (qb *QueryBuilder[T]) WhereIn(field string, values []interface{}) *QueryBuilder[T] {
	filter := NewInFilter[T](field, values)
	return qb.Where(filter)
}

// WhereBetween 添加范围条件
func (qb *QueryBuilder[T]) WhereBetween(field string, start, end interface{}) *QueryBuilder[T] {
	filter := NewRangeFilter[T](field, start, end)
	return qb.Where(filter)
}

// WhereFunc 添加自定义函数过滤条件
func (qb *QueryBuilder[T]) WhereFunc(fn func(T) bool) *QueryBuilder[T] {
	filter := NewFuncFilter[T](fn)
	return qb.Where(filter)
}

// OrderBy 添加排序条件
func (qb *QueryBuilder[T]) OrderBy(field string, direction SortDirection) *QueryBuilder[T] {
	sorter := NewFieldSorter[T](field, direction)
	qb.sorters = append(qb.sorters, sorter)
	return qb
}

// OrderByFunc 添加自定义排序函数
func (qb *QueryBuilder[T]) OrderByFunc(fn func(a, b T) bool) *QueryBuilder[T] {
	sorter := NewFuncSorter[T](fn)
	qb.sorters = append(qb.sorters, sorter)
	return qb
}

// Limit 设置分页
func (qb *QueryBuilder[T]) Limit(page, pageSize int32) *QueryBuilder[T] {
	qb.pagination = &Pagination{
		Page:     page,
		PageSize: pageSize,
	}
	return qb
}

// WithLabelSelector 设置标签选择器
func (qb *QueryBuilder[T]) WithLabelSelector(selector labels.Selector) *QueryBuilder[T] {
	qb.selector = selector
	return qb
}

// WithOptions 设置查询选项
func (qb *QueryBuilder[T]) WithOptions(options *QueryOptions) *QueryBuilder[T] {
	qb.options = options
	return qb
}

// Execute 执行查询
func (qb *QueryBuilder[T]) Execute(data []T) *QueryResult[T] {
	result := &QueryResult[T]{
		Items:      make([]T, 0),
		Total:      0,
		Page:       1,
		PageSize:   20,
		TotalPages: 0,
		Filters:    qb.getFilterSummary(),
		Sorters:    qb.getSorterSummary(),
	}

	// 应用过滤器
	filtered := qb.applyFilters(data)
	result.Total = int32(len(filtered))

	// 应用排序
	sorted := qb.applySorters(filtered)

	// 应用分页
	if qb.pagination != nil {
		result.Page = qb.pagination.Page
		result.PageSize = qb.pagination.PageSize
		result.TotalPages = (result.Total + qb.pagination.PageSize - 1) / qb.pagination.PageSize
		
		start := (qb.pagination.Page - 1) * qb.pagination.PageSize
		end := start + qb.pagination.PageSize
		
		if start < int32(len(sorted)) {
			if end > int32(len(sorted)) {
				end = int32(len(sorted))
			}
			result.Items = sorted[start:end]
		}
	} else {
		result.Items = sorted
		result.PageSize = result.Total
		result.TotalPages = 1
	}

	return result
}

// applyFilters 应用过滤器
func (qb *QueryBuilder[T]) applyFilters(data []T) []T {
	if len(qb.filters) == 0 {
		return data
	}

	filtered := make([]T, 0, len(data))
	for _, item := range data {
		match := true
		for _, filter := range qb.filters {
			if !filter.Match(item) {
				match = false
				break
			}
		}
		if match {
			filtered = append(filtered, item)
		}
	}
	return filtered
}

// applySorters 应用排序器
func (qb *QueryBuilder[T]) applySorters(data []T) []T {
	if len(qb.sorters) == 0 {
		return data
	}

	sorted := make([]T, len(data))
	copy(sorted, data)

	sort.Slice(sorted, func(i, j int) bool {
		for _, sorter := range qb.sorters {
			result := sorter.Compare(sorted[i], sorted[j])
			if result != 0 {
				return result < 0
			}
		}
		return false
	})

	return sorted
}

// getFilterSummary 获取过滤器摘要
func (qb *QueryBuilder[T]) getFilterSummary() []string {
	summary := make([]string, len(qb.filters))
	for i, filter := range qb.filters {
		summary[i] = filter.String()
	}
	return summary
}

// getSorterSummary 获取排序器摘要
func (qb *QueryBuilder[T]) getSorterSummary() []string {
	summary := make([]string, len(qb.sorters))
	for i, sorter := range qb.sorters {
		summary[i] = sorter.String()
	}
	return summary
}

// Count 统计符合条件的记录数
func (qb *QueryBuilder[T]) Count(data []T) int32 {
	filtered := qb.applyFilters(data)
	return int32(len(filtered))
}

// First 获取第一条记录
func (qb *QueryBuilder[T]) First(data []T) (*T, bool) {
	filtered := qb.applyFilters(data)
	if len(filtered) == 0 {
		return nil, false
	}
	
	sorted := qb.applySorters(filtered)
	return &sorted[0], true
}

// Exists 检查是否存在符合条件的记录
func (qb *QueryBuilder[T]) Exists(data []T) bool {
	_, exists := qb.First(data)
	return exists
}

// Clone 克隆查询构建器
func (qb *QueryBuilder[T]) Clone() *QueryBuilder[T] {
	clone := &QueryBuilder[T]{
		filters: make([]Filter[T], len(qb.filters)),
		sorters: make([]Sorter[T], len(qb.sorters)),
		options: qb.options,
	}
	
	copy(clone.filters, qb.filters)
	copy(clone.sorters, qb.sorters)
	
	if qb.pagination != nil {
		clone.pagination = &Pagination{
			Page:     qb.pagination.Page,
			PageSize: qb.pagination.PageSize,
		}
	}
	
	if qb.selector != nil {
		clone.selector = qb.selector
	}
	
	return clone
}

// Reset 重置查询构建器
func (qb *QueryBuilder[T]) Reset() *QueryBuilder[T] {
	qb.filters = qb.filters[:0]
	qb.sorters = qb.sorters[:0]
	qb.pagination = nil
	qb.selector = nil
	return qb
}

// GetFieldValue 获取字段值的辅助函数
func GetFieldValue(obj interface{}, field string) (interface{}, error) {
	v := reflect.ValueOf(obj)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}
	
	if v.Kind() != reflect.Struct {
		return nil, fmt.Errorf("object is not a struct")
	}
	
	// 支持嵌套字段访问，如 "Resources.CPU.Allocated"
	fields := strings.Split(field, ".")
	current := v
	
	for _, fieldName := range fields {
		current = current.FieldByName(fieldName)
		if !current.IsValid() {
			return nil, fmt.Errorf("field %s not found", fieldName)
		}
		
		// 如果是指针，解引用
		if current.Kind() == reflect.Ptr {
			if current.IsNil() {
				return nil, nil
			}
			current = current.Elem()
		}
	}
	
	return current.Interface(), nil
}
