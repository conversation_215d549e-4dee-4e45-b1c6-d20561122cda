package query

import (
	"time"

	"k8s.io/apimachinery/pkg/labels"

	"transwarp.io/mlops/pipeline/internal/core/task/model"
)

// WorkloadQuery Workload专用查询构建器
type TaskQuery struct {
	*QueryBuilder[*model.Task]
}

// NewWorkloadQuery 创建Workload查询构建器
func NewTaskQuery() *TaskQuery {
	return &TaskQuery{
		QueryBuilder: NewQueryBuilder[*model.Task](),
	}
}

// Workload专用过滤器方法

// WhereNamespace 按命名空间过滤
func (wq *TaskQuery) WhereNamespace(namespace string) *TaskQuery {
	if namespace != "" {
		wq.WhereField("Namespace", OpEqual, namespace)
	}
	return wq
}

// WhereProject 按项目过滤
func (wq *TaskQuery) WhereProject(project string) *TaskQuery {
	if project != "" {
		wq.WhereField("Project", OpEqual, project)
	}
	return wq
}

// WhereProjects 按项目列表过滤
func (wq *TaskQuery) WhereProjects(projects []string) *TaskQuery {
	if len(projects) > 0 {
		values := make([]interface{}, len(projects))
		for i, p := range projects {
			values[i] = p
		}
		wq.WhereIn("Project", values)
	}
	return wq
}

// WhereStatus 按状态过滤
func (wq *TaskQuery) WhereStatus(status model.TaskStatus) *TaskQuery {
	wq.WhereField("Status", OpEqual, status)
	return wq
}

// WhereStatuses 按状态列表过滤
func (wq *TaskQuery) WhereStatuses(statuses []model.TaskStatus) *TaskQuery {
	if len(statuses) > 0 {
		values := make([]interface{}, len(statuses))
		for i, s := range statuses {
			values[i] = s
		}
		wq.WhereIn("Status", values)
	}
	return wq
}

// WhereType 按类型过滤
func (wq *TaskQuery) WhereType(workloadType model.TaskType) *TaskQuery {
	wq.WhereField("Type", OpEqual, workloadType)
	return wq
}

// WhereTypes 按类型列表过滤
func (wq *TaskQuery) WhereTypes(types []model.TaskType) *TaskQuery {
	if len(types) > 0 {
		values := make([]interface{}, len(types))
		for i, t := range types {
			values[i] = t
		}
		wq.WhereIn("Type", values)
	}
	return wq
}

// WherePriority 按优先级过滤
func (wq *TaskQuery) WherePriority(priority model.TaskPriority) *TaskQuery {
	wq.WhereField("Priority", OpEqual, priority)
	return wq
}

// WherePriorities 按优先级列表过滤
func (wq *TaskQuery) WherePriorities(priorities []model.TaskPriority) *TaskQuery {
	if len(priorities) > 0 {
		values := make([]interface{}, len(priorities))
		for i, p := range priorities {
			values[i] = p
		}
		wq.WhereIn("Priority", values)
	}
	return wq
}

// WhereCreator 按创建者过滤
func (wq *TaskQuery) WhereCreator(creator string) *TaskQuery {
	if creator != "" {
		wq.WhereField("Creator", OpEqual, creator)
	}
	return wq
}

// WhereQueueName 按队列名称过滤
func (wq *TaskQuery) WhereQueueName(queueName string) *TaskQuery {
	if queueName != "" {
		wq.WhereField("QueueName", OpEqual, queueName)
	}
	return wq
}

// WhereClusterQueueName 按集群队列名称过滤
func (wq *TaskQuery) WhereClusterQueueName(clusterQueueName string) *TaskQuery {
	if clusterQueueName != "" {
		wq.WhereField("ClusterQueueName", OpEqual, clusterQueueName)
	}
	return wq
}

// WhereNameContains 按名称包含过滤
func (wq *TaskQuery) WhereNameContains(keyword string) *TaskQuery {
	if keyword != "" {
		wq.WhereField("Name", OpContains, keyword)
	}
	return wq
}

// WhereDescriptionContains 按描述包含过滤
func (wq *TaskQuery) WhereDescriptionContains(keyword string) *TaskQuery {
	if keyword != "" {
		wq.WhereField("Description", OpContains, keyword)
	}
	return wq
}

// WhereKeywordSearch 关键词搜索（名称或描述包含）
func (wq *TaskQuery) WhereKeywordSearch(keyword string) *TaskQuery {
	if keyword != "" {
		nameFilter := NewFieldFilter[*model.Task]("Name", OpContains, keyword)
		descFilter := NewFieldFilter[*model.Task]("Description", OpContains, keyword)
		compositeFilter := NewCompositeFilter[*model.Task](LogicOr, nameFilter, descFilter)
		wq.Where(compositeFilter)
	}
	return wq
}

// 时间范围过滤

// WhereCreatedAfter 创建时间晚于
func (wq *TaskQuery) WhereCreatedAfter(t time.Time) *TaskQuery {
	wq.WhereField("CreateAt", OpGreaterThan, t)
	return wq
}

// WhereCreatedBefore 创建时间早于
func (wq *TaskQuery) WhereCreatedBefore(t time.Time) *TaskQuery {
	wq.WhereField("CreateAt", OpLessThan, t)
	return wq
}

// WhereCreatedBetween 创建时间范围
func (wq *TaskQuery) WhereCreatedBetween(start, end time.Time) *TaskQuery {
	wq.WhereBetween("CreateAt", start, end)
	return wq
}

// WhereStartedAfter 开始时间晚于
func (wq *TaskQuery) WhereStartedAfter(t time.Time) *TaskQuery {
	wq.WhereField("StartAt", OpGreaterThan, &t)
	return wq
}

// WhereStartedBefore 开始时间早于
func (wq *TaskQuery) WhereStartedBefore(t time.Time) *TaskQuery {
	wq.WhereField("StartAt", OpLessThan, &t)
	return wq
}

// WhereStartedBetween 开始时间范围
func (wq *TaskQuery) WhereStartedBetween(start, end time.Time) *TaskQuery {
	wq.WhereBetween("StartAt", &start, &end)
	return wq
}

// 状态相关过滤

// WherePending 等待中的工作负载
func (wq *TaskQuery) WherePending() *TaskQuery {
	return wq.WhereStatus(model.StatusPending)
}

// WhereRunning 运行中的工作负载
func (wq *TaskQuery) WhereRunning() *TaskQuery {
	return wq.WhereStatus(model.StatusRunning)
}

// WhereCompleted 已完成的工作负载（成功或失败）
func (wq *TaskQuery) WhereCompleted() *TaskQuery {
	statuses := []model.TaskStatus{
		model.StatusSucceeded,
		model.StatusFailed,
		model.StatusCancelled,
	}
	return wq.WhereStatuses(statuses)
}

// WhereActive 活跃的工作负载（未完成）
func (wq *TaskQuery) WhereActive() *TaskQuery {
	statuses := []model.TaskStatus{
		model.StatusPending,
		model.StatusAdmitted,
		model.StatusRunning,
	}
	return wq.WhereStatuses(statuses)
}

// WhereTerminated 已终止的工作负载
func (wq *TaskQuery) WhereTerminated() *TaskQuery {
	wq.WhereFunc(func(w *model.Task) bool {
		return w.IsTerminated()
	})
	return wq
}

// 资源相关过滤

// WhereHasGPU 有GPU资源的工作负载
// func (wq *TaskQuery) WhereHasGPU() *TaskQuery {
// 	wq.WhereFunc(func(w *model.Task) bool {
// 		return w.Resources != nil && w.Resources.HasGpuResources()
// 	})
// 	return wq
// }
//
// // WhereCPUGreaterThan CPU资源大于指定值
// func (wq *TaskQuery) WhereCPUGreaterThan(cpu int64) *TaskQuery {
// 	wq.WhereFunc(func(w *model.Task) bool {
// 		return w.Resources != nil && w.Resources.CPU != nil && w.Resources.CPU.Allocated > cpu
// 	})
// 	return wq
// }
//
// // WhereMemoryGreaterThan 内存资源大于指定值
// func (wq *TaskQuery) WhereMemoryGreaterThan(memory int64) *TaskQuery {
// 	wq.WhereFunc(func(w *model.Task) bool {
// 		return w.Resources != nil && w.Resources.Memory != nil && w.Resources.Memory.Allocated > memory
// 	})
// 	return wq
// }

// 标签相关过滤

// WhereHasLabel 有指定标签
func (wq *TaskQuery) WhereHasLabel(key string) *TaskQuery {
	wq.WhereFunc(func(w *model.Task) bool {
		_, exists := w.Labels[key]
		return exists
	})
	return wq
}

// WhereLabelEquals 标签值等于
func (wq *TaskQuery) WhereLabelEquals(key, value string) *TaskQuery {
	wq.WhereFunc(func(w *model.Task) bool {
		v, exists := w.Labels[key]
		return exists && v == value
	})
	return wq
}

// WhereLabelMatches 标签匹配选择器
func (wq *TaskQuery) WhereLabelMatches(selector string) *TaskQuery {
	labelSelector, err := labels.Parse(selector)
	if err != nil {
		return wq // 忽略错误的选择器
	}

	wq.WhereFunc(func(w *model.Task) bool {
		labelSet := labels.Set(w.Labels)
		return labelSelector.Matches(labelSet)
	})
	return wq
}

// 排序方法

// OrderByName 按名称排序
func (wq *TaskQuery) OrderByName(direction SortDirection) *TaskQuery {
	wq.OrderBy("Name", direction)
	return wq
}

// OrderByCreateTime 按创建时间排序
func (wq *TaskQuery) OrderByCreateTime(direction SortDirection) *TaskQuery {
	wq.OrderBy("CreateAt", direction)
	return wq
}

// OrderByStartTime 按开始时间排序
func (wq *TaskQuery) OrderByStartTime(direction SortDirection) *TaskQuery {
	wq.OrderBy("StartAt", direction)
	return wq
}

// OrderByPriority 按优先级排序
func (wq *TaskQuery) OrderByPriority(direction SortDirection) *TaskQuery {
	wq.OrderBy("Priority", direction)
	return wq
}

// OrderByStatus 按状态排序
func (wq *TaskQuery) OrderByStatus(direction SortDirection) *TaskQuery {
	wq.OrderBy("Status", direction)
	return wq
}

// OrderByDuration 按运行时长排序
func (wq *TaskQuery) OrderByDuration(direction SortDirection) *TaskQuery {
	wq.OrderByFunc(func(a, b *model.Task) bool {
		aDuration := a.CalculateDuration()
		bDuration := b.CalculateDuration()

		if direction == SortAsc {
			return aDuration < bDuration
		}
		return aDuration > bDuration
	})
	return wq
}

// 便捷方法

// Recent 最近创建的工作负载
func (wq *TaskQuery) Recent() *TaskQuery {
	return wq.OrderByCreateTime(SortDesc)
}

// Oldest 最早创建的工作负载
func (wq *TaskQuery) Oldest() *TaskQuery {
	return wq.OrderByCreateTime(SortAsc)
}

// HighPriority 高优先级工作负载
func (wq *TaskQuery) HighPriority() *TaskQuery {
	priorities := []model.TaskPriority{
		model.PriorityHigh,
		model.PriorityUrgent,
	}
	return wq.WherePriorities(priorities).OrderByPriority(SortDesc)
}

// Clone 克隆查询构建器
func (wq *TaskQuery) Clone() *TaskQuery {
	return &TaskQuery{
		QueryBuilder: wq.QueryBuilder.Clone(),
	}
}

// 预定义查询

// AllWorkloads 所有工作负载
func AllWorkloads() *TaskQuery {
	return NewTaskQuery()
}

// PendingWorkloads 等待中的工作负载
func PendingWorkloads() *TaskQuery {
	return NewTaskQuery().WherePending()
}

// RunningWorkloads 运行中的工作负载
func RunningWorkloads() *TaskQuery {
	return NewTaskQuery().WhereRunning()
}

// CompletedWorkloads 已完成的工作负载
func CompletedWorkloads() *TaskQuery {
	return NewTaskQuery().WhereCompleted()
}

// ActiveWorkloads 活跃的工作负载
func ActiveWorkloads() *TaskQuery {
	return NewTaskQuery().WhereActive()
}

// RecentWorkloads 最近的工作负载
// func RecentWorkloads(limit int32) *TaskQuery {
// 	return NewTaskQuery().Recent().Limit(1, limit)
// }

// WorkloadsByProject 按项目查询工作负载
func WorkloadsByProject(project string) *TaskQuery {
	return NewTaskQuery().WhereProject(project)
}

// WorkloadsByCreator 按创建者查询工作负载
func WorkloadsByCreator(creator string) *TaskQuery {
	return NewTaskQuery().WhereCreator(creator)
}

// WorkloadsByQueue 按队列查询工作负载
func WorkloadsByQueue(queueName string) *TaskQuery {
	return NewTaskQuery().WhereQueueName(queueName)
}

// GPUWorkloads GPU工作负载
// func GPUWorkloads() *TaskQuery {
// 	return NewTaskQuery().WhereHasGPU()
// }
