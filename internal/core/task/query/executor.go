package query

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"

	"transwarp.io/mlops/mlops-std/stdlog"
)

// QueryExecutor 查询执行器
type QueryExecutor[T any] struct {
	dataProvider DataProvider[T]
	cache        QueryCache[T]
	metrics      *QueryMetricsCollector
	config       *ExecutorConfig
	mu           sync.RWMutex
}

// DataProvider 数据提供者接口
type DataProvider[T any] interface {
	GetData(ctx context.Context) ([]T, error)
	GetDataByNamespace(ctx context.Context, namespace string) ([]T, error)
	GetCount(ctx context.Context) (int32, error)
}

// QueryCache 查询缓存接口
type QueryCache[T any] interface {
	Get(key string) (*QueryResult[T], bool)
	Set(key string, result *QueryResult[T], ttl time.Duration)
	Delete(key string)
	Clear()
	Stats() CacheStats
}

// ExecutorConfig 执行器配置
type ExecutorConfig struct {
	EnableCache     bool          `json:"enable_cache"`
	CacheTTL        time.Duration `json:"cache_ttl"`
	MaxCacheSize    int           `json:"max_cache_size"`
	QueryTimeout    time.Duration `json:"query_timeout"`
	EnableMetrics   bool          `json:"enable_metrics"`
	SlowQueryThreshold time.Duration `json:"slow_query_threshold"`
	MaxConcurrency  int           `json:"max_concurrency"`
}

// NewQueryExecutor 创建查询执行器
func NewQueryExecutor[T any](provider DataProvider[T], config *ExecutorConfig) *QueryExecutor[T] {
	if config == nil {
		config = DefaultExecutorConfig()
	}
	
	executor := &QueryExecutor[T]{
		dataProvider: provider,
		config:       config,
	}
	
	if config.EnableCache {
		executor.cache = NewMemoryCache[T](config.MaxCacheSize)
	}
	
	if config.EnableMetrics {
		executor.metrics = NewQueryMetricsCollector()
	}
	
	return executor
}

// Execute 执行查询
func (qe *QueryExecutor[T]) Execute(ctx context.Context, query *QueryBuilder[T]) (*QueryResult[T], error) {
	startTime := time.Now()
	queryID := generateQueryID()
	
	// 检查超时
	if qe.config.QueryTimeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, qe.config.QueryTimeout)
		defer cancel()
	}
	
	// 尝试从缓存获取
	if qe.config.EnableCache && qe.cache != nil {
		cacheKey := qe.generateCacheKey(query)
		if cached, found := qe.cache.Get(cacheKey); found {
			qe.recordMetrics(queryID, startTime, len(cached.Items), true, nil)
			stdlog.Debug("Query cache hit", zap.String("query_id", queryID))
			return cached, nil
		}
	}
	
	// 获取数据
	data, err := qe.dataProvider.GetData(ctx)
	if err != nil {
		qe.recordMetrics(queryID, startTime, 0, false, err)
		return nil, fmt.Errorf("failed to get data: %w", err)
	}
	
	// 执行查询
	result := query.Execute(data)
	result.QueryTime = time.Since(startTime).Milliseconds()
	
	// 缓存结果
	if qe.config.EnableCache && qe.cache != nil {
		cacheKey := qe.generateCacheKey(query)
		qe.cache.Set(cacheKey, result, qe.config.CacheTTL)
	}
	
	// 记录指标
	qe.recordMetrics(queryID, startTime, len(result.Items), false, nil)
	
	return result, nil
}

// ExecuteWithNamespace 按命名空间执行查询
func (qe *QueryExecutor[T]) ExecuteWithNamespace(ctx context.Context, namespace string, query *QueryBuilder[T]) (*QueryResult[T], error) {
	startTime := time.Now()
	queryID := generateQueryID()
	
	// 检查超时
	if qe.config.QueryTimeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, qe.config.QueryTimeout)
		defer cancel()
	}
	
	// 尝试从缓存获取
	if qe.config.EnableCache && qe.cache != nil {
		cacheKey := qe.generateCacheKeyWithNamespace(namespace, query)
		if cached, found := qe.cache.Get(cacheKey); found {
			qe.recordMetrics(queryID, startTime, len(cached.Items), true, nil)
			return cached, nil
		}
	}
	
	// 获取数据
	data, err := qe.dataProvider.GetDataByNamespace(ctx, namespace)
	if err != nil {
		qe.recordMetrics(queryID, startTime, 0, false, err)
		return nil, fmt.Errorf("failed to get data for namespace %s: %w", namespace, err)
	}
	
	// 执行查询
	result := query.Execute(data)
	result.QueryTime = time.Since(startTime).Milliseconds()
	
	// 缓存结果
	if qe.config.EnableCache && qe.cache != nil {
		cacheKey := qe.generateCacheKeyWithNamespace(namespace, query)
		qe.cache.Set(cacheKey, result, qe.config.CacheTTL)
	}
	
	// 记录指标
	qe.recordMetrics(queryID, startTime, len(result.Items), false, nil)
	
	return result, nil
}

// Count 统计符合条件的记录数
func (qe *QueryExecutor[T]) Count(ctx context.Context, query *QueryBuilder[T]) (int32, error) {
	data, err := qe.dataProvider.GetData(ctx)
	if err != nil {
		return 0, fmt.Errorf("failed to get data: %w", err)
	}
	
	return query.Count(data), nil
}

// Exists 检查是否存在符合条件的记录
func (qe *QueryExecutor[T]) Exists(ctx context.Context, query *QueryBuilder[T]) (bool, error) {
	data, err := qe.dataProvider.GetData(ctx)
	if err != nil {
		return false, fmt.Errorf("failed to get data: %w", err)
	}
	
	return query.Exists(data), nil
}

// First 获取第一条符合条件的记录
func (qe *QueryExecutor[T]) First(ctx context.Context, query *QueryBuilder[T]) (*T, error) {
	data, err := qe.dataProvider.GetData(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get data: %w", err)
	}
	
	item, found := query.First(data)
	if !found {
		return nil, fmt.Errorf("no matching record found")
	}
	
	return item, nil
}

// ExecuteAsync 异步执行查询
func (qe *QueryExecutor[T]) ExecuteAsync(ctx context.Context, query *QueryBuilder[T]) <-chan *AsyncResult[T] {
	resultChan := make(chan *AsyncResult[T], 1)
	
	go func() {
		defer close(resultChan)
		
		result, err := qe.Execute(ctx, query)
		resultChan <- &AsyncResult[T]{
			Result: result,
			Error:  err,
		}
	}()
	
	return resultChan
}

// ExecuteBatch 批量执行查询
func (qe *QueryExecutor[T]) ExecuteBatch(ctx context.Context, queries []*QueryBuilder[T]) ([]*QueryResult[T], error) {
	if len(queries) == 0 {
		return []*QueryResult[T]{}, nil
	}
	
	// 获取数据一次，用于所有查询
	data, err := qe.dataProvider.GetData(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get data: %w", err)
	}
	
	results := make([]*QueryResult[T], len(queries))
	
	// 并发执行查询
	if qe.config.MaxConcurrency > 1 {
		return qe.executeBatchConcurrent(ctx, queries, data)
	}
	
	// 串行执行查询
	for i, query := range queries {
		results[i] = query.Execute(data)
	}
	
	return results, nil
}

// executeBatchConcurrent 并发执行批量查询
func (qe *QueryExecutor[T]) executeBatchConcurrent(ctx context.Context, queries []*QueryBuilder[T], data []T) ([]*QueryResult[T], error) {
	results := make([]*QueryResult[T], len(queries))
	semaphore := make(chan struct{}, qe.config.MaxConcurrency)
	var wg sync.WaitGroup
	var mu sync.Mutex
	
	for i, query := range queries {
		wg.Add(1)
		go func(index int, q *QueryBuilder[T]) {
			defer wg.Done()
			
			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()
			
			result := q.Execute(data)
			
			mu.Lock()
			results[index] = result
			mu.Unlock()
		}(i, query)
	}
	
	wg.Wait()
	return results, nil
}

// ClearCache 清空缓存
func (qe *QueryExecutor[T]) ClearCache() {
	if qe.cache != nil {
		qe.cache.Clear()
	}
}

// GetCacheStats 获取缓存统计
func (qe *QueryExecutor[T]) GetCacheStats() CacheStats {
	if qe.cache != nil {
		return qe.cache.Stats()
	}
	return CacheStats{}
}

// GetMetrics 获取查询指标
func (qe *QueryExecutor[T]) GetMetrics() *QueryStats {
	if qe.metrics != nil {
		return qe.metrics.GetStats()
	}
	return &QueryStats{}
}

// generateCacheKey 生成缓存键
func (qe *QueryExecutor[T]) generateCacheKey(query *QueryBuilder[T]) string {
	// 这里应该根据查询条件生成唯一的缓存键
	// 简化实现，实际应该更复杂
	return fmt.Sprintf("query_%d", time.Now().UnixNano())
}

// generateCacheKeyWithNamespace 生成带命名空间的缓存键
func (qe *QueryExecutor[T]) generateCacheKeyWithNamespace(namespace string, query *QueryBuilder[T]) string {
	return fmt.Sprintf("ns_%s_%s", namespace, qe.generateCacheKey(query))
}

// recordMetrics 记录指标
func (qe *QueryExecutor[T]) recordMetrics(queryID string, startTime time.Time, resultCount int, cacheHit bool, err error) {
	if qe.metrics == nil {
		return
	}
	
	duration := time.Since(startTime)
	
	metric := &QueryMetrics{
		QueryID:     queryID,
		StartTime:   startTime,
		EndTime:     time.Now(),
		Duration:    duration,
		ResultCount: int32(resultCount),
		CacheHit:    cacheHit,
	}
	
	if err != nil {
		metric.Error = err.Error()
	}
	
	qe.metrics.Record(metric)
	
	// 记录慢查询
	if duration > qe.config.SlowQueryThreshold {
		stdlog.Warn("Slow query detected",
			zap.String("query_id", queryID),
			zap.Duration("duration", duration),
			zap.Int("result_count", resultCount))
	}
}

// AsyncResult 异步结果
type AsyncResult[T any] struct {
	Result *QueryResult[T]
	Error  error
}

// generateQueryID 生成查询ID
func generateQueryID() string {
	return fmt.Sprintf("query_%d", time.Now().UnixNano())
}

// DefaultExecutorConfig 默认执行器配置
func DefaultExecutorConfig() *ExecutorConfig {
	return &ExecutorConfig{
		EnableCache:        true,
		CacheTTL:          5 * time.Minute,
		MaxCacheSize:      1000,
		QueryTimeout:      30 * time.Second,
		EnableMetrics:     true,
		SlowQueryThreshold: 1 * time.Second,
		MaxConcurrency:    10,
	}
}

// CacheStats 缓存统计
type CacheStats struct {
	Hits        int64 `json:"hits"`
	Misses      int64 `json:"misses"`
	Size        int   `json:"size"`
	MaxSize     int   `json:"max_size"`
	HitRate     float64 `json:"hit_rate"`
	Evictions   int64 `json:"evictions"`
}

// QueryMetricsCollector 查询指标收集器
type QueryMetricsCollector struct {
	stats   *QueryStats
	metrics []QueryMetrics
	mu      sync.RWMutex
}

// NewQueryMetricsCollector 创建指标收集器
func NewQueryMetricsCollector() *QueryMetricsCollector {
	return &QueryMetricsCollector{
		stats:   &QueryStats{},
		metrics: make([]QueryMetrics, 0),
	}
}

// Record 记录指标
func (qmc *QueryMetricsCollector) Record(metric *QueryMetrics) {
	qmc.mu.Lock()
	defer qmc.mu.Unlock()
	
	qmc.stats.TotalQueries++
	qmc.stats.LastQueryTime = metric.EndTime
	
	if metric.CacheHit {
		qmc.stats.CacheHits++
	} else {
		qmc.stats.CacheMisses++
	}
	
	if metric.Error != "" {
		qmc.stats.ErrorQueries++
		qmc.stats.LastError = metric.Error
	}
	
	// 更新平均时间
	if qmc.stats.TotalQueries > 0 {
		totalTime := qmc.stats.AverageTime * time.Duration(qmc.stats.TotalQueries-1)
		qmc.stats.AverageTime = (totalTime + metric.Duration) / time.Duration(qmc.stats.TotalQueries)
	}
	
	// 记录慢查询
	if metric.Duration > time.Second {
		qmc.stats.SlowQueries++
	}
	
	// 保留最近的指标
	qmc.metrics = append(qmc.metrics, *metric)
	if len(qmc.metrics) > 1000 {
		qmc.metrics = qmc.metrics[len(qmc.metrics)-1000:]
	}
}

// GetStats 获取统计信息
func (qmc *QueryMetricsCollector) GetStats() *QueryStats {
	qmc.mu.RLock()
	defer qmc.mu.RUnlock()
	
	stats := *qmc.stats
	return &stats
}

// GetRecentMetrics 获取最近的指标
func (qmc *QueryMetricsCollector) GetRecentMetrics(limit int) []QueryMetrics {
	qmc.mu.RLock()
	defer qmc.mu.RUnlock()
	
	if limit <= 0 || limit > len(qmc.metrics) {
		limit = len(qmc.metrics)
	}
	
	start := len(qmc.metrics) - limit
	result := make([]QueryMetrics, limit)
	copy(result, qmc.metrics[start:])
	
	return result
}
