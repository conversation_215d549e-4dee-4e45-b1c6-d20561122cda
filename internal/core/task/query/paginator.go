package query

import (
	"fmt"
	"math"
)

// Paginator 分页器
type Paginator[T any] struct {
	page       int32
	pageSize   int32
	total      int32
	totalPages int32
	items      []T
}

// NewPaginator 创建分页器
func NewPaginator[T any](page, pageSize int32) *Paginator[T] {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 1000 {
		pageSize = 1000
	}
	
	return &Paginator[T]{
		page:     page,
		pageSize: pageSize,
	}
}

// Paginate 执行分页
func (p *Paginator[T]) Paginate(items []T) *PaginationResult[T] {
	p.total = int32(len(items))
	p.totalPages = int32(math.Ceil(float64(p.total) / float64(p.pageSize)))
	
	if p.totalPages == 0 {
		p.totalPages = 1
	}
	
	// 计算偏移量
	offset := (p.page - 1) * p.pageSize
	
	// 检查边界
	if offset >= p.total {
		p.items = []T{}
	} else {
		end := offset + p.pageSize
		if end > p.total {
			end = p.total
		}
		p.items = items[offset:end]
	}
	
	return &PaginationResult[T]{
		Items:      p.items,
		Page:       p.page,
		PageSize:   p.pageSize,
		Total:      p.total,
		TotalPages: p.totalPages,
		HasNext:    p.page < p.totalPages,
		HasPrev:    p.page > 1,
		NextPage:   p.getNextPage(),
		PrevPage:   p.getPrevPage(),
		StartIndex: offset + 1,
		EndIndex:   offset + int32(len(p.items)),
	}
}

// getNextPage 获取下一页
func (p *Paginator[T]) getNextPage() int32 {
	if p.page < p.totalPages {
		return p.page + 1
	}
	return p.page
}

// getPrevPage 获取上一页
func (p *Paginator[T]) getPrevPage() int32 {
	if p.page > 1 {
		return p.page - 1
	}
	return p.page
}

// PaginationResult 分页结果
type PaginationResult[T any] struct {
	Items      []T   `json:"items"`       // 当前页项目
	Page       int32 `json:"page"`        // 当前页码
	PageSize   int32 `json:"page_size"`   // 页大小
	Total      int32 `json:"total"`       // 总项目数
	TotalPages int32 `json:"total_pages"` // 总页数
	HasNext    bool  `json:"has_next"`    // 是否有下一页
	HasPrev    bool  `json:"has_prev"`    // 是否有上一页
	NextPage   int32 `json:"next_page"`   // 下一页页码
	PrevPage   int32 `json:"prev_page"`   // 上一页页码
	StartIndex int32 `json:"start_index"` // 开始索引
	EndIndex   int32 `json:"end_index"`   // 结束索引
}

// IsEmpty 是否为空
func (pr *PaginationResult[T]) IsEmpty() bool {
	return len(pr.Items) == 0
}

// IsFirstPage 是否为第一页
func (pr *PaginationResult[T]) IsFirstPage() bool {
	return pr.Page == 1
}

// IsLastPage 是否为最后一页
func (pr *PaginationResult[T]) IsLastPage() bool {
	return pr.Page == pr.TotalPages
}

// GetPageInfo 获取分页信息
func (pr *PaginationResult[T]) GetPageInfo() string {
	if pr.Total == 0 {
		return "No items found"
	}
	return fmt.Sprintf("Showing %d-%d of %d items (Page %d of %d)",
		pr.StartIndex, pr.EndIndex, pr.Total, pr.Page, pr.TotalPages)
}

// CursorPaginator 游标分页器
type CursorPaginator[T any] struct {
	pageSize   int32
	getCursor  func(T) string
	compareFn  func(T, T) bool
}

// NewCursorPaginator 创建游标分页器
func NewCursorPaginator[T any](pageSize int32, getCursor func(T) string, compareFn func(T, T) bool) *CursorPaginator[T] {
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 1000 {
		pageSize = 1000
	}
	
	return &CursorPaginator[T]{
		pageSize:  pageSize,
		getCursor: getCursor,
		compareFn: compareFn,
	}
}

// Paginate 执行游标分页
func (cp *CursorPaginator[T]) Paginate(items []T, cursor string, direction CursorDirection) *CursorResult[T] {
	var filteredItems []T
	
	if cursor == "" {
		// 没有游标，从头开始
		filteredItems = items
	} else {
		// 根据游标过滤
		for _, item := range items {
			itemCursor := cp.getCursor(item)
			
			if direction == CursorNext {
				// 向后分页，找到游标之后的项目
				if itemCursor > cursor {
					filteredItems = append(filteredItems, item)
				}
			} else {
				// 向前分页，找到游标之前的项目
				if itemCursor < cursor {
					filteredItems = append(filteredItems, item)
				}
			}
		}
	}
	
	// 限制结果数量
	hasMore := len(filteredItems) > int(cp.pageSize)
	if hasMore {
		filteredItems = filteredItems[:cp.pageSize]
	}
	
	var nextCursor, prevCursor string
	if len(filteredItems) > 0 {
		if direction == CursorNext || cursor == "" {
			nextCursor = cp.getCursor(filteredItems[len(filteredItems)-1])
			if len(filteredItems) > 1 {
				prevCursor = cp.getCursor(filteredItems[0])
			}
		} else {
			prevCursor = cp.getCursor(filteredItems[0])
			if len(filteredItems) > 1 {
				nextCursor = cp.getCursor(filteredItems[len(filteredItems)-1])
			}
		}
	}
	
	return &CursorResult[T]{
		Items:      filteredItems,
		NextCursor: nextCursor,
		PrevCursor: prevCursor,
		HasNext:    hasMore,
		HasPrev:    cursor != "",
		PageSize:   cp.pageSize,
		Count:      int32(len(filteredItems)),
	}
}

// CursorDirection 游标方向
type CursorDirection string

const (
	CursorNext CursorDirection = "next" // 向后
	CursorPrev CursorDirection = "prev" // 向前
)

// CursorResult 游标分页结果
type CursorResult[T any] struct {
	Items      []T    `json:"items"`       // 项目列表
	NextCursor string `json:"next_cursor"` // 下一页游标
	PrevCursor string `json:"prev_cursor"` // 上一页游标
	HasNext    bool   `json:"has_next"`    // 是否有下一页
	HasPrev    bool   `json:"has_prev"`    // 是否有上一页
	PageSize   int32  `json:"page_size"`   // 页大小
	Count      int32  `json:"count"`       // 当前页项目数
}

// IsEmpty 是否为空
func (cr *CursorResult[T]) IsEmpty() bool {
	return len(cr.Items) == 0
}

// OffsetPaginator 偏移分页器（用于大数据集）
type OffsetPaginator[T any] struct {
	pageSize int32
	maxPages int32
}

// NewOffsetPaginator 创建偏移分页器
func NewOffsetPaginator[T any](pageSize, maxPages int32) *OffsetPaginator[T] {
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 1000 {
		pageSize = 1000
	}
	if maxPages <= 0 {
		maxPages = 1000
	}
	
	return &OffsetPaginator[T]{
		pageSize: pageSize,
		maxPages: maxPages,
	}
}

// Paginate 执行偏移分页
func (op *OffsetPaginator[T]) Paginate(items []T, offset int32) *OffsetResult[T] {
	total := int32(len(items))
	
	// 检查偏移量
	if offset < 0 {
		offset = 0
	}
	if offset >= total {
		return &OffsetResult[T]{
			Items:    []T{},
			Offset:   offset,
			PageSize: op.pageSize,
			Total:    total,
			HasMore:  false,
		}
	}
	
	// 计算结束位置
	end := offset + op.pageSize
	if end > total {
		end = total
	}
	
	// 提取项目
	resultItems := items[offset:end]
	hasMore := end < total
	
	return &OffsetResult[T]{
		Items:    resultItems,
		Offset:   offset,
		PageSize: op.pageSize,
		Total:    total,
		HasMore:  hasMore,
		Count:    int32(len(resultItems)),
	}
}

// OffsetResult 偏移分页结果
type OffsetResult[T any] struct {
	Items    []T   `json:"items"`     // 项目列表
	Offset   int32 `json:"offset"`    // 偏移量
	PageSize int32 `json:"page_size"` // 页大小
	Total    int32 `json:"total"`     // 总数
	HasMore  bool  `json:"has_more"`  // 是否有更多
	Count    int32 `json:"count"`     // 当前项目数
}

// IsEmpty 是否为空
func (or *OffsetResult[T]) IsEmpty() bool {
	return len(or.Items) == 0
}

// GetNextOffset 获取下一页偏移量
func (or *OffsetResult[T]) GetNextOffset() int32 {
	if or.HasMore {
		return or.Offset + or.PageSize
	}
	return or.Offset
}

// PaginationConfig 分页配置
type PaginationConfig struct {
	DefaultPageSize int32 `json:"default_page_size"` // 默认页大小
	MaxPageSize     int32 `json:"max_page_size"`     // 最大页大小
	MaxPages        int32 `json:"max_pages"`         // 最大页数
	EnableCursor    bool  `json:"enable_cursor"`     // 启用游标分页
	EnableOffset    bool  `json:"enable_offset"`     // 启用偏移分页
}

// NewPaginationConfig 创建分页配置
func NewPaginationConfig() *PaginationConfig {
	return &PaginationConfig{
		DefaultPageSize: 20,
		MaxPageSize:     1000,
		MaxPages:        1000,
		EnableCursor:    true,
		EnableOffset:    true,
	}
}

// ValidatePageSize 验证页大小
func (pc *PaginationConfig) ValidatePageSize(pageSize int32) int32 {
	if pageSize <= 0 {
		return pc.DefaultPageSize
	}
	if pageSize > pc.MaxPageSize {
		return pc.MaxPageSize
	}
	return pageSize
}

// ValidatePage 验证页码
func (pc *PaginationConfig) ValidatePage(page int32) int32 {
	if page <= 0 {
		return 1
	}
	if page > pc.MaxPages {
		return pc.MaxPages
	}
	return page
}
