package model

import (
	"time"
)

type Task struct {
	ID     string    `json:"id"`     // 任务ID
	Spec   *TaskSpec `json:"spec"`   // 任务规格
	Status *Status   `json:"status"` // 任务状态
}

type TaskSpec struct {
	Name        string       `json:"name"`        // 任务名称
	Type        TaskType     `json:"type"`        // 类型
	Priority    TaskPriority `json:"priority"`    // 优先级
	Description string       `json:"description"` // 描述
	Creator     string       `json:"creator"`     // 创建人
	CreateAt    time.Time    `json:"create_at"`   // 创建时间

	// 租户信息
	TenantID   string `json:"tenant_id"`   // 租户ID
	TenantName string `json:"tenant_name"` // 租户名称

	// 项目信息
	ProjectName string `json:"project_name"` // 所属空间/项目
	ProjectID   string `json:"project_id"`   // 所属空间/项目ID

	// 资源需求
	Hard *Resources `json:"hard"`

	// 元数据
	Labels      map[string]string `json:"labels"`      // 标签
	Annotations map[string]string `json:"annotations"` // 注解
	Namespace   string            `json:"namespace"`   // 命名空间
}

type Resources struct {
	CPU       ResourceUnit `json:"cpu"`        // CPU需求(如 "2" 或 "200m")
	Memory    ResourceUnit `json:"memory"`     // 内存需求(如 "2Gi")
	GPUCore   ResourceUnit `json:"gpu_core"`   // GPU核心数
	GPUMemory ResourceUnit `json:"gpu_memory"` // GPU内存(如 "16Gi")
}

type ResourceUnit struct {
	Used    string `json:"used"`
	Request string `json:"request"`
	Limit   string `json:"limit"`
}

type Status struct {
	Phase   string `json:"phase"`   // 当前阶段
	Message string `json:"message"` // 状态信息
	Reason  string `json:"reason"`  // 原因

	StartAt *time.Time `json:"start_time"` // 开始时间
	EndAt   *time.Time `json:"end_time"`   // 结束时间

	Runtime *Runtime `json:"runtime"` // 运行时信息
}

type Runtime struct {
	Pods int32      `json:"pods"`
	Used *Resources `json:"used"` // 资源使用情况

	NodeRuntime []*NodeRuntime `json:"node_runtime"` // 节点运行时信息
}

type NodeRuntime struct {
	NodeName string `json:"node_name"`

	Pods int32 `json:"pods"`

	Used *Resources `json:"used"`
}

type TaskType string

const (
	TaskTypePipeline    TaskType = "PIPELINE"     // 流水线
	TaskTypeTraining    TaskType = "TRAINING"     // 模型训练
	TaskTypeInference   TaskType = "INFERENCE"    // 模型推理
	TaskTypeEvaluation  TaskType = "EVALUATION"   // 模型评估
	TaskTypeDataProcess TaskType = "DATA_PROCESS" // 数据处理
	TaskTypeCustom      TaskType = "CUSTOM"       // 自定义
)

// WorkloadPriority 工作负载优先级枚举
type TaskPriority int32

const (
	PriorityLow    TaskPriority = 0 // 低优先级
	PriorityNormal TaskPriority = 1 // 普通优先级
	PriorityHigh   TaskPriority = 2 // 高优先级
	PriorityUrgent TaskPriority = 3 // 紧急优先级
)

// WorkloadStatus 工作负载状态枚举
type TaskStatus string

const (
	StatusPending   TaskStatus = "Pending"   // 等待中
	StatusAdmitted  TaskStatus = "Admitted"  // 已接受
	StatusRunning   TaskStatus = "Running"   // 运行中
	StatusSucceeded TaskStatus = "Succeeded" // 成功
	StatusFailed    TaskStatus = "Failed"    // 失败
	StatusCancelled TaskStatus = "Cancelled" // 已取消
	StatusSuspended TaskStatus = "Suspended" // 已暂停
)

// ResourceUsage 资源使用情况值对象
type ResourceUsage struct {
	Used    string `json:"used"`      // 已使用
	Request string `json:"allocated"` // 已分配
	Limit   string `json:"limit"`     // 限额
}

// NewResourceUsage 创建新的资源使用情况
func NewResourceUsage(used, allocated, limit string) *ResourceUsage {
	return &ResourceUsage{
		Used:    used,
		Request: allocated,
		Limit:   limit,
	}
}

// IsOverUsed 检查是否超使用
func (ru *ResourceUsage) IsOverUsed() bool {
	return ru.Used > ru.Limit
}

// WorkloadResources 工作负载资源信息值对象
type TaskResources struct {
	Memory    *ResourceUsage `json:"memory"`     // 内存(MB)
	CPU       *ResourceUsage `json:"cpu"`        // CPU(毫核)
	GpuMemory *ResourceUsage `json:"gpu_memory"` // 显存(MB)
	GpuCore   *ResourceUsage `json:"gpu_core"`   // 算力(核数)
	PodCount  int32          `json:"pod_count"`  // Pod个数
}

// NewWorkloadResources 创建新的工作负载资源
func NewTaskResources() *TaskResources {
	return &TaskResources{
		Memory:    &ResourceUsage{},
		CPU:       &ResourceUsage{},
		GpuMemory: &ResourceUsage{},
		GpuCore:   &ResourceUsage{},
		PodCount:  0,
	}
}

// Task 工作负载聚合根
type Task1 struct {
	// 基本信息
	ID          string       `json:"id"`          // 工作负载ID
	Name        string       `json:"name"`        // 任务名称
	Type        TaskType     `json:"type"`        // 类型
	Priority    TaskPriority `json:"priority"`    // 优先级
	Status      TaskStatus   `json:"status"`      // 状态
	Creator     string       `json:"creator"`     // 创建人
	CreateAt    time.Time    `json:"create_at"`   // 创建时间
	StartAt     *time.Time   `json:"start_at"`    // 开始时间
	EndAt       *time.Time   `json:"end_at"`      // 结束时间
	Description string       `json:"description"` // 描述

	TenantID   string `json:"tenant_id"`   // 租户ID
	TenantName string `json:"tenant_name"` // 租户名称

	ProjectName string `json:"project_name"` // 所属空间/项目
	ProjectID   string `json:"project_id"`   // 所属空间/项目

	// queue
	QueueName        string `json:"queue_name"`         // 队列名称
	ClusterQueueName string `json:"cluster_queue_name"` // 集群队列名称

	// 资源信息
	Resources *TaskResources `json:"resources"` // 资源信息

	// extra info
	Labels      map[string]string `json:"labels"`      // 标签
	Annotations map[string]string `json:"annotations"` // 注解
	Namespace   string            `json:"namespace"`
}
