package model

import (
	"time"

	kueuev1beta1 "sigs.k8s.io/kueue/apis/kueue/v1beta1"
)

type TaskType string

const (
	TaskTypePipeline    TaskType = "PIPELINE"     // 流水线
	TaskTypeTraining    TaskType = "TRAINING"     // 模型训练
	TaskTypeInference   TaskType = "INFERENCE"    // 模型推理
	TaskTypeEvaluation  TaskType = "EVALUATION"   // 模型评估
	TaskTypeDataProcess TaskType = "DATA_PROCESS" // 数据处理
	TaskTypeCustom      TaskType = "CUSTOM"       // 自定义
)

// WorkloadPriority 工作负载优先级枚举
type TaskPriority int32

const (
	PriorityLow    TaskPriority = 0 // 低优先级
	PriorityNormal TaskPriority = 1 // 普通优先级
	PriorityHigh   TaskPriority = 2 // 高优先级
	PriorityUrgent TaskPriority = 3 // 紧急优先级
)

// WorkloadStatus 工作负载状态枚举
type TaskStatus string

const (
	StatusPending   TaskStatus = "Pending"   // 等待中
	StatusAdmitted  TaskStatus = "Admitted"  // 已接受
	StatusRunning   TaskStatus = "Running"   // 运行中
	StatusSucceeded TaskStatus = "Succeeded" // 成功
	StatusFailed    TaskStatus = "Failed"    // 失败
	StatusCancelled TaskStatus = "Cancelled" // 已取消
	StatusSuspended TaskStatus = "Suspended" // 已暂停
)

// ResourceUsage 资源使用情况值对象
type ResourceUsage struct {
	Used    string `json:"used"`      // 已使用
	Request string `json:"allocated"` // 已分配
	Limit   string `json:"limit"`     // 限额
}

// NewResourceUsage 创建新的资源使用情况
func NewResourceUsage(used, allocated, limit string) *ResourceUsage {
	return &ResourceUsage{
		Used:    used,
		Request: allocated,
		Limit:   limit,
	}
}

// IsOverUsed 检查是否超使用
func (ru *ResourceUsage) IsOverUsed() bool {
	return ru.Used > ru.Limit
}

// WorkloadResources 工作负载资源信息值对象
type TaskResources struct {
	Memory    *ResourceUsage `json:"memory"`     // 内存(MB)
	CPU       *ResourceUsage `json:"cpu"`        // CPU(毫核)
	GpuMemory *ResourceUsage `json:"gpu_memory"` // 显存(MB)
	GpuCore   *ResourceUsage `json:"gpu_core"`   // 算力(核数)
	PodCount  int32          `json:"pod_count"`  // Pod个数
}

// NewWorkloadResources 创建新的工作负载资源
func NewTaskResources() *TaskResources {
	return &TaskResources{
		Memory:    &ResourceUsage{},
		CPU:       &ResourceUsage{},
		GpuMemory: &ResourceUsage{},
		GpuCore:   &ResourceUsage{},
		PodCount:  0,
	}
}

// Workload 工作负载聚合根
type Task struct {
	// 基本信息
	ID          string       `json:"id"`          // 工作负载ID
	Name        string       `json:"name"`        // 任务名称
	Type        TaskType     `json:"type"`        // 类型
	Priority    TaskPriority `json:"priority"`    // 优先级
	Status      TaskStatus   `json:"status"`      // 状态
	Creator     string       `json:"creator"`     // 创建人
	CreateAt    time.Time    `json:"create_at"`   // 创建时间
	StartAt     *time.Time   `json:"start_at"`    // 开始时间
	EndAt       *time.Time   `json:"end_at"`      // 结束时间
	Description string       `json:"description"` // 描述

	ProjectName string `json:"project_name"` // 所属空间/项目
	ProjectID   string `json:"project_id"`   // 所属空间/项目

	// 资源信息
	Resources *TaskResources `json:"resources"` // 资源信息

	// 调度信息
	QueueName        string `json:"queue_name"`         // 队列名称
	ClusterQueueName string `json:"cluster_queue_name"` // 集群队列名称
	// 描述信息
	Labels      map[string]string `json:"labels"`      // 标签
	Annotations map[string]string `json:"annotations"` // 注解

}

// NewWorkload 创建新的工作负载
func NewTask(id, name, namespace, projectName, projectID string, workloadType TaskType) *Task {
	return &Task{
		ID:          id,
		Name:        name,
		ProjectName: projectName,
		ProjectID:   projectID,
		Type:        workloadType,
		Priority:    PriorityNormal,
		Status:      StatusPending,
		CreateAt:    time.Now(),
		Resources:   NewTaskResources(),
		Labels:      make(map[string]string),
		Annotations: make(map[string]string),
	}
}

// IsTerminated 检查工作负载是否已终止
func (w *Task) IsTerminated() bool {
	return w.Status == StatusSucceeded || w.Status == StatusFailed || w.Status == StatusCancelled
}

// IsRunning 检查工作负载是否正在运行
func (w *Task) IsRunning() bool {
	return w.Status == StatusRunning
}

// IsPending 检查工作负载是否等待中
func (w *Task) IsPending() bool {
	return w.Status == StatusPending
}

// IsAdmitted 检查工作负载是否已被接受
func (w *Task) IsAdmitted() bool {
	return w.Status == StatusAdmitted
}

// IsSuspended 检查工作负载是否已暂停
func (w *Task) IsSuspended() bool {
	return w.Status == StatusSuspended
}

// CalculateDuration 计算运行时长
func (w *Task) CalculateDuration() time.Duration {
	if w.StartAt == nil {
		return 0
	}

	endTime := time.Now()
	if w.EndAt != nil {
		endTime = *w.EndAt
	}

	return endTime.Sub(*w.StartAt)
}

// UpdateStatus 更新状态
func (w *Task) UpdateStatus(status TaskStatus) {
	oldStatus := w.Status
	w.Status = status

	// 状态变更时更新时间
	now := time.Now()
	switch status {
	case StatusRunning:
		if oldStatus == StatusPending || oldStatus == StatusAdmitted {
			w.StartAt = &now
		}
	case StatusSucceeded, StatusFailed, StatusCancelled:
		if w.EndAt == nil {
			w.EndAt = &now
		}
	}
}

// SetQueue 设置队列
func (w *Task) SetQueue(queueName, clusterQueueName string) {
	w.QueueName = queueName
	w.ClusterQueueName = clusterQueueName
}

// AddLabel 添加标签
func (w *Task) AddLabel(key, value string) {
	if w.Labels == nil {
		w.Labels = make(map[string]string)
	}
	w.Labels[key] = value
}

// AddAnnotation 添加注解
func (w *Task) AddAnnotation(key, value string) {
	if w.Annotations == nil {
		w.Annotations = make(map[string]string)
	}
	w.Annotations[key] = value
}

// UpdateResources 更新资源信息
func (w *Task) UpdateResources(resources *TaskResources) {
	w.Resources = resources
}

// FromKueueWorkload 从Kueue Workload对象创建领域对象
func FromKueueWorkload(kw *kueuev1beta1.Workload) *Task {
	w := &Task{
		ID:          string(kw.UID),
		Name:        kw.Name,
		CreateAt:    kw.CreationTimestamp.Time,
		Labels:      kw.Labels,
		Annotations: kw.Annotations,
		Resources:   NewTaskResources(),
	}

	// 从标签中获取项目信息
	if project, exists := kw.Labels["project"]; exists {
		w.ProjectName = project
	}

	// 从标签中获取创建者信息
	if creator, exists := kw.Labels["creator"]; exists {
		w.Creator = creator
	}

	// 从标签中获取类型信息
	if workloadType, exists := kw.Labels["workload-type"]; exists {
		w.Type = TaskType(workloadType)
	} else {
		w.Type = TaskTypeCustom
	}

	// 设置队列信息
	if kw.Spec.QueueName != "" {
		// w.QueueName = kw.Spec.QueueName
	}

	// 设置状态
	w.Status = getTaskStatus(kw)

	// 设置优先级
	w.Priority = getTaskPriority(kw)

	// 计算资源使用情况
	w.Resources = calculateResources(kw)

	return w
}

// getWorkloadStatus 从Kueue Workload获取状态
func getTaskStatus(kw *kueuev1beta1.Workload) TaskStatus {
	for _, condition := range kw.Status.Conditions {
		switch condition.Type {
		case kueuev1beta1.WorkloadAdmitted:
			if condition.Status == "True" {
				return StatusAdmitted
			}
		case kueuev1beta1.WorkloadFinished:
			if condition.Status == "True" {
				return StatusSucceeded
			}
		}
	}

	// 检查是否被暂停
	if kw.Spec.Active != nil && !*kw.Spec.Active {
		return StatusSuspended
	}

	return StatusPending
}

// getWorkloadPriority 从Kueue Workload获取优先级
func getTaskPriority(kw *kueuev1beta1.Workload) TaskPriority {
	if kw.Spec.PriorityClassName != "" {
		// 这里可以根据PriorityClassName映射到具体的优先级
		// 简化处理，返回普通优先级
		return PriorityNormal
	}

	if kw.Spec.Priority != nil {
		switch *kw.Spec.Priority {
		case 0:
			return PriorityLow
		case 1:
			return PriorityNormal
		case 2:
			return PriorityHigh
		case 3:
			return PriorityUrgent
		default:
			return PriorityNormal
		}
	}

	return PriorityNormal
}

// calculateResources 计算资源使用情况
func calculateResources(kw *kueuev1beta1.Workload) *TaskResources {
	resources := NewTaskResources()

	for _, podSet := range kw.Spec.PodSets {
		resources.PodCount += podSet.Count

		if podSet.Template.Spec.Containers != nil {
			for _, container := range podSet.Template.Spec.Containers {
				if container.Resources.Requests != nil {
					// CPU资源
					if cpu := container.Resources.Requests.Cpu(); cpu != nil {
						resources.CPU.Allocated += cpu.MilliValue()
					}

					// 内存资源
					if memory := container.Resources.Requests.Memory(); memory != nil {
						resources.Memory.Allocated += memory.Value() / (1024 * 1024) // 转换为MB
					}

					// GPU资源
					for resourceName, quantity := range container.Resources.Requests {
						switch resourceName.String() {
						case "nvidia.com/gpu":
							resources.GpuCore.Allocated += quantity.Value()
						case "nvidia.com/gpu-memory":
							resources.GpuMemory.Allocated += quantity.Value() / (1024 * 1024) // 转换为MB
						}
					}
				}

				if container.Resources.Limits != nil {
					// CPU限制
					if cpu := container.Resources.Limits.Cpu(); cpu != nil {
						resources.CPU.Limit += cpu.MilliValue()
					}

					// 内存限制
					if memory := container.Resources.Limits.Memory(); memory != nil {
						resources.Memory.Limit += memory.Value() / (1024 * 1024) // 转换为MB
					}

					// GPU限制
					for resourceName, quantity := range container.Resources.Limits {
						switch resourceName.String() {
						case "nvidia.com/gpu":
							resources.GpuCore.Limit += quantity.Value()
						case "nvidia.com/gpu-memory":
							resources.GpuMemory.Limit += quantity.Value() / (1024 * 1024) // 转换为MB
						}
					}
				}
			}
		}
	}

	return resources
}
