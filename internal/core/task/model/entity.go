package model

import (
	"time"
)

type Task struct {
	ID string `json:"id"` // 任务ID

	Name        string       `json:"name"`        // 任务名称
	Type        TaskType     `json:"type"`        // 类型
	Priority    TaskPriority `json:"priority"`    // 优先级
	Description string       `json:"description"` // 描述
	Creator     string       `json:"creator"`     // 创建人
	CreateAt    time.Time    `json:"create_at"`   // 创建时间
	UpdateAt    *time.Time   `json:"update_at"`

	// 租户信息
	TenantID   string `json:"tenant_id"`   // 租户ID
	TenantName string `json:"tenant_name"` // 租户名称

	// 项目信息
	ProjectName string `json:"project_name"` // 所属空间/项目
	ProjectID   string `json:"project_id"`   // 所属空间/项目ID

	QueueName        string `json:"queue_name"`         // 队列名称
	ClusterQueueName string `json:"cluster_queue_name"` // 集群队列名称

	// 资源需求
	Resources *Resources `json:"resources"`

	// 元数据
	Labels      map[string]string `json:"labels"`      // 标签
	Annotations map[string]string `json:"annotations"` // 注解
	Namespace   string            `json:"namespace"`   // 命名空间

	Status *TaskStatus `json:"status"` // 任务状态

	StartAt *time.Time `json:"start_at"` // 开始时间
	EndAt   *time.Time `json:"end_at"`   // 结束时间

}

type Resources struct {
	Requests ResourceSpec  `json:"requests"`
	Limits   ResourceSpec  `json:"limits"`
	Used     *ResourceSpec `json:"used"`
}

type ResourceSpec struct {
	CPU       string `json:"cpu"`        // CPU需求(如 "2" 或 "200m")
	Memory    string `json:"memory"`     // 内存需求(如 "2Gi")
	GPU       string `json:"gpu"`        // GPU核心数
	GPUMemory string `json:"gpu_memory"` // GPU内存(如 "16Gi")
}

type TaskType string

const (
	TaskTypePipeline    TaskType = "PIPELINE"     // 流水线
	TaskTypeTraining    TaskType = "TRAINING"     // 模型训练
	TaskTypeInference   TaskType = "INFERENCE"    // 模型推理
	TaskTypeEvaluation  TaskType = "EVALUATION"   // 模型评估
	TaskTypeDataProcess TaskType = "DATA_PROCESS" // 数据处理
	TaskTypeCustom      TaskType = "CUSTOM"       // 自定义
)

// WorkloadPriority 工作负载优先级枚举
type TaskPriority int32

const (
	PriorityLow    TaskPriority = 0 // 低优先级
	PriorityNormal TaskPriority = 1 // 普通优先级
	PriorityHigh   TaskPriority = 2 // 高优先级
	PriorityUrgent TaskPriority = 3 // 紧急优先级
)

type TaskStatus string

const (
	StatusPending   TaskStatus = "Pending"   // 等待中
	StatusAdmitted  TaskStatus = "Admitted"  // 已接受
	StatusRunning   TaskStatus = "Running"   // 运行中
	StatusSucceeded TaskStatus = "Succeeded" // 成功
	StatusFailed    TaskStatus = "Failed"    // 失败
	StatusCancelled TaskStatus = "Cancelled" // 已取消
	StatusSuspended TaskStatus = "Suspended" // 已暂停
)
