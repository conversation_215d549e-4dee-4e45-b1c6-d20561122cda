package model

type ListTasksReq struct {
	// 分页参数
	Page     int32 `json:"page"`
	PageSize int32 `json:"page_size"`

	TenantID   []string `json:"tenant_id"`
	TenantName []string `json:"tenant_name"`

	// 过滤条件
	ProjectName string   `json:"project_name"`
	ProjectId   string   `json:"project_id"`
	Status      []string `json:"status" form:"status"`
	Type        []string `json:"type" form:"type"`
	Priority    []int32  `json:"priority" form:"priority"`
	Creator     string   `json:"creator" form:"creator"`
	QueueName   string   `json:"queue_name" form:"queue_name"`

	// 时间范围
	CreateAtStart *int64 `json:"create_at_start"`
	CreateAtEnd   *int64 `json:"create_at_end"`

	// 搜索关键词
	SearchKeyword string `json:"search_keyword" form:"search_keyword"`

	// 排序参数
	SortBy    string `json:"sort_by" form:"sort_by"`
	SortOrder string `json:"sort_order" form:"sort_order"`

	Namespace string `json:"namespace"`
}

type ListTasksResp struct {
	Items      []*Task `json:"items"`
	Total      int32   `json:"total"`
	Page       int32   `json:"page"`
	PageSize   int32   `json:"page_size"`
	TotalPages int32   `json:"total_pages"`
}

type TaskStatsResp struct {
	Total      int32              `json:"total"`
	ByStatus   map[string]int     `json:"by_status"`
	ByPriority map[int32]int      `json:"by_priority"`
	Resources  *ResourceStatsResp `json:"resources"`
}

type ResourceStatsResp struct {
	CPU       ResourceMetrics `json:"cpu"`
	Memory    ResourceMetrics `json:"memory"`
	GPU       ResourceMetrics `json:"gpu"`
	GPUMemory ResourceMetrics `json:"gpu_memory"`
}

// ResourceMetrics 资源指标
type ResourceMetrics struct {
	Requested int64 `json:"requested"`
	Used      int64 `json:"used"`
	Limit     int64 `json:"limit"`
}

type CreateTaskReq struct {
	Name        string            `json:"name" binding:"required"`
	Namespace   string            `json:"namespace" binding:"required"`
	ProjectName string            `json:"project_name" binding:"required"`
	ProjectID   string            `json:"project_id" binding:"required"`
	Type        string            `json:"type" binding:"required"`
	Priority    int32             `json:"priority"`
	QueueName   string            `json:"queue_name"`
	Creator     string            `json:"creator"`
	Description string            `json:"description"`
	Labels      map[string]string `json:"labels"`
	Annotations map[string]string `json:"annotations"`
	Resources   *TaskResources    `json:"resources"`
}

type UpdateTaskReq struct {
	Description string            `json:"description"`
	Labels      map[string]string `json:"labels"`
	Annotations map[string]string `json:"annotations"`
	Priority    *int32            `json:"priority"`
	QueueName   *string           `json:"queue_name"`
}

type TaskResp struct {
	*Task
	Duration int64 `json:"duration,omitempty"`
}
