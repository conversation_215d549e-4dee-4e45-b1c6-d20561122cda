package model

import "fmt"

type ListTasksReq struct {
	// 分页参数
	Page     int32 `json:"page"`
	PageSize int32 `json:"page_size"`

	TenantID []string `json:"tenant_id"`

	// 过滤条件
	ProjectName string   `json:"project_name"`
	ProjectId   string   `json:"project_id"`
	Status      []string `json:"status" form:"status"`
	Type        []string `json:"type" form:"type"`
	Priority    []int32  `json:"priority" form:"priority"`
	Creator     string   `json:"creator" form:"creator"`
	QueueName   string   `json:"queue_name" form:"queue_name"`

	// 时间范围
	CreateTimeStart *int64 `json:"create_time_start" form:"create_time_start"`
	CreateTimeEnd   *int64 `json:"create_time_end" form:"create_time_end"`

	// 搜索关键词
	SearchKeyword string `json:"search_keyword" form:"search_keyword"`

	// 排序参数
	SortBy    string `json:"sort_by" form:"sort_by"`
	SortOrder string `json:"sort_order" form:"sort_order"`
}

type ListTasksResp struct {
	Items      []*Task `json:"items"`
	Total      int32   `json:"total"`
	Page       int32   `json:"page"`
	PageSize   int32   `json:"page_size"`
	TotalPages int32   `json:"total_pages"`
}

type TaskStatsResp struct {
	Total      int32              `json:"total"`
	ByStatus   map[string]int     `json:"by_status"`
	ByPriority map[int32]int      `json:"by_priority"`
	Resources  *ResourceStatsResp `json:"resources"`
}

type ResourceStatsResp struct {
	CPU       ResourceMetrics `json:"cpu"`
	Memory    ResourceMetrics `json:"memory"`
	GPU       ResourceMetrics `json:"gpu"`
	GPUMemory ResourceMetrics `json:"gpu_memory"`
}

// ResourceMetrics 资源指标
type ResourceMetrics struct {
	Requested int64 `json:"requested"`
	Used      int64 `json:"used"`
	Limit     int64 `json:"limit"`
}

type CreateTaskReq struct {
	Name        string            `json:"name" binding:"required"`
	Namespace   string            `json:"namespace" binding:"required"`
	ProjectName string            `json:"project_name" binding:"required"`
	ProjectID   string            `json:"project_id" binding:"required"`
	Type        string            `json:"type" binding:"required"`
	Priority    int32             `json:"priority"`
	QueueName   string            `json:"queue_name"`
	Creator     string            `json:"creator"`
	Description string            `json:"description"`
	Labels      map[string]string `json:"labels"`
	Annotations map[string]string `json:"annotations"`
	Resources   *TaskResources    `json:"resources"`
}

type UpdateTaskReq struct {
	Description string            `json:"description"`
	Labels      map[string]string `json:"labels"`
	Annotations map[string]string `json:"annotations"`
	Priority    *int32            `json:"priority"`
	QueueName   *string           `json:"queue_name"`
}

// WorkloadResponse 工作负载响应
type TaskResp struct {
	*Task
	Duration int64 `json:"duration,omitempty"` // 运行时长(秒)
}

func (w *Task) ToTaskResp() *TaskResp {
	resp := &TaskResp{
		Task: w,
	}

	// 计算运行时长
	if w.StartAt != nil {
		duration := w.CalculateDuration()
		resp.Duration = int64(duration.Seconds())
	}

	return resp
}

// FromCreateRequest 从创建请求创建工作负载
func FromCreateRequest(req *CreateTaskReq) *Task {
	workload := NewTask(
		"", // ID将由系统生成
		req.Name,
		req.Namespace,
		req.ProjectName,
		req.ProjectID,
		TaskType(req.Type),
	)

	workload.Priority = TaskPriority(req.Priority)
	workload.QueueName = req.QueueName
	workload.Creator = req.Creator
	workload.Description = req.Description

	if req.Labels != nil {
		workload.Labels = req.Labels
	}

	if req.Annotations != nil {
		workload.Annotations = req.Annotations
	}

	if req.Resources != nil {
		workload.Resources = req.Resources
	}

	return workload
}

// ApplyUpdateRequest 应用更新请求
func (w *Task) ApplyUpdateRequest(req *UpdateTaskReq) {
	if req.Description != "" {
		w.Description = req.Description
	}

	if req.Labels != nil {
		if w.Labels == nil {
			w.Labels = make(map[string]string)
		}
		for k, v := range req.Labels {
			w.Labels[k] = v
		}
	}

	if req.Annotations != nil {
		if w.Annotations == nil {
			w.Annotations = make(map[string]string)
		}
		for k, v := range req.Annotations {
			w.Annotations[k] = v
		}
	}

	if req.Priority != nil {
		w.Priority = TaskPriority(*req.Priority)
	}

	if req.QueueName != nil {
		w.QueueName = *req.QueueName
	}
}

// ValidateCreateRequest 验证创建请求
func ValidateCreateRequest(req *CreateTaskReq) error {
	if req.Name == "" {
		return fmt.Errorf("name is required")
	}

	if req.Namespace == "" {
		return fmt.Errorf("namespace is required")
	}

	if req.ProjectName == "" {
		return fmt.Errorf("project is required")
	}

	if req.Type == "" {
		return fmt.Errorf("type is required")
	}

	// 验证类型是否有效
	validTypes := []string{
		string(TaskTypePipeline),
		string(TaskTypeTraining),
		string(TaskTypeInference),
		string(TaskTypeEvaluation),
		string(TaskTypeDataProcess),
		string(TaskTypeCustom),
	}

	typeValid := false
	for _, validType := range validTypes {
		if req.Type == validType {
			typeValid = true
			break
		}
	}

	if !typeValid {
		return fmt.Errorf("invalid workload type: %s", req.Type)
	}

	// 验证优先级
	if req.Priority < 0 || req.Priority > 3 {
		return fmt.Errorf("priority must be between 0 and 3")
	}

	return nil
}

// ValidateListRequest 验证列表请求
func ValidateListRequest(req *ListTasksReq) error {
	if req.Page <= 0 {
		req.Page = 1
	}

	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	if req.PageSize > 1000 {
		req.PageSize = 1000
	}

	if req.SortBy == "" {
		req.SortBy = "create_at"
	}

	if req.SortOrder == "" {
		req.SortOrder = "desc"
	}

	// 验证排序字段
	validSortFields := []string{"name", "create_at", "start_at", "priority", "status"}
	sortFieldValid := false
	for _, field := range validSortFields {
		if req.SortBy == field {
			sortFieldValid = true
			break
		}
	}

	if !sortFieldValid {
		return fmt.Errorf("invalid sort field: %s", req.SortBy)
	}

	// 验证排序方向
	if req.SortOrder != "asc" && req.SortOrder != "desc" {
		return fmt.Errorf("sort order must be 'asc' or 'desc'")
	}

	return nil
}
