package core

import (
	"context"
	"fmt"

	"github.com/samber/lo"
	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	"transwarp.io/mlops/mlops-std/stderr"
	"transwarp.io/mlops/mlops-std/util"
	"transwarp.io/mlops/pipeline/internal/core/model"
	"transwarp.io/mlops/pipeline/internal/dao"
	dao_model "transwarp.io/mlops/pipeline/internal/dao/model"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
)

const SamplesCreateUser = "samples"

type IPipelineManager interface {
	CreatePipeline(ctx context.Context, pipeline *pb.Pipeline) (string, error)
	ModifyPipeline(ctx context.Context, pipeline *pb.Pipeline) (string, error)
	GetPipeline(ctx context.Context, id string) (*model.Pipeline, error)
	ListPipelines(ctx context.Context, projectId string) ([]*pb.Pipeline, error)
	DeletePipeline(ctx context.Context, id string) error
	CountVersionInPipeline(ctx context.Context, id string) (int64, error)
	CountVersionInPipelines(ctx context.Context) (map[string]int64, error)
	CheckPipelineNameUnique(ctx context.Context, name string, projectId string) error
	DeletePipelines(ctx context.Context, ids []string) error
}

type PipelineManager struct{}

func NewPipelineManager() *PipelineManager {
	return &PipelineManager{}
}

func (m *PipelineManager) CheckPipelineNameUnique(ctx context.Context, name string, projectId string) error {
	size, err := dao.PipelineDAOInst.CountByNameAndProjectId(name, projectId)
	if err != nil {
		return err
	}
	if size > 0 {
		return stderr.PipelineNameExist.Error()
	}
	return nil
}

func (m *PipelineManager) DeletePipeline(ctx context.Context, id string) error {
	return m.DeletePipelines(ctx, []string{id})
}

func (m *PipelineManager) DeletePipelines(ctx context.Context, ids []string) error {
	pvs, err := PipelineVersionMgr.ListPipelineVersions(ctx, ids...)
	if err != nil {
		return fmt.Errorf("list pipeline versions: %w", err)
	}
	vids := lo.Map(pvs, func(pv *pb.PipelineVersion, _ int) string { return pv.Id })
	runs, _, err := RunMgr.ListRun(ctx, "", &pb.ListRunsReq_Filter{
		SourceId: vids,
	}, &pb.PageReq{})
	if err != nil {
		return fmt.Errorf("list pipeline version runs: %w", err)
	}
	// 运行中的不能删除
	for _, run := range runs {
		if run.Status == pb.Run_Running {
			return stderr.DeleteRunningPipelineTask.Error()
		}
	}
	// 删除 job
	jobs, err := dao.JobDAOInst.ListByName(ctx, vids...)
	if err != nil {
		return err
	}
	jids := lo.Map(jobs, func(job *dao_model.Job, _ int) string { return job.UUID })
	err = client.PipelineClientInst.DeletePipelineJob(ctx, jids)
	if err != nil {
		return err
	}
	// 删除 runs
	for _, run := range runs {
		err = RunMgr.DeleteRun(ctx, run.Id)
		if err != nil {
			return fmt.Errorf("delete run: runId: %s: %w", run.Id, err)
		}
	}
	err = dao.PipelineVersionDAOInst.DeleteByPipelineId(ids...)
	if err != nil {
		return fmt.Errorf("delete versions: %w", err)
	}
	return dao.PipelineDAOInst.DeleteByIds(ids)
}

func (m *PipelineManager) CreatePipeline(ctx context.Context, pipelineReq *pb.Pipeline) (string, error) {
	size, err := dao.PipelineDAOInst.CountByNameAndProjectId(pipelineReq.Name, util.GetUsername(ctx))
	if err != nil {
		return "", err
	}
	if size > 0 {
		return "", stderr.PipelineNameExist.Error()
	}
	pipeline := (&model.Pipeline{}).FromPb(pipelineReq)
	pipeline.CreateUser = util.GetUsername(ctx)
	pipelineId, err := dao.PipelineDAOInst.Create(pipeline.ToModel())
	if err != nil {
		return "", err
	}
	return pipelineId, nil
}

func (m *PipelineManager) ModifyPipeline(ctx context.Context, pipeline *pb.Pipeline) (string, error) {
	pipelineEntity, err := dao.PipelineDAOInst.GetById(pipeline.Id)
	if err != nil {
		return "", err
	}
	pipelineEntity.Name = pipeline.Name
	pipelineEntity.Desc = pipeline.Desc
	return dao.PipelineDAOInst.Save(pipelineEntity)
}

func (m *PipelineManager) GetPipeline(ctx context.Context, id string) (*model.Pipeline, error) {
	entity, err := dao.PipelineDAOInst.GetById(id)
	if err != nil {
		return nil, err
	}
	pipeline := (&model.Pipeline{}).FromModel(entity)
	return pipeline, nil
}

func (m *PipelineManager) ListPipelines(ctx context.Context, projectId string) ([]*pb.Pipeline, error) {
	entities, err := dao.PipelineDAOInst.ListByProjectId(projectId)
	if err != nil {
		return nil, err
	}

	pipelines := make([]*pb.Pipeline, 0)
	for _, entry := range entities {
		pipeline := (&model.Pipeline{}).FromModel(entry).ToPb()
		pipelines = append(pipelines, pipeline)
	}

	return pipelines, nil
}

func (m *PipelineManager) CountVersionInPipeline(ctx context.Context, id string) (int64, error) {
	return dao.PipelineVersionDAOInst.CountVersionByPipelineId(id)
}

func (m *PipelineManager) CountVersionInPipelines(ctx context.Context) (map[string]int64, error) {
	return dao.PipelineVersionDAOInst.CountVersionInPipelines()
}
