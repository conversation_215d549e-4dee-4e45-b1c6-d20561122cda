package core

import (
	"context"
	"fmt"
	"io"
	"strconv"

	"gorm.io/gorm"

	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	common "transwarp.io/aip/llmops-common/pb/common"
	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	k8s_util "transwarp.io/mlops/mlops-std/k8s"
	"transwarp.io/mlops/mlops-std/stderr"
	"transwarp.io/mlops/mlops-std/stdlog"
	config "transwarp.io/mlops/pipeline/conf"
	"transwarp.io/mlops/pipeline/internal/core/client"
	"transwarp.io/mlops/pipeline/internal/core/model"
	"transwarp.io/mlops/pipeline/internal/dao"
	dao_model "transwarp.io/mlops/pipeline/internal/dao/model"
)

// m *RunManager IRunManager

type RunManager struct{}

func NewRunManager() *RunManager {
	return &RunManager{}
}

const (
	KubeflowSecretName          = "mlpipeline-minio-artifact"
	LabelKeyApplicationCrdId    = "application-crd-id"
	LabelValueKubeflowPipelines = "kubeflow-pipelines"
	AccessKey                   = "accesskey"
	SecretKey                   = "secretkey"
)

type IRunManager interface {
	SubmitRun(ctx context.Context, run *model.Run) (string, error)
	SubmitRunV2(ctx context.Context, run *model.Run) (string, error)
	RetryRun(ctx context.Context, runId string) error
	TerminateRun(ctx context.Context, runId string) error
	ListRun(ctx context.Context, projectId string, filter *pb.ListRunsReq_Filter, pageConfig *pb.PageReq) ([]*model.Run, int64, error)
	GetRun(ctx context.Context, runId string) (*model.Run, error)
	DeleteRun(ctx context.Context, runId string) error
	GetRunStepLogs(ctx context.Context, runId string, nodeId string) (io.Reader, error)
	GetRunStepArtifact(ctx context.Context, runId string, nodeId string, artifactName string) (io.Reader, string, error)
	GetRunStepEvents(ctx context.Context, id string, step string, projectId string, tenantId string) ([]*common.PodEvent, error)
	GetEventsByLabels(ctx context.Context, labelsMap map[string]string) ([]*common.PodEvent, error)
	GetRunStepPodInfo(ctx context.Context, id string, step string) ([]byte, error)
}

func (m *RunManager) GetRunStepLogs(ctx context.Context, runId string, nodeId string) (io.Reader, error) {
	podName, namespace, err := client.PipelineClientInst.GetRunPodNameAndNamespace(ctx, runId, nodeId)
	if err != nil {
		return nil, err
	}
	serviceCfg := config.PipeineConfig.MLOPS.ServiceConfig
	var limitLines int64
	if serviceCfg.PodLogLimitLine != "" {
		limitLines, err = strconv.ParseInt(serviceCfg.PodLogLimitLine, 10, 64)
		if err != nil {
			return nil, err
		}
	} else {
		limitLines = int64(200)
	}

	var limitBytes int64
	if serviceCfg.PodLogLimitBytes != "" {
		limitBytes, err = strconv.ParseInt(serviceCfg.PodLogLimitBytes, 10, 64)
		if err != nil {
			return nil, err
		}
	} else {
		limitBytes = int64(1024 * 1024 * 2) // 2MB
	}
	log, err := client.K8sClient.GetContainerLogs(ctx, namespace, podName, "main", &k8s_util.GetLogsOptions{
		TailLines:  limitLines,
		LimitBytes: limitBytes,
	})
	if err != nil && errors.IsNotFound(err) {
		stdlog.WithFields("runid", runId, "step", nodeId, "namespace", namespace, "podName", podName).
			Infof("logs step from s3")
		return client.PipelineClientInst.GetRunStepLogs(ctx, runId, nodeId)
	}
	return log, err
}

func (m *RunManager) GetRunStepArtifact(ctx context.Context, runId string, nodeId string, artifactName string) (io.Reader, string, error) {
	return client.PipelineClientInst.GetRunStepArtifact(ctx, runId, nodeId, artifactName)
}

func (m *RunManager) SubmitRun(ctx context.Context, run *model.Run) (string, error) {
	experiment, err := getOrCreateExperiment(ctx, run.ProjectId)
	if err != nil {
		return "", err
	}
	project, err := client.CasCli.GetProjectDetailById(ctx, run.ProjectId)
	if err != nil {
		return "", err
	}

	return client.PipelineClientInst.SubmitRun(ctx, run, experiment, project.TenantUID)
}

func (m *RunManager) SubmitRunV2(ctx context.Context, run *model.Run) (string, error) {
	experiment, err := getOrCreateExperiment(ctx, run.ProjectId)
	if err != nil {
		return "", err
	}
	project, err := client.CasCli.GetProjectDetailById(ctx, run.ProjectId)
	if err != nil {
		return "", err
	}
	return client.PipelineClientInst.SubmitRunV2(ctx, run, experiment, project.TenantUID)
}

func (m *RunManager) RetryRun(ctx context.Context, runId string) error {
	run, err := m.GetRun(ctx, runId)
	if err != nil {
		return err
	}
	if run.Status == pb.Run_Succeeded || run.Status == pb.Run_Running {
		return stderr.CanOnlyRetryFailedOrErrorRun.Error(run.Status.String())
	}
	return client.PipelineClientInst.RetryRun(ctx, runId)
}

func (m *RunManager) TerminateRun(ctx context.Context, runId string) error {
	return client.PipelineClientInst.TerminateRun(ctx, runId)
}

func (m *RunManager) GetRun(ctx context.Context, runId string) (*model.Run, error) {
	entry, err := dao.RunDAOInst.Get(ctx, runId)
	if err != nil {
		return nil, err
	}
	run := &model.Run{}
	run.FromEntry(*entry)
	_, err = dao.MetricDAOInst.ListByRunId(ctx, runId)
	if err != nil {
		return nil, err
	}
	return run, nil
}

func (m *RunManager) DeleteRun(ctx context.Context, runId string) error {
	// 有些情况下 kubelfow 的 runs 会被其他人删除, 所以这里不抛出 err
	_, err := client.PipelineClientInst.DeleteRunById(ctx, runId)
	if err != nil {
		stdlog.Warn("delete run from kubeflow: ", err)
	}
	return dao.RunDAOInst.Delete(ctx, runId)
}

func (m *RunManager) ListRun(ctx context.Context, projectId string, filter *pb.ListRunsReq_Filter, pageConfig *pb.PageReq) ([]*model.Run, int64, error) {
	entries, size, err := dao.RunDAOInst.List(ctx, projectId, filter, pageConfig)
	if err != nil {
		return nil, 0, err
	}
	runs := make([]*model.Run, 0)
	for _, entry := range entries {
		run := &model.Run{}
		run.FromEntry(*entry)
		runs = append(runs, run)
	}
	return runs, size, nil
}

func (m *RunManager) GetRunStepPodInfo(ctx context.Context, id string, step string) ([]byte, error) {
	podName, namespace, err := client.PipelineClientInst.GetRunPodNameAndNamespace(ctx, id, step)
	if err != nil {
		return nil, err
	}
	if len(podName) == 0 {
		return nil, stderr.ParamParseFailure.Error(podName)
	}

	if len(namespace) == 0 {
		return nil, stderr.ParamParseFailure.Error(namespace)
	}

	return client.K8sClient.GetPodInfo(ctx, namespace, podName)
}

func (m *RunManager) GetRunStepEvents(ctx context.Context, id string, step string, projectId string, tenantId string) ([]*common.PodEvent, error) {
	podName, namespace, err := client.PipelineClientInst.GetRunPodNameAndNamespace(ctx, id, step)
	if err != nil {
		stdlog.WithFields("runId", id, "step", step).Infof("pod is nil, please check events")
		namespace = tenantId
	}

	events, err := client.K8sInformerCli.GetPodEvents(ctx, namespace, id, podName)
	if err != nil {
		return nil, err
	}
	podEvents := make([]*common.PodEvent, 0)
	for _, event := range events {
		podEvents = append(podEvents, &common.PodEvent{
			Type:           event.Type,
			Reason:         event.Reason,
			FirstTimestamp: event.FirstTimestamp.Unix(),
			LastTimestamp:  event.LastTimestamp.Unix(),
			Count:          event.Count,
			Message:        event.Message,
		})
	}
	return podEvents, nil
}

func (m *RunManager) GetEventsByLabels(ctx context.Context, labels map[string]string) ([]*common.PodEvent, error) {
	eventList, err := client.K8sClient.GetPodEventsByLabels(ctx, "", labels)
	if err != nil {
		return nil, err
	}
	stdlog.Info(fmt.Sprintf("query events list label:%+v,result:%+v", labels, eventList))
	podEvents := make([]*common.PodEvent, 0)
	for _, event := range eventList.Items {
		podEvents = append(podEvents, &common.PodEvent{
			Type:           event.Type,
			Reason:         event.Reason,
			FirstTimestamp: event.FirstTimestamp.Unix(),
			LastTimestamp:  event.LastTimestamp.Unix(),
			Count:          event.Count,
			Message:        event.Message,
		})
	}
	return podEvents, nil
}

func getOrCreateExperiment(ctx context.Context, projectId string) (*dao_model.Experiment, error) {
	project, err := client.CasCli.GetProjectDetailById(ctx, projectId)
	if err != nil {
		return nil, err
	}

	namespace := k8s_util.CurrentNamespaceInCluster()
	experimentId := fmt.Sprintf("%s/%s", namespace, project.ProjectID)
	experiment, err := dao.ExperimentDAOInst.Get(experimentId)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			experiment = &dao_model.Experiment{
				UUID:      experimentId,
				Name:      experimentId,
				Namespace: project.TenantUID,
			}
			err = dao.ExperimentDAOInst.Save(experiment)
			if err != nil {
				return nil, err
			}
			err = initKubeflowSecret(project.TenantUID)
			if err != nil {
				return nil, err
			}
			// err = initKubeflowServiceAccount(project.TenantUID)
			// if err !=nil {
			//	return nil, err
			//}
		} else {
			return nil, err
		}
	}
	return experiment, nil
}

func initKubeflowSecret(namespace string) error {
	secret := &v1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      KubeflowSecretName,
			Namespace: namespace,
			Labels: map[string]string{
				LabelKeyApplicationCrdId: LabelValueKubeflowPipelines,
			},
		},
		Data: map[string][]byte{
			AccessKey: []byte(config.PipeineConfig.MLOPS.Warehouse.Minio.AccessKey),
			SecretKey: []byte(config.PipeineConfig.MLOPS.Warehouse.Minio.SecretKey),
		},
		Type: v1.SecretTypeOpaque,
	}
	_, err := client.K8sClient.Client.CoreV1().Secrets(namespace).Create(context.Background(), secret, metav1.CreateOptions{})
	if err != nil {
		if errors.IsAlreadyExists(err) {
			stdlog.Info(fmt.Sprintf("Secret %s already exists\n", secret.Name))
		} else {
			fmt.Printf("Error creating secret: %s\n", err.Error())
			return err
		}
	}
	return nil
}

// func initKubeflowServiceAccount(namespace string) error {
//	currentNamespace := config.PipeineConfig.MLOPS.Pipeline.Namespace
//	if currentNamespace == "" {
//		currentNamespace = k8s_util.CurrentNamespaceInCluster()
//	}
//	serviceAccount := &v1.ServiceAccount{
//		ObjectMeta: metav1.ObjectMeta{
//			Name:      model.PipelineServiceAccount,
//			Namespace: namespace,
//		},
//	}
//	_, err := client.K8sClient.Client.CoreV1().ServiceAccounts(namespace).Create(context.Background(), serviceAccount, metav1.CreateOptions{})
//
//	if err != nil {
//		if errors.IsAlreadyExists(err) {
//			stdlog.Info("Secret %s already exists\n", serviceAccount.Name)
//		} else {
//			fmt.Printf("Error creating secret: %s\n", err.Error())
//			return err
//		}
//	}
//
//	clusterRoleBinding := &v1beta1.ClusterRoleBinding{
//		ObjectMeta: metav1.ObjectMeta{
//			Name:      fmt.Sprintf("%s-%s", namespace, model.PipelineServiceAccount),
//		},
//		RoleRef: v1beta1.RoleRef{
//			APIGroup: "rbac.authorization.k8s.io",
//			Kind: "ClusterRole",
//			Name: fmt.Sprintf("%s-llmops-admin", currentNamespace),
//		},
//		Subjects: []v1beta1.Subject{
//			{
//				Kind: "ServiceAccount",
//				Name: model.PipelineServiceAccount,
//				Namespace: namespace,
//			},
//		},
//	}
//	_, err = client.K8sClient.Client.RbacV1beta1().ClusterRoleBindings().Create(context.Background(), clusterRoleBinding, metav1.CreateOptions{})
//
//	if err != nil {
//		if errors.IsAlreadyExists(err) {
//			stdlog.Info("Secret %s already exists\n", serviceAccount.Name)
//		} else {
//			fmt.Printf("Error creating secret: %s\n", err.Error())
//			return err
//		}
//	}
//
//	return nil
//}
