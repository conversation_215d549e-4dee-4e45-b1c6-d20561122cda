package consts

import (
	"transwarp.io/mlops/pipeline/internal/util"
)

const (
	LabelKeyCacheEnabled = "pipelines.kubeflow.org/cache_enabled"

	// const belong llmops product
	LabelLLMOpsNameKey = "llmops.transwarp.io/name"
	// value is llmops installed namespace
	LabelLLMOpsInstanceKey = "llmops.transwarp.io/instance"

	LabelLLMOpsPartOfKey = "llmops.transwarp.io/part-of"

	LabelLLMOpsComponentKey = "llmops.transwarp.io/component"

	// value is llmops installed namespace
	LabelLLMOpsManagedByKey = "llmops.transwarp.io/managed-by"

	LabelLLMOpsVersionKey = "llmops.transwarp.io/version"

	// task label
	LabelTaskManagedByKey = "task.transwarp.io/managed-by"

	LabelTaskRefSourceIDKey = "task.transwarp.io/ref-source-id"

	// kueue related label
	LabelKueueQueueNameKey = "kueue.x-k8s.io/queue-name"
	// [ns]-[high|medium|low], ex: llmops-high  llmops-low  llmops-medium
	LabelKueueWorkloadPriorityKey = "kueue.x-k8s.io/priority-class"
	LabelKueuePodGroupNameKey     = "kueue.x-k8s.io/pod-group-name"
	LabelKueueManagedKey          = "kueue.x-k8s.io/managed"
	LabelKueuePodsetKey           = "kueue.x-k8s.io/podset"
)

const (
	LabelLLMOpsNameValue      = "llmops"
	LabelLLMOpsComponentValue = "pipeline"

	LabelTrueValue  = "true"
	LabelFalseValue = "false"
)

var (
	LabelLLMOpsManagedByValue = util.GetCurrentNamespace()
)

func ContainLabels(target, subset map[string]string) bool {
	if target == nil || subset == nil {
		return false
	}

	for key, value := range subset {
		targetValue, exists := target[key]
		if !exists || targetValue != value {
			return false
		}
	}
	return true
}

func HasKueueManagedLabel(labels map[string]string) bool {
	if v, ok := labels[LabelKueueManagedKey]; ok && v == LabelTrueValue {
		return true
	}

	return false
}

func HasKueuePodsetLabel(labels map[string]string) bool {
	if _, ok := labels[LabelKueuePodsetKey]; ok {
		return true
	}

	return false
}
