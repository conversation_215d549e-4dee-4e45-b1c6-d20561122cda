package consts

const (
	LabelKeyCacheEnabled = "pipelines.kubeflow.org/cache_enabled"

	// const belong llmops product
	LabelLLMOpsNameKey   = "llmops.transwarp.io/name"
	LabelLLMOpsNameValue = "llmops"
	// value is llmops installed namespace
	LabelLLMOpsInstanceKey = "llmops.transwarp.io/instance"

	LabelLLMOpsPartOfKey = "llmops.transwarp.io/part-of"

	LabelLLMOpsComponentKey   = "llmops.transwarp.io/component"
	LabelLLMOpsComponentValue = "pipeline"

	// value is llmops installed namespace
	LabelLLMOpsManagedByKey = "llmops.transwarp.io/managed-by"

	LabelLLMOpsVersionKey = "llmops.transwarp.io/version"

	// task label
	LabelTaskManagedByKey = "task.transwarp.io/managed-by"

	// kueue related label
	LabelKueueQueueNameKey = "kueue.x-k8s.io/queue-name"
	// [ns]-[high|medium|low], ex: llmops-high  llmops-low  llmops-medium
	LabelKueueWorkloadPriorityKey = "kueue.x-k8s.io/priority-class"

	LabelKueuePodGroupNameKey = "kueue.x-k8s.io/pod-group-name"
)

func ContainLabels(target, subset map[string]string) bool {
	if target == nil || subset == nil {
		return false
	}

	for key, value := range subset {
		targetValue, exists := target[key]
		if !exists || targetValue != value {
			return false
		}
	}
	return true
}
