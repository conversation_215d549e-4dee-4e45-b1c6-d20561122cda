package dao

import (
	"context"
	"fmt"
	"os"
	"strings"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"transwarp.io/mlops/mlops-std/conf"
	"transwarp.io/mlops/mlops-std/database"
	"transwarp.io/mlops/mlops-std/stderr"
	logger "transwarp.io/mlops/mlops-std/stdlog"
	"transwarp.io/mlops/mlops-std/util"
	config "transwarp.io/mlops/pipeline/conf"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
)

var PipelineDAOInst *PipelineDAO
var PipelineVersionDAOInst *PipelineVersionDAO
var RunDAOInst *RunDAO
var JobDAOInst *JobDAO
var MetricDAOInst *MetricDAO
var ExperimentDAOInst *ExperimentDAO

func Init() {
	metaDb := database.CreateDB(config.PipeineConfig.MLOPS.Datasource)

	mysqlDAO, err := NewDAO(metaDb)
	if err != nil {
		logger.Error(err)
	}
	mysqlDAO.AutoMigrate()

	mlpipelineDatasource, err := initMlpipelineDatasource()
	if err != nil {
		panic(err)
	}
	logger.Info("init ml pipeline db config:", util.ToJson(mlpipelineDatasource))
	mlPipelineSb := database.CreateDB(*mlpipelineDatasource)
	pipelineDAO, err := NewDAO(mlPipelineSb)
	if err != nil {
		logger.Error(err)
	}

	PipelineDAOInst = NewPipelineDAO(mysqlDAO)
	PipelineVersionDAOInst = NewPipelineVersionDAO(mysqlDAO)
	MetricDAOInst = NewMetricDAO(mysqlDAO)
	RunDAOInst = NewRunDAO(pipelineDAO)
	JobDAOInst = NewJobDAO(pipelineDAO)
	ExperimentDAOInst = NewExperimentDAO(pipelineDAO)
}

func initMlpipelineDatasource() (*conf.Datasource, error) {
	var dbHost string
	var dbPort string
	var username []byte
	var password []byte
	configMaps, err := client.K8sClient.Client.CoreV1().ConfigMaps(config.PipeineConfig.MLOPS.Pipeline.Namespace).List(context.Background(), metav1.ListOptions{
		LabelSelector: fmt.Sprintf("%s=%s", LabelKeyApplicationCrdId, MlPipelineLabelValueApplicationCrdId),
	})
	if err != nil {
		return nil, err
	}
	if configMaps.Items == nil || len(configMaps.Items) == 0 {
		return nil, stderr.PipelineConfigMapIsNotFound.Error()
	}

	for _, cm := range configMaps.Items {
		if strings.HasPrefix(cm.Name, "pipeline-install-config") {
			dbHost = cm.Data["dbHost"]
			dbPort = cm.Data["dbPort"]
		}
	}

	secrets, err := client.K8sClient.Client.CoreV1().Secrets(config.PipeineConfig.MLOPS.Pipeline.Namespace).List(context.Background(), metav1.ListOptions{
		LabelSelector: fmt.Sprintf("%s=%s", LabelKeyApplicationCrdId, MlPipelineLabelValueApplicationCrdId),
	})
	if err != nil {
		return nil, err
	}
	if secrets.Items == nil || len(secrets.Items) == 0 {
		return nil, stderr.PipelineSecretIsNotFound.Error()
	}
	for _, secret := range secrets.Items {
		if strings.HasPrefix(secret.Name, "mysql-secret") {
			username = secret.Data["username"]
			password = secret.Data["password"]
		}
	}

	if dbHost == "" || dbPort == "" || string(username) == "" || string(password) == "" {
		logger.Error("dbHost", dbHost, "dbPort", dbPort, "username", username, "password", password)
		return nil, stderr.PipelineMlDbConfigMiss.Error()
	}
	if os.Getenv("LocalDebug") == "true" {
		dbHost = config.PipeineConfig.MLOPS.Datasource.Host
		dbPort = config.PipeineConfig.MLOPS.Datasource.Port
	}

	return &conf.Datasource{
		Driver:         database.MySQL,
		Username:       string(username),
		Password:       string(password),
		Host:           dbHost,
		Port:           dbPort,
		DBName:         MlPipelineDbName,
		MaxIdle:        20,
		MaxConn:        10,
		NotPrintSql:    false,
		NotCreateTable: false,
	}, nil

}
