// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTask = "tasks"

// Task mapped from table <tasks>
type Task struct {
	ID               string    `gorm:"column:id;primaryKey;comment:ID" json:"id"`                                       // ID
	Name             string    `gorm:"column:name;comment:Name" json:"name"`                                            // Name
	Type             string    `gorm:"column:type;comment:Type" json:"type"`                                            // Type
	Priority         string    `gorm:"column:priority;comment:Priority" json:"priority"`                                // Priority
	Description      string    `gorm:"column:description;comment:Description" json:"description"`                       // Description
	Creator          string    `gorm:"column:creator;comment:Creator" json:"creator"`                                   // Creator
	CreateAt         time.Time `gorm:"column:create_at;default:CURRENT_TIMESTAMP;comment:create time" json:"create_at"` // create time
	UpdateAt         time.Time `gorm:"column:update_at;default:CURRENT_TIMESTAMP;comment:update time" json:"update_at"` // update time
	Status           string    `gorm:"column:status;comment:Status" json:"status"`                                      // Status
	StartAt          time.Time `gorm:"column:start_at;comment:start time" json:"start_at"`                              // start time
	EndAt            time.Time `gorm:"column:end_at;comment:end time" json:"end_at"`                                    // end time
	TenantID         string    `gorm:"column:tenant_id;comment:TenantID" json:"tenant_id"`                              // TenantID
	TenantName       string    `gorm:"column:tenant_name;comment:TenantName" json:"tenant_name"`                        // TenantName
	ProjectID        string    `gorm:"column:project_id;comment:ProjectID" json:"project_id"`                           // ProjectID
	ProjectName      string    `gorm:"column:project_name;comment:ProjectName" json:"project_name"`                     // ProjectName
	RawManifest      string    `gorm:"column:raw_manifest;comment:RawManifest" json:"raw_manifest"`                     // RawManifest
	Resources        string    `gorm:"column:resources;comment:Resources json" json:"resources"`                        // Resources json
	RunningNodes     string    `gorm:"column:running_nodes;comment:RunningNodes json" json:"running_nodes"`             // RunningNodes json
	Extra            string    `gorm:"column:extra;comment:Extra json" json:"extra"`                                    // Extra json
	SubTasks         string    `gorm:"column:sub_tasks;comment:sub task json" json:"sub_tasks"`                         // sub task json
	QueueName        string    `gorm:"column:queue_name;comment:QueueName" json:"queue_name"`                           // QueueName
	ClusterQueueName string    `gorm:"column:cluster_queue_name;comment:ClusterQueueName" json:"cluster_queue_name"`    // ClusterQueueName
	Namespace        string    `gorm:"column:namespace;comment:Namespace" json:"namespace"`                             // Namespace
	Labels           string    `gorm:"column:labels;comment:Labels json" json:"labels"`                                 // Labels json
	Annotations      string    `gorm:"column:annotations;comment:Annotations json" json:"annotations"`                  // Annotations json
	Gvk              string    `gorm:"column:gvk;comment:GVK json" json:"gvk"`                                          // GVK json
	Gvr              string    `gorm:"column:gvr;comment:GVR json" json:"gvr"`                                          // GVR json
}

// TableName Task's table name
func (*Task) TableName() string {
	return TableNameTask
}
