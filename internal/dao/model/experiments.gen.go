// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameExperiment = "experiments"

// Experiment mapped from table <experiments>
type Experiment struct {
	UUID           string `gorm:"column:UUID;primaryKey" json:"UUID"`
	Name           string `gorm:"column:Name;not null" json:"Name"`
	Description    string `gorm:"column:Description;not null" json:"Description"`
	CreatedAtInSec int64  `gorm:"column:CreatedAtInSec;not null" json:"CreatedAtInSec"`
	Namespace      string `gorm:"column:Namespace;not null" json:"Namespace"`
	StorageState   string `gorm:"column:StorageState;not null" json:"StorageState"`
}

// TableName Experiment's table name
func (*Experiment) TableName() string {
	return TableNameExperiment
}
