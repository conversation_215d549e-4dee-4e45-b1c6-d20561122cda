// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameResourceReference = "resource_references"

// ResourceReference mapped from table <resource_references>
type ResourceReference struct {
	ResourceUUID  string `gorm:"column:ResourceUUID;primaryKey" json:"ResourceUUID"`
	ResourceType  string `gorm:"column:ResourceType;primaryKey" json:"ResourceType"`
	ReferenceUUID string `gorm:"column:ReferenceUUID;not null" json:"ReferenceUUID"`
	ReferenceName string `gorm:"column:ReferenceName;not null" json:"ReferenceName"`
	ReferenceType string `gorm:"column:ReferenceType;primaryKey" json:"ReferenceType"`
	Relationship  string `gorm:"column:Relationship;not null" json:"Relationship"`
	Payload       string `gorm:"column:Payload;not null" json:"Payload"`
}

// TableName ResourceReference's table name
func (*ResourceReference) TableName() string {
	return TableNameResourceReference
}
