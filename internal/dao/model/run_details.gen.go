// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameRun = "run_details"

// Run mapped from table <run_details>
type Run struct {
	UUID                    string `gorm:"column:UUID;primaryKey" json:"UUID"`
	ExperimentUUID          string `gorm:"column:ExperimentUUID;not null" json:"ExperimentUUID"`
	DisplayName             string `gorm:"column:DisplayName;not null" json:"DisplayName"`
	Name                    string `gorm:"column:Name;not null" json:"Name"`
	StorageState            string `gorm:"column:StorageState;not null" json:"StorageState"`
	Namespace               string `gorm:"column:Namespace;not null" json:"Namespace"`
	ServiceAccount          string `gorm:"column:ServiceAccount;not null" json:"ServiceAccount"`
	Description             string `gorm:"column:Description;not null" json:"Description"`
	CreatedAtInSec          int64  `gorm:"column:CreatedAtInSec;not null" json:"CreatedAtInSec"`
	ScheduledAtInSec        int64  `gorm:"column:ScheduledAtInSec" json:"ScheduledAtInSec"`
	FinishedAtInSec         int64  `gorm:"column:FinishedAtInSec" json:"FinishedAtInSec"`
	Conditions              string `gorm:"column:Conditions;not null" json:"Conditions"`
	PipelineID              string `gorm:"column:PipelineId;not null" json:"PipelineId"`
	PipelineName            string `gorm:"column:PipelineName;not null" json:"PipelineName"`
	PipelineSpecManifest    string `gorm:"column:PipelineSpecManifest" json:"PipelineSpecManifest"`
	WorkflowSpecManifest    string `gorm:"column:WorkflowSpecManifest;not null" json:"WorkflowSpecManifest"`
	Parameters              string `gorm:"column:Parameters" json:"Parameters"`
	PipelineRuntimeManifest string `gorm:"column:PipelineRuntimeManifest;not null" json:"PipelineRuntimeManifest"`
	WorkflowRuntimeManifest string `gorm:"column:WorkflowRuntimeManifest;not null" json:"WorkflowRuntimeManifest"`
	SophonID                string `gorm:"column:SophonId" json:"SophonId"`
	ProjectID               string `gorm:"column:ProjectId" json:"ProjectId"`
	SourceType              string `gorm:"column:SourceType" json:"SourceType"`
	Owner                   string `gorm:"column:Owner" json:"Owner"`
	SourceID                string `gorm:"column:SourceId" json:"SourceId"`
	SourceName              string `gorm:"column:SourceName" json:"SourceName"`
}

// TableName Run's table name
func (*Run) TableName() string {
	return TableNameRun
}
