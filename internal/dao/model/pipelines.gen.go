// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNamePipeline = "pipelines"

// Pipeline mapped from table <pipelines>
type Pipeline struct {
	ID         string    `gorm:"column:id;primaryKey;comment:ID" json:"id"`                                    // ID
	CreateTime time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"` // 创建时间
	UpdateTime time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"` // 更新时间
	Name       string    `gorm:"column:name;not null;comment:名称" json:"name"`                                  // 名称
	Desc       string    `gorm:"column:desc;comment:描述" json:"desc"`                                           // 描述
	CreateUser string    `gorm:"column:create_user;comment:创建用户" json:"create_user"`                           // 创建用户
	ProjectID  string    `gorm:"column:project_id;comment:项目ID" json:"project_id"`                             // 项目ID
}

// TableName Pipeline's table name
func (*Pipeline) TableName() string {
	return TableNamePipeline
}
