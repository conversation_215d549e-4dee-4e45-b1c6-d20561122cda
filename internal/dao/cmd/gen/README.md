## 自动生成代码
该目录下存放一些用于自动生成DAO代码的脚本

参考 [GEN: Friendly & Safer GORM powered by Code Generation.](https://gorm.io/zh_CN/gen/index.html)

### 安装
> go get -u gorm.io/gen

### Database To Structs
> go run gen_models.go


[//]: # (### 生成DAO层代码)

[//]: # (> go run gen_dao.go)

或者使用gentool工具
https://gorm.io/zh_CN/gen/gen_tool.html

安装
> go install gorm.io/gen/tools/gentool@latest

在当前目录执行
> gentool -dsn "root:transwarp123@(172.16.251.83:30858)/template?charset=utf8mb4&parseTime=True&loc=Local" -tables "users,stuffs" -modelPkgName "../models" -outPath "../query"

