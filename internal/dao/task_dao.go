package dao

import (
	"context"
	"fmt"

	"transwarp.io/mlops/pipeline/internal/dao/model"
)

type TaskDAO struct {
	*CommonDAO
}

func NewTaskDAO(dao *CommonDAO) *TaskDAO {
	taskDAO := &TaskDAO{
		dao,
	}
	return taskDAO
}

func (dao *TaskDAO) Save(ctx context.Context, task *model.Task) (*model.Task, error) {
	q := dao.Task
	if err := q.Save(task); err != nil {
		return nil, fmt.Errorf("failed to save task: %w", err)
	}
	return task, nil
}

func (dao *TaskDAO) Get(ctx context.Context, id string) (*model.Task, error) {
	q := dao.Task
	return q.Where(q.ID.Eq(id)).First()
}

func (dao *TaskDAO) Delete(ctx context.Context, id string) error {
	q := dao.Task
	_, err := q.Where(q.ID.Eq(id)).Delete(&model.Task{})
	return err
}
