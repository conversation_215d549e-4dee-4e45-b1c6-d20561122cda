// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/mlops/pipeline/internal/dao/model"
)

func newPipeline(db *gorm.DB, opts ...gen.DOOption) pipeline {
	_pipeline := pipeline{}

	_pipeline.pipelineDo.UseDB(db, opts...)
	_pipeline.pipelineDo.UseModel(&model.Pipeline{})

	tableName := _pipeline.pipelineDo.TableName()
	_pipeline.ALL = field.NewAsterisk(tableName)
	_pipeline.ID = field.NewString(tableName, "id")
	_pipeline.CreateTime = field.NewTime(tableName, "create_time")
	_pipeline.UpdateTime = field.NewTime(tableName, "update_time")
	_pipeline.Name = field.NewString(tableName, "name")
	_pipeline.Desc = field.NewString(tableName, "desc")
	_pipeline.CreateUser = field.NewString(tableName, "create_user")
	_pipeline.ProjectID = field.NewString(tableName, "project_id")

	_pipeline.fillFieldMap()

	return _pipeline
}

type pipeline struct {
	pipelineDo

	ALL        field.Asterisk
	ID         field.String // ID
	CreateTime field.Time   // 创建时间
	UpdateTime field.Time   // 更新时间
	Name       field.String // 名称
	Desc       field.String // 描述
	CreateUser field.String // 创建用户
	ProjectID  field.String // 项目ID

	fieldMap map[string]field.Expr
}

func (p pipeline) Table(newTableName string) *pipeline {
	p.pipelineDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pipeline) As(alias string) *pipeline {
	p.pipelineDo.DO = *(p.pipelineDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pipeline) updateTableName(table string) *pipeline {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.CreateTime = field.NewTime(table, "create_time")
	p.UpdateTime = field.NewTime(table, "update_time")
	p.Name = field.NewString(table, "name")
	p.Desc = field.NewString(table, "desc")
	p.CreateUser = field.NewString(table, "create_user")
	p.ProjectID = field.NewString(table, "project_id")

	p.fillFieldMap()

	return p
}

func (p *pipeline) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pipeline) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 7)
	p.fieldMap["id"] = p.ID
	p.fieldMap["create_time"] = p.CreateTime
	p.fieldMap["update_time"] = p.UpdateTime
	p.fieldMap["name"] = p.Name
	p.fieldMap["desc"] = p.Desc
	p.fieldMap["create_user"] = p.CreateUser
	p.fieldMap["project_id"] = p.ProjectID
}

func (p pipeline) clone(db *gorm.DB) pipeline {
	p.pipelineDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p pipeline) replaceDB(db *gorm.DB) pipeline {
	p.pipelineDo.ReplaceDB(db)
	return p
}

type pipelineDo struct{ gen.DO }

func (p pipelineDo) Debug() *pipelineDo {
	return p.withDO(p.DO.Debug())
}

func (p pipelineDo) WithContext(ctx context.Context) *pipelineDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pipelineDo) ReadDB() *pipelineDo {
	return p.Clauses(dbresolver.Read)
}

func (p pipelineDo) WriteDB() *pipelineDo {
	return p.Clauses(dbresolver.Write)
}

func (p pipelineDo) Session(config *gorm.Session) *pipelineDo {
	return p.withDO(p.DO.Session(config))
}

func (p pipelineDo) Clauses(conds ...clause.Expression) *pipelineDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pipelineDo) Returning(value interface{}, columns ...string) *pipelineDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pipelineDo) Not(conds ...gen.Condition) *pipelineDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pipelineDo) Or(conds ...gen.Condition) *pipelineDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pipelineDo) Select(conds ...field.Expr) *pipelineDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pipelineDo) Where(conds ...gen.Condition) *pipelineDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pipelineDo) Order(conds ...field.Expr) *pipelineDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pipelineDo) Distinct(cols ...field.Expr) *pipelineDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pipelineDo) Omit(cols ...field.Expr) *pipelineDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pipelineDo) Join(table schema.Tabler, on ...field.Expr) *pipelineDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pipelineDo) LeftJoin(table schema.Tabler, on ...field.Expr) *pipelineDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pipelineDo) RightJoin(table schema.Tabler, on ...field.Expr) *pipelineDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pipelineDo) Group(cols ...field.Expr) *pipelineDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pipelineDo) Having(conds ...gen.Condition) *pipelineDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pipelineDo) Limit(limit int) *pipelineDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pipelineDo) Offset(offset int) *pipelineDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pipelineDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *pipelineDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pipelineDo) Unscoped() *pipelineDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pipelineDo) Create(values ...*model.Pipeline) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pipelineDo) CreateInBatches(values []*model.Pipeline, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pipelineDo) Save(values ...*model.Pipeline) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pipelineDo) First() (*model.Pipeline, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Pipeline), nil
	}
}

func (p pipelineDo) Take() (*model.Pipeline, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Pipeline), nil
	}
}

func (p pipelineDo) Last() (*model.Pipeline, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Pipeline), nil
	}
}

func (p pipelineDo) Find() ([]*model.Pipeline, error) {
	result, err := p.DO.Find()
	return result.([]*model.Pipeline), err
}

func (p pipelineDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Pipeline, err error) {
	buf := make([]*model.Pipeline, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pipelineDo) FindInBatches(result *[]*model.Pipeline, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pipelineDo) Attrs(attrs ...field.AssignExpr) *pipelineDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pipelineDo) Assign(attrs ...field.AssignExpr) *pipelineDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pipelineDo) Joins(fields ...field.RelationField) *pipelineDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pipelineDo) Preload(fields ...field.RelationField) *pipelineDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pipelineDo) FirstOrInit() (*model.Pipeline, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Pipeline), nil
	}
}

func (p pipelineDo) FirstOrCreate() (*model.Pipeline, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Pipeline), nil
	}
}

func (p pipelineDo) FindByPage(offset int, limit int) (result []*model.Pipeline, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pipelineDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pipelineDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pipelineDo) Delete(models ...*model.Pipeline) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pipelineDo) withDO(do gen.Dao) *pipelineDo {
	p.DO = *do.(*gen.DO)
	return p
}
