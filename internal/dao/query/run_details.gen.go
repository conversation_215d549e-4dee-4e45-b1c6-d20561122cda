// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/mlops/pipeline/internal/dao/model"
)

func newRun(db *gorm.DB, opts ...gen.DOOption) run {
	_run := run{}

	_run.runDo.UseDB(db, opts...)
	_run.runDo.UseModel(&model.Run{})

	tableName := _run.runDo.TableName()
	_run.ALL = field.NewAsterisk(tableName)
	_run.UUID = field.NewString(tableName, "UUID")
	_run.ExperimentUUID = field.NewString(tableName, "ExperimentUUID")
	_run.DisplayName = field.NewString(tableName, "DisplayName")
	_run.Name = field.NewString(tableName, "Name")
	_run.StorageState = field.NewString(tableName, "StorageState")
	_run.Namespace = field.NewString(tableName, "Namespace")
	_run.ServiceAccount = field.NewString(tableName, "ServiceAccount")
	_run.Description = field.NewString(tableName, "Description")
	_run.CreatedAtInSec = field.NewInt64(tableName, "CreatedAtInSec")
	_run.ScheduledAtInSec = field.NewInt64(tableName, "ScheduledAtInSec")
	_run.FinishedAtInSec = field.NewInt64(tableName, "FinishedAtInSec")
	_run.Conditions = field.NewString(tableName, "Conditions")
	_run.PipelineID = field.NewString(tableName, "PipelineId")
	_run.PipelineName = field.NewString(tableName, "PipelineName")
	_run.PipelineSpecManifest = field.NewString(tableName, "PipelineSpecManifest")
	_run.WorkflowSpecManifest = field.NewString(tableName, "WorkflowSpecManifest")
	_run.Parameters = field.NewString(tableName, "Parameters")
	_run.PipelineRuntimeManifest = field.NewString(tableName, "PipelineRuntimeManifest")
	_run.WorkflowRuntimeManifest = field.NewString(tableName, "WorkflowRuntimeManifest")
	_run.SophonID = field.NewString(tableName, "SophonId")
	_run.ProjectID = field.NewString(tableName, "ProjectId")
	_run.SourceType = field.NewString(tableName, "SourceType")
	_run.Owner = field.NewString(tableName, "Owner")
	_run.SourceID = field.NewString(tableName, "SourceId")
	_run.SourceName = field.NewString(tableName, "SourceName")

	_run.fillFieldMap()

	return _run
}

type run struct {
	runDo

	ALL                     field.Asterisk
	UUID                    field.String
	ExperimentUUID          field.String
	DisplayName             field.String
	Name                    field.String
	StorageState            field.String
	Namespace               field.String
	ServiceAccount          field.String
	Description             field.String
	CreatedAtInSec          field.Int64
	ScheduledAtInSec        field.Int64
	FinishedAtInSec         field.Int64
	Conditions              field.String
	PipelineID              field.String
	PipelineName            field.String
	PipelineSpecManifest    field.String
	WorkflowSpecManifest    field.String
	Parameters              field.String
	PipelineRuntimeManifest field.String
	WorkflowRuntimeManifest field.String
	SophonID                field.String
	ProjectID               field.String
	SourceType              field.String
	Owner                   field.String
	SourceID                field.String
	SourceName              field.String

	fieldMap map[string]field.Expr
}

func (r run) Table(newTableName string) *run {
	r.runDo.UseTable(newTableName)
	return r.updateTableName(newTableName)
}

func (r run) As(alias string) *run {
	r.runDo.DO = *(r.runDo.As(alias).(*gen.DO))
	return r.updateTableName(alias)
}

func (r *run) updateTableName(table string) *run {
	r.ALL = field.NewAsterisk(table)
	r.UUID = field.NewString(table, "UUID")
	r.ExperimentUUID = field.NewString(table, "ExperimentUUID")
	r.DisplayName = field.NewString(table, "DisplayName")
	r.Name = field.NewString(table, "Name")
	r.StorageState = field.NewString(table, "StorageState")
	r.Namespace = field.NewString(table, "Namespace")
	r.ServiceAccount = field.NewString(table, "ServiceAccount")
	r.Description = field.NewString(table, "Description")
	r.CreatedAtInSec = field.NewInt64(table, "CreatedAtInSec")
	r.ScheduledAtInSec = field.NewInt64(table, "ScheduledAtInSec")
	r.FinishedAtInSec = field.NewInt64(table, "FinishedAtInSec")
	r.Conditions = field.NewString(table, "Conditions")
	r.PipelineID = field.NewString(table, "PipelineId")
	r.PipelineName = field.NewString(table, "PipelineName")
	r.PipelineSpecManifest = field.NewString(table, "PipelineSpecManifest")
	r.WorkflowSpecManifest = field.NewString(table, "WorkflowSpecManifest")
	r.Parameters = field.NewString(table, "Parameters")
	r.PipelineRuntimeManifest = field.NewString(table, "PipelineRuntimeManifest")
	r.WorkflowRuntimeManifest = field.NewString(table, "WorkflowRuntimeManifest")
	r.SophonID = field.NewString(table, "SophonId")
	r.ProjectID = field.NewString(table, "ProjectId")
	r.SourceType = field.NewString(table, "SourceType")
	r.Owner = field.NewString(table, "Owner")
	r.SourceID = field.NewString(table, "SourceId")
	r.SourceName = field.NewString(table, "SourceName")

	r.fillFieldMap()

	return r
}

func (r *run) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := r.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (r *run) fillFieldMap() {
	r.fieldMap = make(map[string]field.Expr, 25)
	r.fieldMap["UUID"] = r.UUID
	r.fieldMap["ExperimentUUID"] = r.ExperimentUUID
	r.fieldMap["DisplayName"] = r.DisplayName
	r.fieldMap["Name"] = r.Name
	r.fieldMap["StorageState"] = r.StorageState
	r.fieldMap["Namespace"] = r.Namespace
	r.fieldMap["ServiceAccount"] = r.ServiceAccount
	r.fieldMap["Description"] = r.Description
	r.fieldMap["CreatedAtInSec"] = r.CreatedAtInSec
	r.fieldMap["ScheduledAtInSec"] = r.ScheduledAtInSec
	r.fieldMap["FinishedAtInSec"] = r.FinishedAtInSec
	r.fieldMap["Conditions"] = r.Conditions
	r.fieldMap["PipelineId"] = r.PipelineID
	r.fieldMap["PipelineName"] = r.PipelineName
	r.fieldMap["PipelineSpecManifest"] = r.PipelineSpecManifest
	r.fieldMap["WorkflowSpecManifest"] = r.WorkflowSpecManifest
	r.fieldMap["Parameters"] = r.Parameters
	r.fieldMap["PipelineRuntimeManifest"] = r.PipelineRuntimeManifest
	r.fieldMap["WorkflowRuntimeManifest"] = r.WorkflowRuntimeManifest
	r.fieldMap["SophonId"] = r.SophonID
	r.fieldMap["ProjectId"] = r.ProjectID
	r.fieldMap["SourceType"] = r.SourceType
	r.fieldMap["Owner"] = r.Owner
	r.fieldMap["SourceId"] = r.SourceID
	r.fieldMap["SourceName"] = r.SourceName
}

func (r run) clone(db *gorm.DB) run {
	r.runDo.ReplaceConnPool(db.Statement.ConnPool)
	return r
}

func (r run) replaceDB(db *gorm.DB) run {
	r.runDo.ReplaceDB(db)
	return r
}

type runDo struct{ gen.DO }

func (r runDo) Debug() *runDo {
	return r.withDO(r.DO.Debug())
}

func (r runDo) WithContext(ctx context.Context) *runDo {
	return r.withDO(r.DO.WithContext(ctx))
}

func (r runDo) ReadDB() *runDo {
	return r.Clauses(dbresolver.Read)
}

func (r runDo) WriteDB() *runDo {
	return r.Clauses(dbresolver.Write)
}

func (r runDo) Session(config *gorm.Session) *runDo {
	return r.withDO(r.DO.Session(config))
}

func (r runDo) Clauses(conds ...clause.Expression) *runDo {
	return r.withDO(r.DO.Clauses(conds...))
}

func (r runDo) Returning(value interface{}, columns ...string) *runDo {
	return r.withDO(r.DO.Returning(value, columns...))
}

func (r runDo) Not(conds ...gen.Condition) *runDo {
	return r.withDO(r.DO.Not(conds...))
}

func (r runDo) Or(conds ...gen.Condition) *runDo {
	return r.withDO(r.DO.Or(conds...))
}

func (r runDo) Select(conds ...field.Expr) *runDo {
	return r.withDO(r.DO.Select(conds...))
}

func (r runDo) Where(conds ...gen.Condition) *runDo {
	return r.withDO(r.DO.Where(conds...))
}

func (r runDo) Order(conds ...field.Expr) *runDo {
	return r.withDO(r.DO.Order(conds...))
}

func (r runDo) Distinct(cols ...field.Expr) *runDo {
	return r.withDO(r.DO.Distinct(cols...))
}

func (r runDo) Omit(cols ...field.Expr) *runDo {
	return r.withDO(r.DO.Omit(cols...))
}

func (r runDo) Join(table schema.Tabler, on ...field.Expr) *runDo {
	return r.withDO(r.DO.Join(table, on...))
}

func (r runDo) LeftJoin(table schema.Tabler, on ...field.Expr) *runDo {
	return r.withDO(r.DO.LeftJoin(table, on...))
}

func (r runDo) RightJoin(table schema.Tabler, on ...field.Expr) *runDo {
	return r.withDO(r.DO.RightJoin(table, on...))
}

func (r runDo) Group(cols ...field.Expr) *runDo {
	return r.withDO(r.DO.Group(cols...))
}

func (r runDo) Having(conds ...gen.Condition) *runDo {
	return r.withDO(r.DO.Having(conds...))
}

func (r runDo) Limit(limit int) *runDo {
	return r.withDO(r.DO.Limit(limit))
}

func (r runDo) Offset(offset int) *runDo {
	return r.withDO(r.DO.Offset(offset))
}

func (r runDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *runDo {
	return r.withDO(r.DO.Scopes(funcs...))
}

func (r runDo) Unscoped() *runDo {
	return r.withDO(r.DO.Unscoped())
}

func (r runDo) Create(values ...*model.Run) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Create(values)
}

func (r runDo) CreateInBatches(values []*model.Run, batchSize int) error {
	return r.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (r runDo) Save(values ...*model.Run) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Save(values)
}

func (r runDo) First() (*model.Run, error) {
	if result, err := r.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Run), nil
	}
}

func (r runDo) Take() (*model.Run, error) {
	if result, err := r.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Run), nil
	}
}

func (r runDo) Last() (*model.Run, error) {
	if result, err := r.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Run), nil
	}
}

func (r runDo) Find() ([]*model.Run, error) {
	result, err := r.DO.Find()
	return result.([]*model.Run), err
}

func (r runDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Run, err error) {
	buf := make([]*model.Run, 0, batchSize)
	err = r.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (r runDo) FindInBatches(result *[]*model.Run, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return r.DO.FindInBatches(result, batchSize, fc)
}

func (r runDo) Attrs(attrs ...field.AssignExpr) *runDo {
	return r.withDO(r.DO.Attrs(attrs...))
}

func (r runDo) Assign(attrs ...field.AssignExpr) *runDo {
	return r.withDO(r.DO.Assign(attrs...))
}

func (r runDo) Joins(fields ...field.RelationField) *runDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Joins(_f))
	}
	return &r
}

func (r runDo) Preload(fields ...field.RelationField) *runDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Preload(_f))
	}
	return &r
}

func (r runDo) FirstOrInit() (*model.Run, error) {
	if result, err := r.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Run), nil
	}
}

func (r runDo) FirstOrCreate() (*model.Run, error) {
	if result, err := r.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Run), nil
	}
}

func (r runDo) FindByPage(offset int, limit int) (result []*model.Run, count int64, err error) {
	result, err = r.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = r.Offset(-1).Limit(-1).Count()
	return
}

func (r runDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = r.Count()
	if err != nil {
		return
	}

	err = r.Offset(offset).Limit(limit).Scan(result)
	return
}

func (r runDo) Scan(result interface{}) (err error) {
	return r.DO.Scan(result)
}

func (r runDo) Delete(models ...*model.Run) (result gen.ResultInfo, err error) {
	return r.DO.Delete(models)
}

func (r *runDo) withDO(do gen.Dao) *runDo {
	r.DO = *do.(*gen.DO)
	return r
}
