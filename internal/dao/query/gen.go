// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                db,
		Experiment:        newExperiment(db, opts...),
		Job:               newJob(db, opts...),
		Metric:            newMetric(db, opts...),
		Pipeline:          newPipeline(db, opts...),
		PipelineVersion:   newPipelineVersion(db, opts...),
		ResourceReference: newResourceReference(db, opts...),
		Run:               newRun(db, opts...),
		Task:              newTask(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	Experiment        experiment
	Job               job
	Metric            metric
	Pipeline          pipeline
	PipelineVersion   pipelineVersion
	ResourceReference resourceReference
	Run               run
	Task              task
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                db,
		Experiment:        q.Experiment.clone(db),
		Job:               q.Job.clone(db),
		Metric:            q.Metric.clone(db),
		Pipeline:          q.Pipeline.clone(db),
		PipelineVersion:   q.PipelineVersion.clone(db),
		ResourceReference: q.ResourceReference.clone(db),
		Run:               q.Run.clone(db),
		Task:              q.Task.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                db,
		Experiment:        q.Experiment.replaceDB(db),
		Job:               q.Job.replaceDB(db),
		Metric:            q.Metric.replaceDB(db),
		Pipeline:          q.Pipeline.replaceDB(db),
		PipelineVersion:   q.PipelineVersion.replaceDB(db),
		ResourceReference: q.ResourceReference.replaceDB(db),
		Run:               q.Run.replaceDB(db),
		Task:              q.Task.replaceDB(db),
	}
}

type queryCtx struct {
	Experiment        *experimentDo
	Job               *jobDo
	Metric            *metricDo
	Pipeline          *pipelineDo
	PipelineVersion   *pipelineVersionDo
	ResourceReference *resourceReferenceDo
	Run               *runDo
	Task              *taskDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		Experiment:        q.Experiment.WithContext(ctx),
		Job:               q.Job.WithContext(ctx),
		Metric:            q.Metric.WithContext(ctx),
		Pipeline:          q.Pipeline.WithContext(ctx),
		PipelineVersion:   q.PipelineVersion.WithContext(ctx),
		ResourceReference: q.ResourceReference.WithContext(ctx),
		Run:               q.Run.WithContext(ctx),
		Task:              q.Task.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
