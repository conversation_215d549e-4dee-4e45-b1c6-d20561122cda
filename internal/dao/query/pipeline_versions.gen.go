// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/mlops/pipeline/internal/dao/model"
)

func newPipelineVersion(db *gorm.DB, opts ...gen.DOOption) pipelineVersion {
	_pipelineVersion := pipelineVersion{}

	_pipelineVersion.pipelineVersionDo.UseDB(db, opts...)
	_pipelineVersion.pipelineVersionDo.UseModel(&model.PipelineVersion{})

	tableName := _pipelineVersion.pipelineVersionDo.TableName()
	_pipelineVersion.ALL = field.NewAsterisk(tableName)
	_pipelineVersion.ID = field.NewString(tableName, "id")
	_pipelineVersion.CreateTime = field.NewTime(tableName, "create_time")
	_pipelineVersion.UpdateTime = field.NewTime(tableName, "update_time")
	_pipelineVersion.PipelineID = field.NewString(tableName, "pipeline_id")
	_pipelineVersion.Name = field.NewString(tableName, "name")
	_pipelineVersion.TaskFlow = field.NewString(tableName, "task_flow")
	_pipelineVersion.Desc = field.NewString(tableName, "desc")
	_pipelineVersion.SchedulingStyle = field.NewInt32(tableName, "scheduling_style")
	_pipelineVersion.TimingConfigCron = field.NewString(tableName, "timing_config_cron")
	_pipelineVersion.TimingConfigScheduleType = field.NewInt32(tableName, "timing_config_schedule_type")
	_pipelineVersion.WorkflowAdvancedConfig = field.NewString(tableName, "workflow_advanced_config")
	_pipelineVersion.EventType = field.NewInt32(tableName, "event_type")
	_pipelineVersion.EventParams = field.NewString(tableName, "event_params")

	_pipelineVersion.fillFieldMap()

	return _pipelineVersion
}

type pipelineVersion struct {
	pipelineVersionDo

	ALL                      field.Asterisk
	ID                       field.String // ID
	CreateTime               field.Time   // 创建时间
	UpdateTime               field.Time   // 更新时间
	PipelineID               field.String // pipeline ID
	Name                     field.String // 名称
	TaskFlow                 field.String // 工作流程图json
	Desc                     field.String // 描述
	SchedulingStyle          field.Int32  // 调度方式，0手动调度，1定时调度
	TimingConfigCron         field.String // 调度周期cron表达式
	TimingConfigScheduleType field.Int32  // 定时调度设置，0基础 1高级
	WorkflowAdvancedConfig   field.String // 工作流高级配置json
	EventType                field.Int32  // 事件调度类型，当前仅支持0：FileAssets
	EventParams              field.String // 事件相关参数json

	fieldMap map[string]field.Expr
}

func (p pipelineVersion) Table(newTableName string) *pipelineVersion {
	p.pipelineVersionDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pipelineVersion) As(alias string) *pipelineVersion {
	p.pipelineVersionDo.DO = *(p.pipelineVersionDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pipelineVersion) updateTableName(table string) *pipelineVersion {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.CreateTime = field.NewTime(table, "create_time")
	p.UpdateTime = field.NewTime(table, "update_time")
	p.PipelineID = field.NewString(table, "pipeline_id")
	p.Name = field.NewString(table, "name")
	p.TaskFlow = field.NewString(table, "task_flow")
	p.Desc = field.NewString(table, "desc")
	p.SchedulingStyle = field.NewInt32(table, "scheduling_style")
	p.TimingConfigCron = field.NewString(table, "timing_config_cron")
	p.TimingConfigScheduleType = field.NewInt32(table, "timing_config_schedule_type")
	p.WorkflowAdvancedConfig = field.NewString(table, "workflow_advanced_config")
	p.EventType = field.NewInt32(table, "event_type")
	p.EventParams = field.NewString(table, "event_params")

	p.fillFieldMap()

	return p
}

func (p *pipelineVersion) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pipelineVersion) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 13)
	p.fieldMap["id"] = p.ID
	p.fieldMap["create_time"] = p.CreateTime
	p.fieldMap["update_time"] = p.UpdateTime
	p.fieldMap["pipeline_id"] = p.PipelineID
	p.fieldMap["name"] = p.Name
	p.fieldMap["task_flow"] = p.TaskFlow
	p.fieldMap["desc"] = p.Desc
	p.fieldMap["scheduling_style"] = p.SchedulingStyle
	p.fieldMap["timing_config_cron"] = p.TimingConfigCron
	p.fieldMap["timing_config_schedule_type"] = p.TimingConfigScheduleType
	p.fieldMap["workflow_advanced_config"] = p.WorkflowAdvancedConfig
	p.fieldMap["event_type"] = p.EventType
	p.fieldMap["event_params"] = p.EventParams
}

func (p pipelineVersion) clone(db *gorm.DB) pipelineVersion {
	p.pipelineVersionDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p pipelineVersion) replaceDB(db *gorm.DB) pipelineVersion {
	p.pipelineVersionDo.ReplaceDB(db)
	return p
}

type pipelineVersionDo struct{ gen.DO }

func (p pipelineVersionDo) Debug() *pipelineVersionDo {
	return p.withDO(p.DO.Debug())
}

func (p pipelineVersionDo) WithContext(ctx context.Context) *pipelineVersionDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pipelineVersionDo) ReadDB() *pipelineVersionDo {
	return p.Clauses(dbresolver.Read)
}

func (p pipelineVersionDo) WriteDB() *pipelineVersionDo {
	return p.Clauses(dbresolver.Write)
}

func (p pipelineVersionDo) Session(config *gorm.Session) *pipelineVersionDo {
	return p.withDO(p.DO.Session(config))
}

func (p pipelineVersionDo) Clauses(conds ...clause.Expression) *pipelineVersionDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pipelineVersionDo) Returning(value interface{}, columns ...string) *pipelineVersionDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pipelineVersionDo) Not(conds ...gen.Condition) *pipelineVersionDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pipelineVersionDo) Or(conds ...gen.Condition) *pipelineVersionDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pipelineVersionDo) Select(conds ...field.Expr) *pipelineVersionDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pipelineVersionDo) Where(conds ...gen.Condition) *pipelineVersionDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pipelineVersionDo) Order(conds ...field.Expr) *pipelineVersionDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pipelineVersionDo) Distinct(cols ...field.Expr) *pipelineVersionDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pipelineVersionDo) Omit(cols ...field.Expr) *pipelineVersionDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pipelineVersionDo) Join(table schema.Tabler, on ...field.Expr) *pipelineVersionDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pipelineVersionDo) LeftJoin(table schema.Tabler, on ...field.Expr) *pipelineVersionDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pipelineVersionDo) RightJoin(table schema.Tabler, on ...field.Expr) *pipelineVersionDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pipelineVersionDo) Group(cols ...field.Expr) *pipelineVersionDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pipelineVersionDo) Having(conds ...gen.Condition) *pipelineVersionDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pipelineVersionDo) Limit(limit int) *pipelineVersionDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pipelineVersionDo) Offset(offset int) *pipelineVersionDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pipelineVersionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *pipelineVersionDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pipelineVersionDo) Unscoped() *pipelineVersionDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pipelineVersionDo) Create(values ...*model.PipelineVersion) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pipelineVersionDo) CreateInBatches(values []*model.PipelineVersion, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pipelineVersionDo) Save(values ...*model.PipelineVersion) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pipelineVersionDo) First() (*model.PipelineVersion, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.PipelineVersion), nil
	}
}

func (p pipelineVersionDo) Take() (*model.PipelineVersion, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.PipelineVersion), nil
	}
}

func (p pipelineVersionDo) Last() (*model.PipelineVersion, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.PipelineVersion), nil
	}
}

func (p pipelineVersionDo) Find() ([]*model.PipelineVersion, error) {
	result, err := p.DO.Find()
	return result.([]*model.PipelineVersion), err
}

func (p pipelineVersionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PipelineVersion, err error) {
	buf := make([]*model.PipelineVersion, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pipelineVersionDo) FindInBatches(result *[]*model.PipelineVersion, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pipelineVersionDo) Attrs(attrs ...field.AssignExpr) *pipelineVersionDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pipelineVersionDo) Assign(attrs ...field.AssignExpr) *pipelineVersionDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pipelineVersionDo) Joins(fields ...field.RelationField) *pipelineVersionDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pipelineVersionDo) Preload(fields ...field.RelationField) *pipelineVersionDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pipelineVersionDo) FirstOrInit() (*model.PipelineVersion, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.PipelineVersion), nil
	}
}

func (p pipelineVersionDo) FirstOrCreate() (*model.PipelineVersion, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.PipelineVersion), nil
	}
}

func (p pipelineVersionDo) FindByPage(offset int, limit int) (result []*model.PipelineVersion, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pipelineVersionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pipelineVersionDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pipelineVersionDo) Delete(models ...*model.PipelineVersion) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pipelineVersionDo) withDO(do gen.Dao) *pipelineVersionDo {
	p.DO = *do.(*gen.DO)
	return p
}
