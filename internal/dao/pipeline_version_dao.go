package dao

import (
	"github.com/google/uuid"
	"transwarp.io/mlops/pipeline/internal/dao/model"
)

type PipelineVersionDAO struct {
	*CommonDAO
}

func NewPipelineVersionDAO(dao *CommonDAO) *PipelineVersionDAO {
	pipelineVersionDAO := &PipelineVersionDAO{
		dao,
	}
	return pipelineVersionDAO
}

func (dao *PipelineVersionDAO) DeleteByPipelineId(pipelineIds ...string) error {
	q := dao.PipelineVersion
	_, err := q.Where(q.PipelineID.In(pipelineIds...)).Delete()
	return err
}

func (dao *PipelineVersionDAO) Delete(Id string) error {
	q := dao.PipelineVersion
	_, err := q.Where(q.ID.Eq(Id)).Delete()
	return err
}

func (dao *PipelineVersionDAO) DeleteByIds(Ids []string) error {
	q := dao.PipelineVersion
	_, err := q.Where(q.ID.In(Ids...)).Delete()
	return err
}

func (dao *PipelineVersionDAO) GetById(id string) (*model.PipelineVersion, error) {
	q := dao.PipelineVersion
	return q.Where(q.ID.Eq(id)).First()
}

func (dao *PipelineVersionDAO) CountByNameAndPipelineId(name string, pipelineId string) (int64, error) {
	q := dao.PipelineVersion
	return q.Where(q.Name.Eq(name), q.PipelineID.Eq(pipelineId)).Count()
}

func (dao *PipelineVersionDAO) Create(pipelineVersion *model.PipelineVersion) (string, error) {
	q := dao.PipelineVersion
	if pipelineVersion.ID == "" {
		pipelineVersion.ID = uuid.New().String()
	}
	err := q.Create(pipelineVersion)
	return pipelineVersion.ID, err
}

func (dao *PipelineVersionDAO) Save(pipelineVersion *model.PipelineVersion) (string, error) {
	q := dao.PipelineVersion
	err := q.Save(pipelineVersion)
	return pipelineVersion.ID, err
}

func (dao *PipelineVersionDAO) List(pipelineIds ...string) ([]*model.PipelineVersion, error) {
	q := dao.PipelineVersion
	return q.Where(q.PipelineID.In(pipelineIds...)).Find()
}

func (dao *PipelineVersionDAO) CountVersionInPipelines() (map[string]int64, error) {
	q := dao.PipelineVersion
	var results []struct {
		PipelineID string
		Count      int64
	}
	err := q.Select(q.PipelineID, q.ID.Count().As("count")).Group(q.PipelineID).Scan(&results)
	if err != nil {
		return nil, err
	}
	countMap := map[string]int64{}
	for _, result := range results {
		countMap[result.PipelineID] = result.Count
	}
	return countMap, nil
}

func (dao *PipelineVersionDAO) CountVersionByPipelineId(pipelineId string) (int64, error) {
	q := dao.PipelineVersion
	return q.Where(q.PipelineID.Eq(pipelineId)).Count()
}
