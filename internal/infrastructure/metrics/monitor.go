package metrics

import (
	"context"
	"fmt"
	"sync"

	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

type MetricsMonitor struct {
	configManager *ConfigManager
	queryEngine   *QueryEngine
	cacheManager  *CacheManager
	collector     *MetricsCollector

	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
}

func NewMetricsMonitor(configPath string) (*MetricsMonitor, error) {
	configManager, err := NewConfigManager(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to create config manager: %w", err)
	}

	config := configManager.GetConfig()

	queryEngine, err := NewQueryEngine(&config.Prometheus)
	if err != nil {
		return nil, fmt.Errorf("failed to create query engine: %w", err)
	}

	var cacheManager *CacheManager
	if config.Cache.Enabled {
		cacheManager = NewCacheManager(config.Cache)
	}

	collector := NewMetricsCollector(queryEngine, configManager, cacheManager)

	return &MetricsMonitor{
		configManager: configManager,
		queryEngine:   queryEngine,
		cacheManager:  cacheManager,
		collector:     collector,
	}, nil
}

func (mm *MetricsMonitor) Start(ctx context.Context) error {
	mm.ctx, mm.cancel = context.WithCancel(ctx)

	if err := mm.collector.Start(mm.ctx); err != nil {
		return fmt.Errorf("failed to start collector: %w", err)
	}

	zlog.SugarInfof("Metrics manager started successfully")
	return nil
}

func (mm *MetricsMonitor) Stop() error {
	if mm.cancel != nil {
		mm.cancel()
	}

	if err := mm.collector.Stop(); err != nil {
		zlog.SugarErrorf("Failed to stop collector: %v", err)
	}

	if mm.cacheManager != nil {
		mm.cacheManager.Stop()
	}

	mm.wg.Wait()
	zlog.SugarInfof("Metrics manager stopped")
	return nil
}

func (mm *MetricsMonitor) GetResourceUsage(ctx context.Context, filters map[string]string) ([]*ResourceUsage, error) {
	return mm.collector.GetResourceUsage(ctx, filters)
}

func (mm *MetricsMonitor) Query(ctx context.Context, request *QueryRequest) (*QueryResult, error) {
	return mm.queryEngine.Query(ctx, request)
}

func (mm *MetricsMonitor) QueryRange(ctx context.Context, request *QueryRequest) (*QueryResult, error) {
	return mm.queryEngine.QueryRange(ctx, request)
}

func (mm *MetricsMonitor) Health(ctx context.Context) error {
	return mm.queryEngine.Health(ctx)
}

func (mm *MetricsMonitor) GetStatus() map[string]interface{} {
	status := map[string]interface{}{
		"collectors": mm.collector.GetCollectorStatus(),
	}

	if mm.cacheManager != nil {
		status["cache"] = mm.cacheManager.GetStats()
	}

	return status
}

func (mm *MetricsMonitor) ReloadConfig() error {
	return mm.configManager.ReloadConfig()
}
