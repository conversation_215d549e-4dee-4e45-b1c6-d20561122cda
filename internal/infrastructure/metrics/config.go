package metrics

import (
	"fmt"
	"os"
	"time"

	"gopkg.in/yaml.v3"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

type ConfigManager struct {
	config     *MetricsConfig
	configPath string
}

func NewConfigManager(configPath string) (*ConfigManager, error) {
	cm := &ConfigManager{
		configPath: configPath,
	}

	if err := cm.LoadConfig(); err != nil {
		return nil, err
	}

	return cm, nil
}

func (cm *ConfigManager) LoadConfig() error {
	data, err := os.ReadFile(cm.configPath)
	if err != nil {
		return fmt.Errorf("failed to read config file: %w", err)
	}

	var config MetricsConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		return fmt.Errorf("failed to parse config file: %w", err)
	}

	cm.setDefaults(&config)

	if err := cm.validateConfig(&config); err != nil {
		return fmt.Errorf("invalid config: %w", err)
	}

	cm.config = &config
	zlog.SugarInfof("Loaded metrics config from %s", cm.configPath)
	return nil
}

func (cm *ConfigManager) setDefaults(config *MetricsConfig) {
	if config.Prometheus.Timeout == 0 {
		config.Prometheus.Timeout = 30 * time.Second
	}
	if config.Prometheus.MaxRetries == 0 {
		config.Prometheus.MaxRetries = 3
	}
	if config.Prometheus.RetryDelay == 0 {
		config.Prometheus.RetryDelay = 1 * time.Second
	}

	if config.Cache.TTL == 0 {
		config.Cache.TTL = 5 * time.Minute
	}
	if config.Cache.MaxSize == 0 {
		config.Cache.MaxSize = 1000
	}
	if config.Cache.CleanupInterval == 0 {
		config.Cache.CleanupInterval = 10 * time.Minute
	}

	for name, metric := range config.Metrics {
		if metric.Interval == 0 {
			metric.Interval = 30 * time.Second
		}
		if metric.Timeout == 0 {
			metric.Timeout = 10 * time.Second
		}
		if metric.Type == "" {
			metric.Type = MetricTypeGauge
		}
		config.Metrics[name] = metric
	}
}

func (cm *ConfigManager) validateConfig(config *MetricsConfig) error {
	if config.Prometheus.Endpoint == "" {
		return fmt.Errorf("prometheus endpoint is required")
	}

	for name, metric := range config.Metrics {
		if metric.Query == "" {
			return fmt.Errorf("metric %s query is required", name)
		}
	}

	return nil
}

func (cm *ConfigManager) GetConfig() *MetricsConfig {
	return cm.config
}

func (cm *ConfigManager) GetMetricConfig(name string) (*MetricConfig, bool) {
	metric, exists := cm.config.Metrics[name]
	return &metric, exists
}

func (cm *ConfigManager) ReloadConfig() error {
	return cm.LoadConfig()
}

func GetDefaultMetricsConfig() *MetricsConfig {
	return &MetricsConfig{
		Prometheus: PrometheusConfig{
			Endpoint:   "http://prometheus:9090",
			Timeout:    30 * time.Second,
			MaxRetries: 3,
			RetryDelay: 1 * time.Second,
		},
		Cache: CacheConfig{
			Enabled:         true,
			TTL:             5 * time.Minute,
			MaxSize:         1000,
			CleanupInterval: 10 * time.Minute,
		},
		Metrics: map[string]MetricConfig{
			"node_cpu_usage": {
				Name:        "node_cpu_usage",
				Query:       `100 - (avg by (instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)`,
				Description: "Node CPU usage percentage",
				Unit:        "percent",
				Type:        MetricTypeGauge,
				Labels:      []string{"instance", "node"},
				Interval:    30 * time.Second,
			},
			"node_memory_usage": {
				Name:        "node_memory_usage",
				Query:       `(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100`,
				Description: "Node memory usage percentage",
				Unit:        "percent",
				Type:        MetricTypeGauge,
				Labels:      []string{"instance", "node"},
				Interval:    30 * time.Second,
			},
			"pod_cpu_usage": {
				Name:        "pod_cpu_usage",
				Query:       `sum(rate(container_cpu_usage_seconds_total{container!="POD",container!=""}[5m])) by (pod, namespace, node)`,
				Description: "Pod CPU usage",
				Unit:        "cores",
				Type:        MetricTypeGauge,
				Labels:      []string{"pod", "namespace", "node"},
				Interval:    30 * time.Second,
			},
			"pod_memory_usage": {
				Name:        "pod_memory_usage",
				Query:       `sum(container_memory_working_set_bytes{container!="POD",container!=""}) by (pod, namespace, node)`,
				Description: "Pod memory usage",
				Unit:        "bytes",
				Type:        MetricTypeGauge,
				Labels:      []string{"pod", "namespace", "node"},
				Interval:    30 * time.Second,
			},
			"gpu_utilization": {
				Name:        "gpu_utilization",
				Query:       `DCGM_FI_DEV_GPU_UTIL`,
				Description: "GPU utilization percentage",
				Unit:        "percent",
				Type:        MetricTypeGauge,
				Labels:      []string{"gpu", "instance", "modelName"},
				Interval:    15 * time.Second,
			},
			"gpu_memory_usage": {
				Name:        "gpu_memory_usage",
				Query:       `DCGM_FI_DEV_FB_USED`,
				Description: "GPU memory usage",
				Unit:        "bytes",
				Type:        MetricTypeGauge,
				Labels:      []string{"gpu", "instance", "modelName"},
				Interval:    15 * time.Second,
			},
			"gpu_memory_total": {
				Name:        "gpu_memory_total",
				Query:       `DCGM_FI_DEV_FB_TOTAL`,
				Description: "GPU memory total",
				Unit:        "bytes",
				Type:        MetricTypeGauge,
				Labels:      []string{"gpu", "instance", "modelName"},
				Interval:    15 * time.Second,
			},
		},
		Collectors: []CollectorConfig{
			{
				Name:        "resource_collector",
				Enabled:     true,
				Interval:    30 * time.Second,
				MetricNames: []string{"node_cpu_usage", "node_memory_usage", "pod_cpu_usage", "pod_memory_usage"},
			},
			{
				Name:        "gpu_collector",
				Enabled:     true,
				Interval:    15 * time.Second,
				MetricNames: []string{"gpu_utilization", "gpu_memory_usage", "gpu_memory_total"},
			},
		},
	}
}
