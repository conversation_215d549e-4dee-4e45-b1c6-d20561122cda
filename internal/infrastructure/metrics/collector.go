package metrics

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

type MetricsCollector struct {
	queryEngine   *QueryEngine
	configManager *ConfigManager
	cacheManager  *CacheManager

	collectors map[string]*ResourceCollector
	mu         sync.RWMutex

	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
}

type ResourceCollector struct {
	name        string
	config      CollectorConfig
	metricNames []string
	interval    time.Duration
	enabled     bool

	lastCollection time.Time
	mu             sync.RWMutex
}

func NewMetricsCollector(queryEngine *QueryEngine, configManager *ConfigManager, cacheManager *CacheManager) *MetricsCollector {
	return &MetricsCollector{
		queryEngine:   queryEngine,
		configManager: configManager,
		cacheManager:  cacheManager,
		collectors:    make(map[string]*ResourceCollector),
	}
}

func (mc *MetricsCollector) Start(ctx context.Context) error {
	mc.ctx, mc.cancel = context.WithCancel(ctx)

	config := mc.configManager.GetConfig()
	for _, collectorConfig := range config.Collectors {
		if collectorConfig.Enabled {
			collector := &ResourceCollector{
				name:        collectorConfig.Name,
				config:      collectorConfig,
				metricNames: collectorConfig.MetricNames,
				interval:    collectorConfig.Interval,
				enabled:     collectorConfig.Enabled,
			}

			mc.mu.Lock()
			mc.collectors[collectorConfig.Name] = collector
			mc.mu.Unlock()

			mc.wg.Add(1)
			go mc.runCollector(collector)
		}
	}

	zlog.SugarInfof("Started %d metric collectors", len(mc.collectors))
	return nil
}

func (mc *MetricsCollector) Stop() error {
	if mc.cancel != nil {
		mc.cancel()
	}

	mc.wg.Wait()
	zlog.SugarInfof("Stopped all metric collectors")
	return nil
}

func (mc *MetricsCollector) runCollector(collector *ResourceCollector) {
	defer mc.wg.Done()

	ticker := time.NewTicker(collector.interval)
	defer ticker.Stop()

	mc.collectMetrics(collector)

	for {
		select {
		case <-mc.ctx.Done():
			return
		case <-ticker.C:
			mc.collectMetrics(collector)
		}
	}
}

func (mc *MetricsCollector) collectMetrics(collector *ResourceCollector) {
	collector.mu.Lock()
	defer collector.mu.Unlock()

	start := time.Now()
	defer func() {
		collector.lastCollection = time.Now()
		zlog.SugarDebugf("Collector %s completed in %v", collector.name, time.Since(start))
	}()

	for _, metricName := range collector.metricNames {
		if err := mc.collectSingleMetric(metricName); err != nil {
			zlog.SugarErrorf("Failed to collect metric %s: %v", metricName, err)
		}
	}
}

func (mc *MetricsCollector) collectSingleMetric(metricName string) error {
	metricConfig, exists := mc.configManager.GetMetricConfig(metricName)
	if !exists {
		return fmt.Errorf("metric config not found: %s", metricName)
	}

	if mc.cacheManager != nil {
		if cached := mc.cacheManager.Get(metricName); cached != nil {
			return nil
		}
	}

	request := &QueryRequest{
		MetricName: metricName,
		Query:      metricConfig.Query,
		EndTime:    time.Now(),
		Timeout:    metricConfig.Timeout,
	}

	result, err := mc.queryEngine.Query(mc.ctx, request)
	if err != nil {
		return fmt.Errorf("query failed: %w", err)
	}

	if mc.cacheManager != nil {
		mc.cacheManager.Set(metricName, result, metricConfig.Interval)
	}

	return nil
}

func (mc *MetricsCollector) GetResourceUsage(ctx context.Context, filters map[string]string) ([]*ResourceUsage, error) {
	var usages []*ResourceUsage

	cpuUsages, err := mc.getCPUUsage(ctx, filters)
	if err != nil {
		zlog.SugarErrorf("Failed to get CPU usage: %v", err)
	} else {
		usages = append(usages, cpuUsages...)
	}

	memUsages, err := mc.getMemoryUsage(ctx, filters)
	if err != nil {
		zlog.SugarErrorf("Failed to get memory usage: %v", err)
	} else {
		usages = append(usages, memUsages...)
	}

	gpuUsages, err := mc.getGPUUsage(ctx, filters)
	if err != nil {
		zlog.SugarErrorf("Failed to get GPU usage: %v", err)
	} else {
		usages = append(usages, gpuUsages...)
	}

	return usages, nil
}

func (mc *MetricsCollector) getCPUUsage(ctx context.Context, filters map[string]string) ([]*ResourceUsage, error) {
	var usages []*ResourceUsage

	podCPUQuery := `sum(rate(container_cpu_usage_seconds_total{container!="POD",container!=""}[5m])) by (pod, namespace, node)`
	if namespace, ok := filters["namespace"]; ok {
		podCPUQuery = fmt.Sprintf(`sum(rate(container_cpu_usage_seconds_total{container!="POD",container!="",namespace="%s"}[5m])) by (pod, namespace, node)`, namespace)
	}

	request := &QueryRequest{
		MetricName: "pod_cpu_usage",
		Query:      podCPUQuery,
		EndTime:    time.Now(),
		Timeout:    10 * time.Second,
	}

	result, err := mc.queryEngine.Query(ctx, request)
	if err != nil {
		return nil, err
	}

	for _, point := range result.Data {
		usage := &ResourceUsage{
			ResourceType: ResourceTypeCPU,
			PodName:      point.Labels["pod"],
			Namespace:    point.Labels["namespace"],
			NodeName:     point.Labels["node"],
			CPUUsage:     point.Value,
			Timestamp:    point.Timestamp,
		}
		usages = append(usages, usage)
	}

	return usages, nil
}

func (mc *MetricsCollector) getMemoryUsage(ctx context.Context, filters map[string]string) ([]*ResourceUsage, error) {
	var usages []*ResourceUsage

	podMemQuery := `sum(container_memory_working_set_bytes{container!="POD",container!=""}) by (pod, namespace, node)`
	if namespace, ok := filters["namespace"]; ok {
		podMemQuery = fmt.Sprintf(`sum(container_memory_working_set_bytes{container!="POD",container!="",namespace="%s"}) by (pod, namespace, node)`, namespace)
	}

	request := &QueryRequest{
		MetricName: "pod_memory_usage",
		Query:      podMemQuery,
		EndTime:    time.Now(),
		Timeout:    10 * time.Second,
	}

	result, err := mc.queryEngine.Query(ctx, request)
	if err != nil {
		return nil, err
	}

	for _, point := range result.Data {
		usage := &ResourceUsage{
			ResourceType: ResourceTypeMemory,
			PodName:      point.Labels["pod"],
			Namespace:    point.Labels["namespace"],
			NodeName:     point.Labels["node"],
			MemoryUsage:  point.Value,
			Timestamp:    point.Timestamp,
		}
		usages = append(usages, usage)
	}

	return usages, nil
}

func (mc *MetricsCollector) getGPUUsage(ctx context.Context, filters map[string]string) ([]*ResourceUsage, error) {
	var usages []*ResourceUsage

	gpuUtilQuery := `DCGM_FI_DEV_GPU_UTIL`
	if node, ok := filters["node"]; ok {
		gpuUtilQuery = fmt.Sprintf(`DCGM_FI_DEV_GPU_UTIL{instance=~"%s.*"}`, node)
	}

	utilRequest := &QueryRequest{
		MetricName: "gpu_utilization",
		Query:      gpuUtilQuery,
		EndTime:    time.Now(),
		Timeout:    10 * time.Second,
	}

	utilResult, err := mc.queryEngine.Query(ctx, utilRequest)
	if err != nil {
		return nil, err
	}

	gpuMemQuery := `DCGM_FI_DEV_FB_USED`
	if node, ok := filters["node"]; ok {
		gpuMemQuery = fmt.Sprintf(`DCGM_FI_DEV_FB_USED{instance=~"%s.*"}`, node)
	}

	memRequest := &QueryRequest{
		MetricName: "gpu_memory_usage",
		Query:      gpuMemQuery,
		EndTime:    time.Now(),
		Timeout:    10 * time.Second,
	}

	memResult, err := mc.queryEngine.Query(ctx, memRequest)
	if err != nil {
		return nil, err
	}

	gpuMap := make(map[string]*ResourceUsage)

	for _, point := range utilResult.Data {
		key := fmt.Sprintf("%s-%s", point.Labels["instance"], point.Labels["gpu"])

		gpuIndex, _ := strconv.Atoi(point.Labels["gpu"])

		usage := &ResourceUsage{
			ResourceType: ResourceTypeGPU,
			NodeName:     extractNodeName(point.Labels["instance"]),
			GPUUsage:     point.Value,
			GPUIndex:     gpuIndex,
			GPUModel:     point.Labels["modelName"],
			Timestamp:    point.Timestamp,
		}

		gpuMap[key] = usage
	}

	for _, point := range memResult.Data {
		key := fmt.Sprintf("%s-%s", point.Labels["instance"], point.Labels["gpu"])

		if usage, exists := gpuMap[key]; exists {
			usage.GPUMemoryUsage = point.Value
		} else {
			gpuIndex, _ := strconv.Atoi(point.Labels["gpu"])

			usage := &ResourceUsage{
				ResourceType:   ResourceTypeGPUMemory,
				NodeName:       extractNodeName(point.Labels["instance"]),
				GPUMemoryUsage: point.Value,
				GPUIndex:       gpuIndex,
				GPUModel:       point.Labels["modelName"],
				Timestamp:      point.Timestamp,
			}

			gpuMap[key] = usage
		}
	}

	for _, usage := range gpuMap {
		usages = append(usages, usage)
	}

	return usages, nil
}

func extractNodeName(instance string) string {
	parts := strings.Split(instance, ":")
	if len(parts) > 0 {
		return parts[0]
	}
	return instance
}

func (mc *MetricsCollector) GetCollectorStatus() map[string]interface{} {
	mc.mu.RLock()
	defer mc.mu.RUnlock()

	status := make(map[string]interface{})

	for name, collector := range mc.collectors {
		collector.mu.RLock()
		collectorStatus := map[string]interface{}{
			"name":            collector.name,
			"enabled":         collector.enabled,
			"interval":        collector.interval.String(),
			"metric_names":    collector.metricNames,
			"last_collection": collector.lastCollection,
		}
		collector.mu.RUnlock()

		status[name] = collectorStatus
	}

	return status
}
