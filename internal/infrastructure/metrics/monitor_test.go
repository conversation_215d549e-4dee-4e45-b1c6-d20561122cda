package metrics

import (
	"context"
	"testing"
)

func TestMonitor(t *testing.T) {
	if testing.Short() {
		t.<PERSON>("Skipping long-running integration test in short mode")
	}

	monitor, err := NewMetricsMonitor("../../../conf/metrics.yaml")
	if err != nil {
		t.<PERSON><PERSON><PERSON>("failed to create monitor: %v", err)
		return
	}

	ctx := context.Background()
	// monitor.Start(ctx)
	usage, err := monitor.GetResourceUsage(ctx, nil)
	if err != nil {
		t.Errorf("failed to get resource usage: %v", err)
		return
	}

	t.Logf("resource usage: %+v", usage)
}
