package metrics

import (
	"time"
)

type MetricConfig struct {
	Name        string            `yaml:"name" json:"name"`
	Query       string            `yaml:"query" json:"query"`
	Description string            `yaml:"description" json:"description"`
	Unit        string            `yaml:"unit" json:"unit"`
	Type        MetricType        `yaml:"type" json:"type"`
	Labels      []string          `yaml:"labels" json:"labels"`
	Interval    time.Duration     `yaml:"interval" json:"interval"`
	Timeout     time.Duration     `yaml:"timeout" json:"timeout"`
	Tags        map[string]string `yaml:"tags" json:"tags"`
}

type MetricType string

const (
	MetricTypeGauge     MetricType = "gauge"
	MetricTypeCounter   MetricType = "counter"
	MetricTypeHistogram MetricType = "histogram"
	MetricTypeSummary   MetricType = "summary"
)

type ResourceType string

const (
	ResourceTypeCPU       ResourceType = "cpu"
	ResourceTypeMemory    ResourceType = "memory"
	ResourceTypeGPU       ResourceType = "gpu"
	ResourceTypeGPUMemory ResourceType = "gpu_memory"
	ResourceTypeNetwork   ResourceType = "network"
	ResourceTypeDisk      ResourceType = "disk"
)

type QueryRequest struct {
	MetricName string            `json:"metric_name"`
	Query      string            `json:"query"`
	StartTime  time.Time         `json:"start_time"`
	EndTime    time.Time         `json:"end_time"`
	Step       time.Duration     `json:"step"`
	Labels     map[string]string `json:"labels"`
	Timeout    time.Duration     `json:"timeout"`
}

type QueryResult struct {
	MetricName string        `json:"metric_name"`
	Data       []MetricPoint `json:"data"`
	Labels     Labels        `json:"labels"`
	Timestamp  time.Time     `json:"timestamp"`
	Status     string        `json:"status"`
	Error      string        `json:"error,omitempty"`
}

type MetricPoint struct {
	Timestamp time.Time `json:"timestamp"`
	Value     float64   `json:"value"`
	Labels    Labels    `json:"labels"`
}

type Labels map[string]string

type ResourceUsage struct {
	ResourceType  ResourceType `json:"resource_type"`
	NodeName      string       `json:"node_name"`
	PodName       string       `json:"pod_name,omitempty"`
	Namespace     string       `json:"namespace,omitempty"`
	ContainerName string       `json:"container_name,omitempty"`

	CPUUsage    float64 `json:"cpu_usage,omitempty"`
	CPUCores    float64 `json:"cpu_cores,omitempty"`
	CPURequests float64 `json:"cpu_requests,omitempty"`
	CPULimits   float64 `json:"cpu_limits,omitempty"`

	MemoryUsage    float64 `json:"memory_usage,omitempty"`
	MemoryUsagePct float64 `json:"memory_usage_pct,omitempty"`
	MemoryRequests float64 `json:"memory_requests,omitempty"`
	MemoryLimits   float64 `json:"memory_limits,omitempty"`

	GPUUsage       float64 `json:"gpu_usage,omitempty"`
	GPUMemoryUsage float64 `json:"gpu_memory_usage,omitempty"`
	GPUMemoryTotal float64 `json:"gpu_memory_total,omitempty"`
	GPUMemoryPct   float64 `json:"gpu_memory_pct,omitempty"`
	GPUIndex       int     `json:"gpu_index,omitempty"`
	GPUModel       string  `json:"gpu_model,omitempty"`

	Timestamp time.Time `json:"timestamp"`
}

type MetricsConfig struct {
	Prometheus PrometheusConfig        `yaml:"prometheus" json:"prometheus"`
	Metrics    map[string]MetricConfig `yaml:"metrics" json:"metrics"`
	Cache      CacheConfig             `yaml:"cache" json:"cache"`
	Collectors []CollectorConfig       `yaml:"collectors" json:"collectors"`
}

type PrometheusConfig struct {
	Endpoint   string        `yaml:"endpoint" json:"endpoint"`
	Username   string        `yaml:"username" json:"username"`
	Password   string        `yaml:"password" json:"password"`
	Timeout    time.Duration `yaml:"timeout" json:"timeout"`
	MaxRetries int           `yaml:"max_retries" json:"max_retries"`
	RetryDelay time.Duration `yaml:"retry_delay" json:"retry_delay"`
	EnableTLS  bool          `yaml:"enable_tls" json:"enable_tls"`
	TLSConfig  TLSConfig     `yaml:"tls_config" json:"tls_config"`
}

type TLSConfig struct {
	CertFile           string `yaml:"cert_file" json:"cert_file"`
	KeyFile            string `yaml:"key_file" json:"key_file"`
	CAFile             string `yaml:"ca_file" json:"ca_file"`
	InsecureSkipVerify bool   `yaml:"insecure_skip_verify" json:"insecure_skip_verify"`
}

type CacheConfig struct {
	Enabled         bool          `yaml:"enabled" json:"enabled"`
	TTL             time.Duration `yaml:"ttl" json:"ttl"`
	MaxSize         int           `yaml:"max_size" json:"max_size"`
	CleanupInterval time.Duration `yaml:"cleanup_interval" json:"cleanup_interval"`
}

type CollectorConfig struct {
	Name        string        `yaml:"name" json:"name"`
	Enabled     bool          `yaml:"enabled" json:"enabled"`
	Interval    time.Duration `yaml:"interval" json:"interval"`
	MetricNames []string      `yaml:"metric_names" json:"metric_names"`
	Labels      Labels        `yaml:"labels" json:"labels"`
}
