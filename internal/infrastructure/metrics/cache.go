package metrics

import (
	"sync"
	"time"
)

type CacheManager struct {
	cache  map[string]*CacheItem
	mu     sync.RWMutex
	config CacheConfig
	stopCh chan struct{}
}

type CacheItem struct {
	Data      interface{}
	ExpiresAt time.Time
}

func NewCacheManager(config CacheConfig) *CacheManager {
	cm := &CacheManager{
		cache:  make(map[string]*CacheItem),
		config: config,
		stopCh: make(chan struct{}),
	}

	if config.Enabled {
		go cm.cleanupLoop()
	}

	return cm
}

func (cm *CacheManager) Get(key string) interface{} {
	if !cm.config.Enabled {
		return nil
	}

	cm.mu.RLock()
	defer cm.mu.RUnlock()

	item, exists := cm.cache[key]
	if !exists {
		return nil
	}

	if time.Now().After(item.ExpiresAt) {
		delete(cm.cache, key)
		return nil
	}

	return item.Data
}

func (cm *CacheManager) Set(key string, data interface{}, ttl time.Duration) {
	if !cm.config.Enabled {
		return
	}

	cm.mu.Lock()
	defer cm.mu.Unlock()

	if len(cm.cache) >= cm.config.MaxSize {
		cm.evictOldest()
	}

	if ttl == 0 {
		ttl = cm.config.TTL
	}

	cm.cache[key] = &CacheItem{
		Data:      data,
		ExpiresAt: time.Now().Add(ttl),
	}
}

func (cm *CacheManager) Delete(key string) {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	delete(cm.cache, key)
}

func (cm *CacheManager) Clear() {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	cm.cache = make(map[string]*CacheItem)
}

func (cm *CacheManager) evictOldest() {
	var oldestKey string
	var oldestTime time.Time

	for key, item := range cm.cache {
		if oldestKey == "" || item.ExpiresAt.Before(oldestTime) {
			oldestKey = key
			oldestTime = item.ExpiresAt
		}
	}

	if oldestKey != "" {
		delete(cm.cache, oldestKey)
	}
}

func (cm *CacheManager) cleanupLoop() {
	ticker := time.NewTicker(cm.config.CleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-cm.stopCh:
			return
		case <-ticker.C:
			cm.cleanup()
		}
	}
}

func (cm *CacheManager) cleanup() {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	now := time.Now()
	for key, item := range cm.cache {
		if now.After(item.ExpiresAt) {
			delete(cm.cache, key)
		}
	}
}

func (cm *CacheManager) Stop() {
	close(cm.stopCh)
}

func (cm *CacheManager) GetStats() map[string]interface{} {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	return map[string]interface{}{
		"enabled":          cm.config.Enabled,
		"size":             len(cm.cache),
		"max_size":         cm.config.MaxSize,
		"ttl":              cm.config.TTL.String(),
		"cleanup_interval": cm.config.CleanupInterval.String(),
	}
}
