package metrics

import (
	"context"
	"crypto/tls"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/prometheus/client_golang/api"
	v1 "github.com/prometheus/client_golang/api/prometheus/v1"
	"github.com/prometheus/common/model"

	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

type QueryEngine struct {
	client      api.Client
	v1api       v1.API
	config      *PrometheusConfig
	retryConfig RetryConfig
}

type RetryConfig struct {
	MaxRetries int
	RetryDelay time.Duration
}

func NewQueryEngine(config *PrometheusConfig) (*QueryEngine, error) {
	httpClient := &http.Client{
		Timeout: config.Timeout,
	}

	if config.EnableTLS {
		tlsConfig := &tls.Config{
			InsecureSkipVerify: config.TLSConfig.InsecureSkipVerify,
		}

		if config.TLSConfig.CertFile != "" && config.TLSConfig.KeyFile != "" {
			cert, err := tls.LoadX509KeyPair(config.TLSConfig.CertFile, config.TLSConfig.KeyFile)
			if err != nil {
				return nil, fmt.Errorf("failed to load TLS cert: %w", err)
			}
			tlsConfig.Certificates = []tls.Certificate{cert}
		}

		httpClient.Transport = &http.Transport{
			TLSClientConfig: tlsConfig,
		}
	}

	if config.Username != "" && config.Password != "" {
		httpClient.Transport = &BasicAuthTransport{
			Username:  config.Username,
			Password:  config.Password,
			Transport: httpClient.Transport,
		}
	}

	client, err := api.NewClient(api.Config{
		Address: config.Endpoint,
		Client:  httpClient,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create prometheus client: %w", err)
	}

	return &QueryEngine{
		client: client,
		v1api:  v1.NewAPI(client),
		config: config,
		retryConfig: RetryConfig{
			MaxRetries: config.MaxRetries,
			RetryDelay: config.RetryDelay,
		},
	}, nil
}

type BasicAuthTransport struct {
	Username  string
	Password  string
	Transport http.RoundTripper
}

func (bat *BasicAuthTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	req.SetBasicAuth(bat.Username, bat.Password)
	if bat.Transport != nil {
		return bat.Transport.RoundTrip(req)
	}
	return http.DefaultTransport.RoundTrip(req)
}

func (qe *QueryEngine) Query(ctx context.Context, request *QueryRequest) (*QueryResult, error) {
	var result *QueryResult
	var err error

	for attempt := 0; attempt <= qe.retryConfig.MaxRetries; attempt++ {
		if attempt > 0 {
			zlog.SugarWarnf("Retrying query attempt %d for metric %s", attempt, request.MetricName)
			time.Sleep(qe.retryConfig.RetryDelay)
		}

		result, err = qe.executeQuery(ctx, request)
		if err == nil {
			return result, nil
		}

		if !qe.shouldRetry(err) {
			break
		}
	}

	return nil, fmt.Errorf("query failed after %d attempts: %w", qe.retryConfig.MaxRetries+1, err)
}

func (qe *QueryEngine) executeQuery(ctx context.Context, request *QueryRequest) (*QueryResult, error) {
	if request.Timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, request.Timeout)
		defer cancel()
	}

	value, warnings, err := qe.v1api.Query(ctx, request.Query, request.EndTime)
	if err != nil {
		return &QueryResult{
			MetricName: request.MetricName,
			Status:     "error",
			Error:      err.Error(),
			Timestamp:  time.Now(),
		}, err
	}

	if len(warnings) > 0 {
		zlog.SugarWarnf("Query warnings for %s: %v", request.MetricName, warnings)
	}

	points, labels := qe.parseValue(value)

	return &QueryResult{
		MetricName: request.MetricName,
		Data:       points,
		Labels:     labels,
		Status:     "success",
		Timestamp:  time.Now(),
	}, nil
}

func (qe *QueryEngine) QueryRange(ctx context.Context, request *QueryRequest) (*QueryResult, error) {
	if request.Timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, request.Timeout)
		defer cancel()
	}

	r := v1.Range{
		Start: request.StartTime,
		End:   request.EndTime,
		Step:  request.Step,
	}

	value, warnings, err := qe.v1api.QueryRange(ctx, request.Query, r)
	if err != nil {
		return &QueryResult{
			MetricName: request.MetricName,
			Status:     "error",
			Error:      err.Error(),
			Timestamp:  time.Now(),
		}, err
	}

	if len(warnings) > 0 {
		zlog.SugarWarnf("Query range warnings for %s: %v", request.MetricName, warnings)
	}

	points, labels := qe.parseValue(value)

	return &QueryResult{
		MetricName: request.MetricName,
		Data:       points,
		Labels:     labels,
		Status:     "success",
		Timestamp:  time.Now(),
	}, nil
}

func (qe *QueryEngine) parseValue(value model.Value) ([]MetricPoint, Labels) {
	var points []MetricPoint
	var commonLabels Labels

	switch v := value.(type) {
	case model.Vector:
		for _, sample := range v {
			labels := make(Labels)
			for k, v := range sample.Metric {
				labels[string(k)] = string(v)
			}

			points = append(points, MetricPoint{
				Timestamp: sample.Timestamp.Time(),
				Value:     float64(sample.Value),
				Labels:    labels,
			})
		}

	case model.Matrix:
		for _, sampleStream := range v {
			labels := make(Labels)
			for k, v := range sampleStream.Metric {
				labels[string(k)] = string(v)
			}

			for _, pair := range sampleStream.Values {
				points = append(points, MetricPoint{
					Timestamp: pair.Timestamp.Time(),
					Value:     float64(pair.Value),
					Labels:    labels,
				})
			}
		}

	case *model.Scalar:
		points = append(points, MetricPoint{
			Timestamp: v.Timestamp.Time(),
			Value:     float64(v.Value),
			Labels:    make(Labels),
		})

	case *model.String:
		zlog.SugarWarnf("String value not supported: %s", v.Value)
	}

	return points, commonLabels
}

func (qe *QueryEngine) shouldRetry(err error) bool {
	errStr := err.Error()

	retryableErrors := []string{
		"connection refused",
		"timeout",
		"temporary failure",
		"server error",
		"503",
		"502",
		"504",
	}

	for _, retryableErr := range retryableErrors {
		if strings.Contains(strings.ToLower(errStr), retryableErr) {
			return true
		}
	}

	return false
}

func (qe *QueryEngine) Health(ctx context.Context) error {
	_, warnings, err := qe.v1api.Query(ctx, "up", time.Now())
	if len(warnings) > 0 {
		zlog.SugarWarnf("Query health warnings: %v", warnings)
	}
	return err
}
