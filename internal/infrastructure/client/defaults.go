package client

import (
	"context"
	"sync"

	"google.golang.org/grpc/metadata"
	token "transwarp.io/mlops/mlops-std/token"
)

var defaultContext = sync.OnceValues(func() (context.Context, error) {
	ctx := context.Background()
	info := &token.TokenInfo{}
	info.UserName = "thinger"
	tokenStr := token.Encode(info)
	md := metadata.New(map[string]string{token.AUTH_HEADER: tokenStr})
	return metadata.NewIncomingContext(ctx, md), nil
})
