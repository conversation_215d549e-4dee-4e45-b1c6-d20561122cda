package client

import (
	"context"
	"fmt"
)

const (
	MinioClientName = "minio"
)

type MinioClientWrapper struct {
	client *MinioClient

	options *MinioClientOptions
}

type MinioClientFactory struct{}

func NewMinioClientFactory() ClientFactory {
	return &MinioClientFactory{}
}

func (f *MinioClientFactory) Name() string {
	return MinioClientName
}

func (f *MinioClientFactory) Create(options interface{}) (Client, error) {
	ops, ok := options.(*MinioClientOptions)
	if !ok {
		return nil, fmt.Errorf("invalid config type for minio client")
	}

	return &MinioClientWrapper{
		options: ops,
	}, nil
}

func (w *MinioClientWrapper) Name() string {
	return MinioClientName
}

func (w *MinioClientWrapper) Dependencies() []string {
	return []string{}
}

func (w *MinioClientWrapper) Initialize(ctx context.Context) error {
	client, err := NewMinioClient(w.options.MinioEndpoint,
		w.options.<PERSON><PERSON>ey,
		w.options.SecretKey,
		w.options.Secure,
		w.options.Gegion,
		w.options.InitConnectionTimeout)
	if err != nil {
		return fmt.Errorf("failed to create minio client: %w", err)
	}

	w.client = client
	return nil
}

func (w *MinioClientWrapper) Start(ctx context.Context) error {
	return nil
}

func (w *MinioClientWrapper) Stop(ctx context.Context) error {
	return nil
}

func (w *MinioClientWrapper) HealthCheck(ctx context.Context) error {
	if w.client == nil {
		return fmt.Errorf("minio client not initialized")
	}

	return nil
}

func (w *MinioClientWrapper) GetClient() *MinioClient {
	return w.client
}
