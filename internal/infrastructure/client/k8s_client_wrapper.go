package client

import (
	"context"
	"fmt"
)

const (
	K8sClientName = "k8s"
)

type K8sClientWrapper struct {
	client *K8sClientset

	options *K8sClientOptions
}

type K8sClientFactory struct{}

func NewK8sClientFactory() ClientFactory {
	return &K8sClientFactory{}
}

func (f *K8sClientFactory) Name() string {
	return K8sClientName
}

func (f *K8sClientFactory) Create(options interface{}) (Client, error) {
	ops, ok := options.(*K8sClientOptions)
	if !ok {
		return nil, fmt.Errorf("invalid config type for k8s client")
	}

	return &K8sClientWrapper{
		options: ops,
	}, nil
}

func (w *K8sClientWrapper) Name() string {
	return K8sClientName
}

func (w *K8sClientWrapper) Dependencies() []string {
	return []string{}
}

func (w *K8sClientWrapper) Initialize(ctx context.Context) error {
	client, err := NewK8sClient()
	if err != nil {
		return fmt.Erro<PERSON>("failed to create k8s client: %w", err)
	}

	w.client = client
	return nil
}

func (w *K8sClientWrapper) Start(ctx context.Context) error {
	return w.client.Start(ctx)
}

func (w *K8sClientWrapper) Stop(ctx context.Context) error {
	return nil
}

func (w *K8sClientWrapper) HealthCheck(ctx context.Context) error {
	if w.client == nil {
		return fmt.Errorf("k8s client not initialized")
	}

	_, err := w.client.Clientset.Discovery().ServerVersion()
	return err
}

func (w *K8sClientWrapper) GetClient() *K8sClientset {
	return w.client
}
