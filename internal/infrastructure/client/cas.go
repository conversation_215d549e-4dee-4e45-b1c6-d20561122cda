package client

import (
	"context"
	"fmt"
	"time"

	"transwarp.io/mlops/mlops-std/stderr"
	"transwarp.io/mlops/pipeline/pkg/util/httpclient"
)

const (
	CASHttpClientName = "cas"
)

type CASHttpClient struct {
	*httpclient.HttpClient
}

func NewCASHttpClient() (*CASHttpClient, error) {
	url := "http://autocv-cas-service:80"
	return &CASHttpClient{
		HttpClient: httpclient.NewHttpClient(url,
			httpclient.WithInsecureSkipVerify()),
	}, nil
}

type ProjectDetail struct {
	CreateTime    string              `json:"create_time"`
	CreateUser    string              `json:"create_user"`
	Description   string              `json:"description"`
	Disabled      bool                `json:"disabled"`
	Industry      string              `json:"industry"`
	Labels        map[string][]string `json:"labels"`
	Logo          string              `json:"logo"`
	MemberCount   int                 `json:"member_count"`
	Name          string              `json:"name"`
	ProjectID     string              `json:"project_id"`
	ResourceQuota ResourceQuota       `json:"resource_quota"`
	TenantUID     string              `json:"tenant_uid"`
}

type ResourceQuota struct {
	Bandwidth         string `json:"bandwidth"`
	EgressBandwidth   string `json:"egress_bandwidth"`
	GPU               string `json:"gpu"`
	GPUMemory         string `json:"gpu_memory"`
	IngressBandwidth  string `json:"ingress_bandwidth"`
	Knowl             string `json:"knowl"`
	LimitsCPU         string `json:"limits_cpu"`
	LimitsMemory      string `json:"limits_memory"`
	Pods              string `json:"pods"`
	RequestsCPU       string `json:"requests_cpu"`
	RequestsGPU       string `json:"requests_gpu"`
	RequestsGPUMemory string `json:"requests_gpu_memory"`
	RequestsMemory    string `json:"requests_memory"`
	RequestsStorage   string `json:"requests_storage"`
}

func (c *CASHttpClient) GetProjectDetailById(ctx context.Context, projectId string) (*ProjectDetail, error) {
	path := fmt.Sprintf("/api/v1/projmgr/projects/%s", projectId)

	headers, _ := getTokenHeader(ctx)
	result, err := httpclient.GetT[ProjectDetail](ctx, c.HttpClient, path, nil, headers)

	if err != nil {
		return nil, stderr.Wrap(err, "query deploy template err")
	}

	return result, nil
}

type Permission struct {
	ID     int      `json:"id"`
	Code   string   `json:"code"`
	Name   string   `json:"name"`
	Action []string `json:"action"`
}

type UserProfile struct {
	UID              int          `json:"uid"`
	UserName         string       `json:"user_name"`
	FullName         string       `json:"full_name"`
	Email            string       `json:"email"`
	Password         string       `json:"password"`
	UserGroupNames   []string     `json:"user_group_names"`
	CreateUser       string       `json:"create_user"`
	CreateTime       time.Time    `json:"create_time"`
	PlatformRoleName string       `json:"platform_role_name"`
	Permissions      []Permission `json:"permissions"`
	DefaultProject   string       `json:"default_project"`
}

func (c *CASHttpClient) GetUserProfile(ctx context.Context) (*UserProfile, error) {
	path := fmt.Sprintf("/api/v1/usermgr/users/profile")

	headers, _ := getTokenHeader(ctx)
	result, err := httpclient.GetT[UserProfile](ctx, c.HttpClient, path, nil, headers)
	if err != nil {
		return nil, stderr.Wrap(err, "query deploy template err")
	}

	return result, nil
}

type Project struct {
	ProjectID   string    `json:"project_id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Labels      []string  `json:"labels,omitempty"`
	CreateUser  string    `json:"createUser"`
	TenantUID   string    `json:"tenant_uid"`
	MemberCount int       `json:"member_count"`
	Disabled    bool      `json:"disabled"`
	Create_user string    `json:"create_user"`
	CreateTime  time.Time `json:"create_time"`
	Logo        string    `json:"logo"`
	Examine     int       `json:"examine"`
}

func (c *CASHttpClient) Listprojects(ctx context.Context) ([]*Project, error) {
	path := fmt.Sprintf("/api/v1/projmgr/projects")
	headers, _ := getTokenHeader(ctx)
	result, err := httpclient.GetT[[]*Project](ctx, c.HttpClient, path, nil, headers)
	if err != nil {
		return nil, stderr.Wrap(err, "query deploy template err")
	}

	return *result, nil
}
