package client

import (
	"context"
	"fmt"
)

const (
	PipelineClientName = "pipeline"
)

type PipelineClientWrapper struct {
	client *PipelineClient

	options *PipelineClientOptions
}

type PipelineClientFactory struct{}

func NewPipelineClientFactory() ClientFactory {
	return &PipelineClientFactory{}
}

func (f *PipelineClientFactory) Name() string {
	return PipelineClientName
}

func (f *PipelineClientFactory) Create(options interface{}) (Client, error) {
	ops, ok := options.(*PipelineClientOptions)
	if !ok {
		return nil, fmt.Errorf("invalid config type for pipeline client")
	}

	return &PipelineClientWrapper{
		options: ops,
	}, nil
}

func (w *PipelineClientWrapper) Name() string {
	return PipelineClientName
}

func (w *PipelineClientWrapper) Dependencies() []string {
	return []string{}
}

func (w *PipelineClientWrapper) Initialize(ctx context.Context) error {
	client, err := NewPipelineClient()
	if err != nil {
		return fmt.Errorf("failed to create pipeline client: %w", err)
	}

	w.client = client
	return nil
}

func (w *PipelineClientWrapper) Start(ctx context.Context) error {
	return nil
}

func (w *PipelineClientWrapper) Stop(ctx context.Context) error {
	return nil
}

func (w *PipelineClientWrapper) HealthCheck(ctx context.Context) error {
	if w.client == nil {
		return fmt.Errorf("pipeline client not initialized")
	}

	return nil
}

func (w *PipelineClientWrapper) GetClient() *PipelineClient {
	return w.client
}
