package client

import (
	"context"
	"fmt"
	"time"

	"k8s.io/client-go/tools/cache"
	lwsclientset "sigs.k8s.io/lws/client-go/clientset/versioned"
	lwsinformers "sigs.k8s.io/lws/client-go/informers/externalversions"

	util "transwarp.io/mlops/pipeline/internal/util"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

const (
	LeaderworkerSetClientName = "kueue"
)

type LeaderWorkerSetClient struct {
	clientset       lwsclientset.Interface
	informerFactory lwsinformers.SharedInformerFactory

	// Informers
	LeaderWorkerSetInformer cache.SharedIndexInformer

	eventHandlers map[string][]cache.ResourceEventHandler

	stopCh chan struct{}
}

func NewLeaderWorkerSetClient() (*LeaderWorkerSetClient, error) {
	config, err := util.GetKubeConfig()
	if err != nil {
		zlog.SugarErrorf("Error building kueue client config: %v", err)
		return nil, err
	}

	clientset := lwsclientset.NewForConfigOrDie(config)
	client := &LeaderWorkerSetClient{
		clientset:       clientset,
		informerFactory: lwsinformers.NewSharedInformerFactory(clientset, 10*time.Minute),
		eventHandlers:   make(map[string][]cache.ResourceEventHandler),
		stopCh:          make(chan struct{}),
	}

	if err := client.setupAndSyncInformers(context.Background()); err != nil {
		zlog.SugarWarnf("setup lws informer failed: %v", err)
	}
	return client, nil
}

func (c *LeaderWorkerSetClient) WaitForCacheSync(ctx context.Context) bool {
	return cache.WaitForCacheSync(ctx.Done(),
		c.LeaderWorkerSetInformer.HasSynced,
	)
}

func (c *LeaderWorkerSetClient) setupAndSyncInformers(ctx context.Context) error {
	if c.informerFactory == nil {
		zlog.SugarErrorf("Informer factory not initialized, skipping informer setup")
		return fmt.Errorf("informer factory not initialized")
	}

	c.LeaderWorkerSetInformer = c.informerFactory.Leaderworkerset().V1().LeaderWorkerSets().Informer()

	go c.informerFactory.Start(c.stopCh)

	syncCtx, cancel := context.WithTimeout(ctx, 300*time.Second) // Use a context derived from the input context
	defer cancel()

	if !c.WaitForCacheSync(syncCtx) {
		return fmt.Errorf("timed out waiting for lws cache sync. LWS may not be installed or reachable")
	}

	zlog.SugarInfoln("LWS informers cache synced successfully.")
	return nil
}
