package client

import (
	"context"
	"fmt"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/tools/cache"
	kueuev1beta1 "sigs.k8s.io/kueue/apis/kueue/v1beta1"
	kueueclientset "sigs.k8s.io/kueue/client-go/clientset/versioned"
	kueueinformers "sigs.k8s.io/kueue/client-go/informers/externalversions"
	kueuelisters "sigs.k8s.io/kueue/client-go/listers/kueue/v1beta1"

	util "transwarp.io/mlops/pipeline/internal/util"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

type KueueClient struct {
	clientset       kueueclientset.Interface
	informerFactory kueueinformers.SharedInformerFactory

	// Listers
	workloadLister       kueuelisters.WorkloadLister
	localQueueLister     kueuelisters.LocalQueueLister
	clusterQueueLister   kueuelisters.ClusterQueueLister
	resourceFlavorLister kueuelisters.ResourceFlavorLister

	// Informers
	workloadInformer       cache.SharedIndexInformer
	localQueueInformer     cache.SharedIndexInformer
	clusterQueueInformer   cache.SharedIndexInformer
	resourceFlavorInformer cache.SharedIndexInformer

	eventHandlers map[string][]cache.ResourceEventHandler

	stopCh chan struct{}
}

func NewKueueClient() *KueueClient {
	config, err := util.GetKubeConfig()
	if err != nil {
		zlog.SugarErrorf("Error building kueue client config: %v", err)
		return &KueueClient{
			clientset:       nil,
			informerFactory: nil,
			eventHandlers:   make(map[string][]cache.ResourceEventHandler),
		}
	}

	clientset := kueueclientset.NewForConfigOrDie(config)
	client := &KueueClient{
		clientset:       clientset,
		informerFactory: kueueinformers.NewSharedInformerFactory(clientset, 10*time.Minute),
		eventHandlers:   make(map[string][]cache.ResourceEventHandler),
		stopCh:          make(chan struct{}),
	}

	client.setupInformers()
	return client
}

func (kc *KueueClient) StartInformers(ctx context.Context) {
	zlog.SugarInfoln("Starting Kueue informers")
	kc.informerFactory.Start(ctx.Done())

	if !kc.WaitForCacheSync(ctx) {
		zlog.SugarErrorln("Failed to sync Kueue informer caches")
		return
	}

	zlog.SugarInfoln("Kueue informers started and synced successfully")
}

func (kc *KueueClient) WaitForCacheSync(ctx context.Context) bool {
	return cache.WaitForCacheSync(ctx.Done(),
		kc.workloadInformer.HasSynced,
		kc.localQueueInformer.HasSynced,
		kc.clusterQueueInformer.HasSynced,
		kc.resourceFlavorInformer.HasSynced,
	)
}

func (kc *KueueClient) setupInformers() {
	if kc.informerFactory == nil {
		zlog.SugarErrorf("Informer factory not initialized, skipping informer setup")
		return
	}

	// Workload Informer
	kc.workloadInformer = kc.informerFactory.Kueue().V1beta1().Workloads().Informer()
	kc.workloadLister = kc.informerFactory.Kueue().V1beta1().Workloads().Lister()

	// LocalQueue Informer
	kc.localQueueInformer = kc.informerFactory.Kueue().V1beta1().LocalQueues().Informer()
	kc.localQueueLister = kc.informerFactory.Kueue().V1beta1().LocalQueues().Lister()

	// ClusterQueue Informer
	kc.clusterQueueInformer = kc.informerFactory.Kueue().V1beta1().ClusterQueues().Informer()
	kc.clusterQueueLister = kc.informerFactory.Kueue().V1beta1().ClusterQueues().Lister()

	// ResourceFlavor Informer
	kc.resourceFlavorInformer = kc.informerFactory.Kueue().V1beta1().ResourceFlavors().Informer()
	kc.resourceFlavorLister = kc.informerFactory.Kueue().V1beta1().ResourceFlavors().Lister()

	stopCh := make(chan struct{}, 0)
	go func() {
		kc.informerFactory.Start(stopCh)
		defer close(stopCh)
		// timeout 60
		ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
		defer cancel()

		if !kc.WaitForCacheSync(ctx) {
			zlog.SugarErrorf("Timed out waiting for Kueue cache sync. Kueue may not be installed.")
		} else {
			zlog.SugarInfoln("Kueue informers cache synced successfully.")
		}
	}()
}

func (kc *KueueClient) AddWorkloadEventHandler(handler cache.ResourceEventHandler) {
	kc.workloadInformer.AddEventHandler(handler)
	kc.eventHandlers["workload"] = append(kc.eventHandlers["workload"], handler)
}

func (kc *KueueClient) AddLocalQueueEventHandler(handler cache.ResourceEventHandler) {
	kc.localQueueInformer.AddEventHandler(handler)
	kc.eventHandlers["localqueue"] = append(kc.eventHandlers["localqueue"], handler)
}

func (kc *KueueClient) AddClusterQueueEventHandler(handler cache.ResourceEventHandler) {
	kc.clusterQueueInformer.AddEventHandler(handler)
	kc.eventHandlers["clusterqueue"] = append(kc.eventHandlers["clusterqueue"], handler)
}

func (kc *KueueClient) ListWorkloads(namespace string) ([]*kueuev1beta1.Workload, error) {
	if namespace == "" {
		return kc.workloadLister.List(labels.Everything())
	}
	return kc.workloadLister.Workloads(namespace).List(labels.Everything())
}

func (kc *KueueClient) GetWorkload(namespace, name string) (*kueuev1beta1.Workload, error) {
	return kc.workloadLister.Workloads(namespace).Get(name)
}

func (kc *KueueClient) ListWorkloadsBySelector(namespace string, selector labels.Selector) ([]*kueuev1beta1.Workload, error) {
	if namespace == "" {
		return kc.workloadLister.List(selector)
	}
	return kc.workloadLister.Workloads(namespace).List(selector)
}

func (kc *KueueClient) CreateWorkload(ctx context.Context, workload *kueuev1beta1.Workload) (*kueuev1beta1.Workload, error) {
	return kc.clientset.KueueV1beta1().Workloads(workload.Namespace).Create(ctx, workload, metav1.CreateOptions{})
}

func (kc *KueueClient) UpdateWorkload(ctx context.Context, workload *kueuev1beta1.Workload) (*kueuev1beta1.Workload, error) {
	return kc.clientset.KueueV1beta1().Workloads(workload.Namespace).Update(ctx, workload, metav1.UpdateOptions{})
}

func (kc *KueueClient) DeleteWorkload(ctx context.Context, namespace, name string) error {
	return kc.clientset.KueueV1beta1().Workloads(namespace).Delete(ctx, name, metav1.DeleteOptions{})
}

func (kc *KueueClient) ListLocalQueues(namespace string) ([]*kueuev1beta1.LocalQueue, error) {
	if namespace == "" {
		return kc.localQueueLister.List(labels.Everything())
	}
	return kc.localQueueLister.LocalQueues(namespace).List(labels.Everything())
}

func (kc *KueueClient) GetLocalQueue(namespace, name string) (*kueuev1beta1.LocalQueue, error) {
	return kc.localQueueLister.LocalQueues(namespace).Get(name)
}

func (kc *KueueClient) ListClusterQueues() ([]*kueuev1beta1.ClusterQueue, error) {
	return kc.clusterQueueLister.List(labels.Everything())
}

func (kc *KueueClient) GetClusterQueue(name string) (*kueuev1beta1.ClusterQueue, error) {
	return kc.clusterQueueLister.Get(name)
}

func (kc *KueueClient) ListResourceFlavors() ([]*kueuev1beta1.ResourceFlavor, error) {
	return kc.resourceFlavorLister.List(labels.Everything())
}

func (kc *KueueClient) GetResourceFlavor(name string) (*kueuev1beta1.ResourceFlavor, error) {
	return kc.resourceFlavorLister.Get(name)
}

func (kc *KueueClient) GetWorkloadStats(namespace string) (*WorkloadStats, error) {
	workloads, err := kc.ListWorkloads(namespace)
	if err != nil {
		return nil, fmt.Errorf("failed to list workloads: %w", err)
	}

	stats := &WorkloadStats{
		Total:      len(workloads),
		ByStatus:   make(map[string]int),
		ByPriority: make(map[int32]int),
		Resources: &ResourceStats{
			CPU:       make(map[string]int64),
			Memory:    make(map[string]int64),
			GPU:       make(map[string]int64),
			GPUMemory: make(map[string]int64),
		},
	}

	for _, wl := range workloads {
		// 统计状态
		for _, condition := range wl.Status.Conditions {
			if condition.Type == kueuev1beta1.WorkloadAdmitted {
				if condition.Status == metav1.ConditionTrue {
					stats.ByStatus["Admitted"]++
				} else {
					stats.ByStatus["Pending"]++
				}
				break
			}
		}

		// 统计优先级
		if wl.Spec.PriorityClassName != "" {
			// 这里需要根据PriorityClassName获取实际的优先级值
			// 简化处理，使用默认值
			stats.ByPriority[0]++
		}

		// 统计资源
		for _, podSet := range wl.Spec.PodSets {
			if podSet.Template.Spec.Containers != nil {
				for _, container := range podSet.Template.Spec.Containers {
					if container.Resources.Requests != nil {
						if cpu := container.Resources.Requests.Cpu(); cpu != nil {
							stats.Resources.CPU["requested"] += cpu.MilliValue()
						}
						if memory := container.Resources.Requests.Memory(); memory != nil {
							stats.Resources.Memory["requested"] += memory.Value()
						}
						// GPU资源需要根据具体的资源名称来获取
						for resourceName, quantity := range container.Resources.Requests {
							if resourceName.String() == "nvidia.com/gpu" {
								stats.Resources.GPU["requested"] += quantity.Value()
							}
							if resourceName.String() == "nvidia.com/gpu-memory" {
								stats.Resources.GPUMemory["requested"] += quantity.Value()
							}
						}
					}
				}
			}
		}
	}

	return stats, nil
}

type WorkloadStats struct {
	Total      int            `json:"total"`
	ByStatus   map[string]int `json:"by_status"`
	ByPriority map[int32]int  `json:"by_priority"`
	Resources  *ResourceStats `json:"resources"`
}

type ResourceStats struct {
	CPU       map[string]int64 `json:"cpu"`        // 毫核
	Memory    map[string]int64 `json:"memory"`     // 字节
	GPU       map[string]int64 `json:"gpu"`        // 核数
	GPUMemory map[string]int64 `json:"gpu_memory"` // 字节
}

func (kc *KueueClient) GetClientset() kueueclientset.Interface {
	return kc.clientset
}

func (kc *KueueClient) GetInformerFactory() kueueinformers.SharedInformerFactory {
	return kc.informerFactory
}
