package client

import (
	"context"
	"fmt"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/tools/cache"
	kueuev1beta1 "sigs.k8s.io/kueue/apis/kueue/v1beta1"
	kueueclientset "sigs.k8s.io/kueue/client-go/clientset/versioned"
	kueueinformers "sigs.k8s.io/kueue/client-go/informers/externalversions"
	kueuelisters "sigs.k8s.io/kueue/client-go/listers/kueue/v1beta1"

	"transwarp.io/mlops/mlops-std/stdlog"
)

// KueueClient Kueue客户端封装
type KueueClient struct {
	clientset       kueueclientset.Interface
	informerFactory kueueinformers.SharedInformerFactory

	// Listers
	workloadLister       kueuelisters.WorkloadLister
	localQueueLister     kueuelisters.LocalQueueLister
	clusterQueueLister   kueuelisters.ClusterQueueLister
	resourceFlavorLister kueuelisters.ResourceFlavorLister

	// Informers
	workloadInformer       cache.SharedIndexInformer
	localQueueInformer     cache.SharedIndexInformer
	clusterQueueInformer   cache.SharedIndexInformer
	resourceFlavorInformer cache.SharedIndexInformer

	// 事件处理器
	eventHandlers map[string][]cache.ResourceEventHandler
}

// NewKueueClient 创建新的Kueue客户端
func NewKueueClient() *KueueClient {
	clientset := kueueclientset.NewForConfigOrDie(nil)
	client := &KueueClient{
		clientset:       clientset,
		informerFactory: kueueinformers.NewSharedInformerFactory(clientset, 10*time.Minute),
		eventHandlers:   make(map[string][]cache.ResourceEventHandler),
	}

	client.setupInformers()
	return client
}

// setupInformers 设置Informers和Listers
func (kc *KueueClient) setupInformers() {
	// Workload Informer
	kc.workloadInformer = kc.informerFactory.Kueue().V1beta1().Workloads().Informer()
	kc.workloadLister = kc.informerFactory.Kueue().V1beta1().Workloads().Lister()

	// LocalQueue Informer
	kc.localQueueInformer = kc.informerFactory.Kueue().V1beta1().LocalQueues().Informer()
	kc.localQueueLister = kc.informerFactory.Kueue().V1beta1().LocalQueues().Lister()

	// ClusterQueue Informer
	kc.clusterQueueInformer = kc.informerFactory.Kueue().V1beta1().ClusterQueues().Informer()
	kc.clusterQueueLister = kc.informerFactory.Kueue().V1beta1().ClusterQueues().Lister()

	// ResourceFlavor Informer
	kc.resourceFlavorInformer = kc.informerFactory.Kueue().V1beta1().ResourceFlavors().Informer()
	kc.resourceFlavorLister = kc.informerFactory.Kueue().V1beta1().ResourceFlavors().Lister()
}

// AddWorkloadEventHandler 添加Workload事件处理器
func (kc *KueueClient) AddWorkloadEventHandler(handler cache.ResourceEventHandler) {
	kc.workloadInformer.AddEventHandler(handler)
	kc.eventHandlers["workload"] = append(kc.eventHandlers["workload"], handler)
}

// AddLocalQueueEventHandler 添加LocalQueue事件处理器
func (kc *KueueClient) AddLocalQueueEventHandler(handler cache.ResourceEventHandler) {
	kc.localQueueInformer.AddEventHandler(handler)
	kc.eventHandlers["localqueue"] = append(kc.eventHandlers["localqueue"], handler)
}

// AddClusterQueueEventHandler 添加ClusterQueue事件处理器
func (kc *KueueClient) AddClusterQueueEventHandler(handler cache.ResourceEventHandler) {
	kc.clusterQueueInformer.AddEventHandler(handler)
	kc.eventHandlers["clusterqueue"] = append(kc.eventHandlers["clusterqueue"], handler)
}

// ListWorkloads 列出所有Workloads
func (kc *KueueClient) ListWorkloads(namespace string) ([]*kueuev1beta1.Workload, error) {
	if namespace == "" {
		return kc.workloadLister.List(labels.Everything())
	}
	return kc.workloadLister.Workloads(namespace).List(labels.Everything())
}

// GetWorkload 获取指定的Workload
func (kc *KueueClient) GetWorkload(namespace, name string) (*kueuev1beta1.Workload, error) {
	return kc.workloadLister.Workloads(namespace).Get(name)
}

// ListWorkloadsBySelector 根据标签选择器列出Workloads
func (kc *KueueClient) ListWorkloadsBySelector(namespace string, selector labels.Selector) ([]*kueuev1beta1.Workload, error) {
	if namespace == "" {
		return kc.workloadLister.List(selector)
	}
	return kc.workloadLister.Workloads(namespace).List(selector)
}

// CreateWorkload 创建Workload
func (kc *KueueClient) CreateWorkload(ctx context.Context, workload *kueuev1beta1.Workload) (*kueuev1beta1.Workload, error) {
	return kc.clientset.KueueV1beta1().Workloads(workload.Namespace).Create(ctx, workload, metav1.CreateOptions{})
}

// UpdateWorkload 更新Workload
func (kc *KueueClient) UpdateWorkload(ctx context.Context, workload *kueuev1beta1.Workload) (*kueuev1beta1.Workload, error) {
	return kc.clientset.KueueV1beta1().Workloads(workload.Namespace).Update(ctx, workload, metav1.UpdateOptions{})
}

// DeleteWorkload 删除Workload
func (kc *KueueClient) DeleteWorkload(ctx context.Context, namespace, name string) error {
	return kc.clientset.KueueV1beta1().Workloads(namespace).Delete(ctx, name, metav1.DeleteOptions{})
}

// ListLocalQueues 列出所有LocalQueues
func (kc *KueueClient) ListLocalQueues(namespace string) ([]*kueuev1beta1.LocalQueue, error) {
	if namespace == "" {
		return kc.localQueueLister.List(labels.Everything())
	}
	return kc.localQueueLister.LocalQueues(namespace).List(labels.Everything())
}

// GetLocalQueue 获取指定的LocalQueue
func (kc *KueueClient) GetLocalQueue(namespace, name string) (*kueuev1beta1.LocalQueue, error) {
	return kc.localQueueLister.LocalQueues(namespace).Get(name)
}

// ListClusterQueues 列出所有ClusterQueues
func (kc *KueueClient) ListClusterQueues() ([]*kueuev1beta1.ClusterQueue, error) {
	return kc.clusterQueueLister.List(labels.Everything())
}

// GetClusterQueue 获取指定的ClusterQueue
func (kc *KueueClient) GetClusterQueue(name string) (*kueuev1beta1.ClusterQueue, error) {
	return kc.clusterQueueLister.Get(name)
}

// ListResourceFlavors 列出所有ResourceFlavors
func (kc *KueueClient) ListResourceFlavors() ([]*kueuev1beta1.ResourceFlavor, error) {
	return kc.resourceFlavorLister.List(labels.Everything())
}

// GetResourceFlavor 获取指定的ResourceFlavor
func (kc *KueueClient) GetResourceFlavor(name string) (*kueuev1beta1.ResourceFlavor, error) {
	return kc.resourceFlavorLister.Get(name)
}

// WaitForCacheSync 等待缓存同步
func (kc *KueueClient) WaitForCacheSync(ctx context.Context) bool {
	return cache.WaitForCacheSync(ctx.Done(),
		kc.workloadInformer.HasSynced,
		kc.localQueueInformer.HasSynced,
		kc.clusterQueueInformer.HasSynced,
		kc.resourceFlavorInformer.HasSynced,
	)
}

// GetWorkloadStats 获取Workload统计信息
func (kc *KueueClient) GetWorkloadStats(namespace string) (*WorkloadStats, error) {
	workloads, err := kc.ListWorkloads(namespace)
	if err != nil {
		return nil, fmt.Errorf("failed to list workloads: %w", err)
	}

	stats := &WorkloadStats{
		Total:      len(workloads),
		ByStatus:   make(map[string]int),
		ByPriority: make(map[int32]int),
		Resources: &ResourceStats{
			CPU:       make(map[string]int64),
			Memory:    make(map[string]int64),
			GPU:       make(map[string]int64),
			GPUMemory: make(map[string]int64),
		},
	}

	for _, wl := range workloads {
		// 统计状态
		for _, condition := range wl.Status.Conditions {
			if condition.Type == kueuev1beta1.WorkloadAdmitted {
				if condition.Status == metav1.ConditionTrue {
					stats.ByStatus["Admitted"]++
				} else {
					stats.ByStatus["Pending"]++
				}
				break
			}
		}

		// 统计优先级
		if wl.Spec.PriorityClassName != "" {
			// 这里需要根据PriorityClassName获取实际的优先级值
			// 简化处理，使用默认值
			stats.ByPriority[0]++
		}

		// 统计资源
		for _, podSet := range wl.Spec.PodSets {
			if podSet.Template.Spec.Containers != nil {
				for _, container := range podSet.Template.Spec.Containers {
					if container.Resources.Requests != nil {
						if cpu := container.Resources.Requests.Cpu(); cpu != nil {
							stats.Resources.CPU["requested"] += cpu.MilliValue()
						}
						if memory := container.Resources.Requests.Memory(); memory != nil {
							stats.Resources.Memory["requested"] += memory.Value()
						}
						// GPU资源需要根据具体的资源名称来获取
						for resourceName, quantity := range container.Resources.Requests {
							if resourceName.String() == "nvidia.com/gpu" {
								stats.Resources.GPU["requested"] += quantity.Value()
							}
							if resourceName.String() == "nvidia.com/gpu-memory" {
								stats.Resources.GPUMemory["requested"] += quantity.Value()
							}
						}
					}
				}
			}
		}
	}

	return stats, nil
}

// WorkloadStats Workload统计信息
type WorkloadStats struct {
	Total      int            `json:"total"`
	ByStatus   map[string]int `json:"by_status"`
	ByPriority map[int32]int  `json:"by_priority"`
	Resources  *ResourceStats `json:"resources"`
}

// ResourceStats 资源统计信息
type ResourceStats struct {
	CPU       map[string]int64 `json:"cpu"`        // 毫核
	Memory    map[string]int64 `json:"memory"`     // 字节
	GPU       map[string]int64 `json:"gpu"`        // 核数
	GPUMemory map[string]int64 `json:"gpu_memory"` // 字节
}

// StartInformers 启动Informers
func (kc *KueueClient) StartInformers(ctx context.Context) {
	stdlog.Info("Starting Kueue informers")
	kc.informerFactory.Start(ctx.Done())

	// 等待缓存同步
	if !kc.WaitForCacheSync(ctx) {
		stdlog.Error("Failed to sync Kueue informer caches")
		return
	}

	stdlog.Info("Kueue informers started and synced successfully")
}

// GetClientset 获取原始的Kueue clientset
func (kc *KueueClient) GetClientset() kueueclientset.Interface {
	return kc.clientset
}

// GetInformerFactory 获取Informer Factory
func (kc *KueueClient) GetInformerFactory() kueueinformers.SharedInformerFactory {
	return kc.informerFactory
}
