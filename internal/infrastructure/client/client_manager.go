package client

import (
	"context"
	"fmt"
	"sync"
	"time"

	"transwarp.io/mlops/mlops-std/stdlog"
)

var (
	GlobalManager ClientManager
	once          sync.Once
)

func init() {
	initGlobalManager()
}

func initGlobalManager() ClientManager {
	once.Do(func() {
		GlobalManager = NewClientManager()
	})
	return GlobalManager
}

type ClientManager interface {
	Register(factory ClientFactory) error

	Initialize(ctx context.Context, configs map[string]interface{}) error

	Start(ctx context.Context) error

	Stop(ctx context.Context) error

	Get(name string) (Client, error)

	GetTyped(name string, target interface{}) error

	HealthCheck(ctx context.Context) map[string]error
}

type DefaultClientManager struct {
	factories map[string]ClientFactory
	clients   map[string]Client
	infos     map[string]*ClientInfo
	mu        sync.RWMutex

	initOrder []string
}

func NewClientManager() ClientManager {
	return &DefaultClientManager{
		factories: make(map[string]ClientFactory),
		clients:   make(map[string]Client),
		infos:     make(map[string]*ClientInfo),
	}
}

func (cm *DefaultClientManager) Register(factory ClientFactory) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	name := factory.Name()
	if _, exists := cm.factories[name]; exists {
		return fmt.Errorf("client factory %s already registered", name)
	}

	cm.factories[name] = factory
	cm.infos[name] = &ClientInfo{
		Name:         name,
		Status:       StatusUninitialized,
		Dependencies: []string{},
	}

	stdlog.Info("Registered client factory", "name", name)
	return nil
}

func (cm *DefaultClientManager) Initialize(ctx context.Context, configs map[string]interface{}) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	if err := cm.calculateInitOrder(); err != nil {
		return fmt.Errorf("failed to calculate initialization order: %w", err)
	}

	for _, name := range cm.initOrder {
		if err := cm.initializeClient(ctx, name, configs[name]); err != nil {
			return fmt.Errorf("failed to initialize client %s: %w", name, err)
		}
	}

	stdlog.Info("All clients initialized successfully")
	return nil
}

func (cm *DefaultClientManager) initializeClient(ctx context.Context, name string, options interface{}) error {
	factory, exists := cm.factories[name]
	if !exists {
		return fmt.Errorf("client factory %s not found", name)
	}

	info := cm.infos[name]
	info.Status = StatusUninitialized

	client, err := factory.Create(options)
	if err != nil {
		info.Status = StatusError
		info.LastError = err
		return fmt.Errorf("failed to create client %s: %w", name, err)
	}

	if err := client.Initialize(ctx); err != nil {
		info.Status = StatusError
		info.LastError = err
		return fmt.Errorf("failed to initialize client %s: %w", name, err)
	}

	cm.clients[name] = client
	info.Status = StatusInitialized
	info.Options = options
	info.Dependencies = client.Dependencies()

	stdlog.Info("Client initialized", "name", name)
	return nil
}

func (cm *DefaultClientManager) Start(ctx context.Context) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	for _, name := range cm.initOrder {
		if err := cm.startClient(ctx, name); err != nil {
			return fmt.Errorf("failed to start client %s: %w", name, err)
		}
	}

	stdlog.Info("All clients started successfully")
	return nil
}

func (cm *DefaultClientManager) startClient(ctx context.Context, name string) error {
	client, exists := cm.clients[name]
	if !exists {
		return fmt.Errorf("client %s not found", name)
	}

	info := cm.infos[name]

	if err := client.Start(ctx); err != nil {
		info.Status = StatusError
		info.LastError = err
		return fmt.Errorf("failed to start client %s: %w", name, err)
	}

	now := time.Now()
	info.Status = StatusStarted
	info.StartTime = &now
	info.LastError = nil

	stdlog.Info("Client started", "name", name)
	return nil
}

func (cm *DefaultClientManager) Stop(ctx context.Context) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	for i := len(cm.initOrder) - 1; i >= 0; i-- {
		name := cm.initOrder[i]
		if err := cm.stopClient(ctx, name); err != nil {
			stdlog.Error("Failed to stop client", "name", name, "error", err)
		}
	}

	stdlog.Info("All clients stopped")
	return nil
}

func (cm *DefaultClientManager) stopClient(ctx context.Context, name string) error {
	client, exists := cm.clients[name]
	if !exists {
		return nil
	}

	info := cm.infos[name]

	if err := client.Stop(ctx); err != nil {
		info.Status = StatusError
		info.LastError = err
		return fmt.Errorf("failed to stop client %s: %w", name, err)
	}

	info.Status = StatusStopped
	stdlog.Info("Client stopped", "name", name)
	return nil
}

func (cm *DefaultClientManager) Get(name string) (Client, error) {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	client, exists := cm.clients[name]
	if !exists {
		return nil, fmt.Errorf("client %s not found", name)
	}

	return client, nil
}

func (cm *DefaultClientManager) GetTyped(name string, target interface{}) error {
	client, err := cm.Get(name)
	if err != nil {
		return err
	}

	switch t := target.(type) {
	case **PipelineClient:
		if pc, ok := client.(*PipelineClientWrapper); ok {
			*t = pc.client
			return nil
		}
	case **K8sClientset:
		if kic, ok := client.(*K8sClientWrapper); ok {
			*t = kic.client
			return nil
		}
	case **CVClient:
		if cvc, ok := client.(*CVClientWrapper); ok {
			*t = cvc.client
			return nil
		}
	case **ArgoEventClient:
		if aec, ok := client.(*ArgoEventClientWrapper); ok {
			*t = aec.client
			return nil
		}
	case **KueueClient:
		if kc, ok := client.(*KueueClientWrapper); ok {
			*t = kc.client
			return nil
		}
	}

	return fmt.Errorf("client %s cannot be cast to requested type", name)
}

func (cm *DefaultClientManager) HealthCheck(ctx context.Context) map[string]error {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	results := make(map[string]error)

	for name, client := range cm.clients {
		if err := client.HealthCheck(ctx); err != nil {
			results[name] = err
			cm.infos[name].LastError = err
		}
	}

	return results
}

func (cm *DefaultClientManager) calculateInitOrder() error {
	// 构建依赖图
	graph := make(map[string][]string)
	inDegree := make(map[string]int)

	// 初始化
	for name := range cm.factories {
		graph[name] = []string{}
		inDegree[name] = 0
	}

	// 构建依赖关系
	for name, factory := range cm.factories {
		client, err := factory.Create(nil) // 临时创建以获取依赖信息
		if err != nil {
			continue
		}

		deps := client.Dependencies()
		for _, dep := range deps {
			if _, exists := cm.factories[dep]; !exists {
				return fmt.Errorf("dependency %s not found for client %s", dep, name)
			}
			graph[dep] = append(graph[dep], name)
			inDegree[name]++
		}
	}

	// 拓扑排序
	queue := []string{}
	for name, degree := range inDegree {
		if degree == 0 {
			queue = append(queue, name)
		}
	}

	order := []string{}
	for len(queue) > 0 {
		current := queue[0]
		queue = queue[1:]
		order = append(order, current)

		for _, neighbor := range graph[current] {
			inDegree[neighbor]--
			if inDegree[neighbor] == 0 {
				queue = append(queue, neighbor)
			}
		}
	}

	if len(order) != len(cm.factories) {
		return fmt.Errorf("circular dependency detected")
	}

	cm.initOrder = order
	return nil
}

func (cm *DefaultClientManager) GetClientInfos() map[string]*ClientInfo {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	infos := make(map[string]*ClientInfo)
	for name, info := range cm.infos {
		infoCopy := *info
		infos[name] = &infoCopy
	}

	return infos
}
