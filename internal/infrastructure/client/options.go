package client

import "time"

type K8sClientOptions struct {
}

type PipelineClientOptions struct {
}

type KueueClientOptions struct {
}

type MinioClientOptions struct {
	MinioEndpoint         string
	AccessKey             string
	SecretKey             string
	Secure                bool
	Gegion                string
	InitConnectionTimeout time.Duration
}

type ArgoEventClientOptions struct {
}

type CVClientOptions struct {
	baseURL string
}
