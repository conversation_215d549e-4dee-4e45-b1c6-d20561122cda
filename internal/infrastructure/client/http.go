package client

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
	"transwarp.io/mlops/mlops-std/stderr"
	"transwarp.io/mlops/mlops-std/stdlog"
	token_util "transwarp.io/mlops/mlops-std/token"
)

type HttpBaseClient struct {
	*http.Client
	baseUrl string
}

func NewHttpClient(url string) *HttpBaseClient {
	return &HttpBaseClient{
		Client: &http.Client{
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{InsecureSkipVerify: true}},
			Timeout: 30 * time.Second,
		},
		baseUrl: url,
	}
}

func (a *HttpBaseClient) url() string {
	return a.baseUrl
}

func (m *HttpBaseClient) doRequest(ctx context.Context, method string, requestUrl string, requestBody interface{}, result interface{}) error {
	var req *http.Request
	var err error
	if requestBody != nil {
		// 将请求体转换为 JSON 格式
		jsonBody, err := json.Marshal(requestBody)
		if err != nil {
			return stderr.Internal.Errorf("request body failed to marshal json, err: %+v, requestBody: %+v", err, requestBody)
		}

		req, err = http.NewRequest(method, requestUrl, bytes.NewBuffer(jsonBody))
		if err != nil {
			return stderr.Internal.Errorf("failed to New Request, err: %+v", err)
		}
	} else {
		req, err = http.NewRequest(method, requestUrl, nil)
		if err != nil {
			return stderr.Internal.Errorf("failed to New Request, err: %+v", err)
		}
	}
	token, err := token_util.GetToken(ctx)
	if err != nil {
		return err
	}
	req.Header.Set(token_util.AUTH_HEADER, token)
	req.Header.Set("Content-Type", "application/json")
	start := time.Now()
	rsp, err := m.Do(req)
	stdlog.Info(fmt.Sprintf("do request result:err=%+v,rsp=%+v", err, rsp))

	if err != nil {
		return stderr.Internal.Errorf("failed to do req, err: %+v", err)
	}
	defer rsp.Body.Close()

	body, err := io.ReadAll(rsp.Body)
	if err != nil {
		return stderr.Internal.Errorf("failed to read bdoy, err: %+v", err)
	}
	end := time.Now()
	stdlog.Debug(fmt.Sprintf("do request times:%d", end.Sub(start).Milliseconds()))
	if rsp.StatusCode != 200 {
		return fmt.Errorf("status code:%d, err message: %+v", rsp.StatusCode, string(body))
	}
	stdlog.Debug(fmt.Sprintf("do request success,response:%s", string(body)))
	if err := json.Unmarshal(body, &result); err != nil {
		stdlog.Debug(fmt.Errorf("failed to unmarshal json:%s", string(body)))
	}
	return nil
}
