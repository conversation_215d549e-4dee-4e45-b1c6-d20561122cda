package client

import (
	"context"
	"time"
)

type Client interface {
	Name() string

	Initialize(ctx context.Context) error

	Start(ctx context.Context) error

	Stop(ctx context.Context) error

	HealthCheck(ctx context.Context) error

	Dependencies() []string
}

type ClientFactory interface {
	Create(options interface{}) (Client, error)

	Name() string
}

type ClientStatus int

const (
	StatusUninitialized ClientStatus = iota
	StatusInitialized
	StatusStarted
	StatusStopped
	StatusError
)

type ClientInfo struct {
	Name         string       `json:"name"`
	Status       ClientStatus `json:"status"`
	Dependencies []string     `json:"dependencies"`
	LastError    error        `json:"last_error,omitempty"`
	StartTime    *time.Time   `json:"start_time,omitempty"`
	Options      interface{}  `json:"options,omitempty"`
}
