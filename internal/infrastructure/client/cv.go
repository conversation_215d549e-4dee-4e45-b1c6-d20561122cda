package client

import (
	"context"
	"fmt"

	tokenutil "transwarp.io/mlops/mlops-std/token"
	"transwarp.io/mlops/pipeline/conf"
	"transwarp.io/mlops/pipeline/pkg/util/httpclient"
)

type CVClient struct {
	*httpclient.HttpClient
}

func NewCVClient() *CVClient {
	url := "http://autocv-cvat-service:80"
	if conf.PipeineConfig.Pipeline.Cvat.Host != "" {
		url = fmt.Sprintf("http://%s:%s", conf.PipeineConfig.Pipeline.Cvat.Host, conf.PipeineConfig.Pipeline.Cvat.Port)
	}
	return &CVClient{
		HttpClient: httpclient.NewHttpClient(url,
			httpclient.WithInsecureSkipVerify()),
	}
}

type MLOpsServiceBaseInfo struct {
	Id         string `json:"id"`
	Name       string `json:"name"`
	ProjectID  string `json:"project_id"`
	SourceType string `json:"source_type"` // 来源类型 [cv.SourceType], 接口返回的是枚举的变量名
}

type MLOpsServiceList struct {
	MLOpsServiceInfos []*MLOpsServiceBaseInfo `protobuf:"bytes,3,rep,name=service_infos,json=serviceInfos,proto3" json:"service_infos" description:"服务基础信息"`
}

type TaskDetail struct {
	ID            string        `json:"id"`
	Name          string        `json:"name"`
	Status        string        `json:"status"`
	ProjectID     string        `json:"projectId"`
	TenantUID     string        `json:"tenantUid"`
	CatalogParams CatalogParams `json:"catalogParams"`
	AssetFiles    []AssetFile   `json:"assetFiles"`
	KBPTmplType   string        `json:"kBPTmplType"`
}

type CatalogParams struct {
	CatalogID string `json:"catalogId"`
	LabelIDs  []int  `json:"labelIDs"`
}

type AssetFile struct {
	AssetID        string `json:"assetId"`
	AssetVersionID string `json:"assetVersionId"`
}

func (c *CVClient) GetTaskDetail(ctx context.Context, taskId string) (*TaskDetail, error) {
	path := fmt.Sprintf("/api/process/tasks/%s/detail", taskId)

	headers, err := getTokenHeader(ctx)
	if err != nil {
		return nil, err
	}
	result, err := httpclient.GetT[TaskDetail](ctx, c.HttpClient, path, nil, headers)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func getTokenHeader(ctx context.Context) (map[string]string, error) {
	token, err := tokenutil.GetToken(ctx)
	if err != nil {
		return nil, err
	}
	return map[string]string{
		tokenutil.AUTH_HEADER: token,
	}, nil
}

const (
	CVClientName = "cv"
)

func init() {
	if err := GlobalManager.Register(NewCVClientFactory()); err != nil {
		panic(fmt.Sprintf("failed to register cv client factory: %w", err))
	}
}

type CVClientWrapper struct {
	client *CVClient

	options *CVClientOptions
}

type CVClientFactory struct{}

func NewCVClientFactory() ClientFactory {
	return &CVClientFactory{}
}

func (f *CVClientFactory) Name() string {
	return CVClientName
}

func (f *CVClientFactory) Create(options interface{}) (Client, error) {
	ops, ok := options.(*CVClientOptions)
	if !ok {
		return nil, fmt.Errorf("invalid config type for pipeline client")
	}

	return &CVClientWrapper{
		options: ops,
	}, nil
}

func (w *CVClientWrapper) Name() string {
	return CVClientName
}

func (w *CVClientWrapper) Dependencies() []string {
	return []string{}
}

func (w *CVClientWrapper) Initialize(ctx context.Context) error {
	w.client = NewCVClient()
	return nil
}

func (w *CVClientWrapper) Start(ctx context.Context) error {
	return nil
}

func (w *CVClientWrapper) Stop(ctx context.Context) error {
	return nil
}

func (w *CVClientWrapper) HealthCheck(ctx context.Context) error {
	if w.client == nil {
		return fmt.Errorf("pipeline client not initialized")
	}

	return nil
}

func (w *CVClientWrapper) GetClient() *CVClient {
	return w.client
}
