package client

import (
	"context"
	"fmt"
	"transwarp.io/mlops/pipeline/conf"
)

type CvClient struct {
	*HttpBaseClient
}

func NewCvClient() *CvClient {
	url := "http://autocv-cvat-service:80"
	if conf.PipeineConfig.Pipeline.Cvat.Host != "" {
		url = fmt.Sprintf("http://%s:%s", conf.PipeineConfig.Pipeline.Cvat.Host, conf.PipeineConfig.Pipeline.Cvat.Port)
	}
	return &CvClient{
		HttpBaseClient: NewHttpClient(url),
	}
}

func (c *CvClient) url() string {
	return c.baseUrl
}

type MLOpsServiceBaseInfo struct {
	Id         string `json:"id"`
	Name       string `json:"name"`
	ProjectID  string `json:"project_id"`
	SourceType string `json:"source_type"` // 来源类型 [cv.SourceType], 接口返回的是枚举的变量名
}

type MLOpsServiceList struct {
	MLOpsServiceInfos []*MLOpsServiceBaseInfo `protobuf:"bytes,3,rep,name=service_infos,json=serviceInfos,proto3" json:"service_infos" description:"服务基础信息"`
}


type TaskDetail struct {
	ID            string        `json:"id"`
	Name          string        `json:"name"`
	Status        string        `json:"status"`
	ProjectID     string        `json:"projectId"`
	TenantUID     string        `json:"tenantUid"`
	CatalogParams CatalogParams `json:"catalogParams"`
	AssetFiles    []AssetFile   `json:"assetFiles"`
	KBPTmplType   string        `json:"kBPTmplType"`
}

type CatalogParams struct {
	CatalogID string `json:"catalogId"`
	LabelIDs  []int  `json:"labelIDs"`
}

type AssetFile struct {
	AssetID        string `json:"assetId"`
	AssetVersionID string `json:"assetVersionId"`
}


func (c *CvClient) GetTaskDetail(ctx context.Context, taskId string) (*TaskDetail, error) {
	requestUrl := c.url() + fmt.Sprintf("/api/process/tasks/%s/detail", taskId)
	var result *TaskDetail
	method := "GET"
	err := c.doRequest(ctx, method, requestUrl, nil, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}