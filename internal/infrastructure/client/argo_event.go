package client

import (
	"bytes"
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	eventsv1alpha1 "github.com/argoproj/argo-events/pkg/apis/events/v1alpha1"
	clientset "github.com/argoproj/argo-events/pkg/client/clientset/versioned"
	versionedclient "github.com/argoproj/argo-events/pkg/client/clientset/versioned"
	argoeventinforemer "github.com/argoproj/argo-events/pkg/client/informers/externalversions"
	listers "github.com/argoproj/argo-events/pkg/client/listers/events/v1alpha1"
	"github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/fields"
	"k8s.io/utils/ptr"
	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	"transwarp.io/applied-ai/aiot/vision-std/boot/k8s"
	client "transwarp.io/mlops/mlops-std/k8s"
	"transwarp.io/mlops/mlops-std/stderr"
	"transwarp.io/mlops/mlops-std/stdlog"
	"transwarp.io/mlops/pipeline/conf"
	_const "transwarp.io/mlops/pipeline/const"
	"transwarp.io/mlops/pipeline/internal/core/model"
)

const (
	ArgoEventClientName = "argo-event"
)

type ArgoEventClient struct {
	sensorLister listers.SensorLister
	ebLister     listers.EventBusLister
	esLister     listers.EventSourceLister
	clientset    *clientset.Clientset
	stopCh       chan struct{}
}

func NewArgoEventClient() (*ArgoEventClient, error) {
	client := &ArgoEventClient{}
	client.Init()
	return client, nil
}

func (s *ArgoEventClient) Init() error {
	config, err := client.GetK8sRestConfig()
	if err != nil {
		return stderr.Wrap(err, "init argo cli err :%v", err)
	}
	s.clientset, err = versionedclient.NewForConfig(config)
	if err != nil {
		return stderr.Wrap(err, "init argo cli err :%v", err)
	}
	factory := argoeventinforemer.NewSharedInformerFactoryWithOptions(
		s.clientset,
		30*time.Second, // resync 周期
		argoeventinforemer.WithTweakListOptions(func(opts *metav1.ListOptions) {
			opts.FieldSelector = fields.Everything().String()
		}),
	)

	s.sensorLister = factory.Argoproj().V1alpha1().Sensors().Lister()
	s.ebLister = factory.Argoproj().V1alpha1().EventBus().Lister()
	s.esLister = factory.Argoproj().V1alpha1().EventSources().Lister()
	// 启动 informer
	s.stopCh = make(chan struct{}) // 创建 stopCh
	go factory.Start(s.stopCh)

	// 等待缓存同步完成
	synced := factory.WaitForCacheSync(s.stopCh)
	for t, ok := range synced {
		if !ok {
			return fmt.Errorf("cache for %v failed to sync", t)
		}
	}
	return nil
}

func (s *ArgoEventClient) InitNamespaceResource(namespace string, projectId string) error {

	err := s.initSecret(namespace)
	if err != nil {
		stdlog.Error(fmt.Sprintf("error in init namespace %s sc: %+v", namespace, err))
		return stderr.Internal.Errorf("error in init namespace %s sc: %s", namespace, err.Error())
	}
	err = s.initEventBus(namespace)
	if err != nil {
		stdlog.Error(fmt.Sprintf("error in init namespace %s eb: %+v", namespace, err))
		return stderr.Internal.Errorf("error in init namespace %s eb: %s", namespace, err.Error())
	}
	err = s.initEventSource(namespace, projectId)
	if err != nil {
		stdlog.Error(fmt.Sprintf("error in init namespace %s es: %+v", namespace, err))
		return stderr.Internal.Errorf("error in init namespace %s es: %s", namespace, err.Error())
	}
	return nil
}

func (s *ArgoEventClient) initEventBus(namespace string) error {
	eb := defaultEventBus(namespace)
	_, err := s.ebLister.EventBus(namespace).Get(eb.Name)
	if err != nil && !errors.IsNotFound(err) {
		return err
	}
	if errors.IsNotFound(err) {
		stdlog.Info(fmt.Sprintf("namespace %s eb %s no exist, create ...", namespace, eb.Name))
		_, err = s.clientset.ArgoprojV1alpha1().EventBus(namespace).Create(context.Background(), eb, metav1.CreateOptions{})
		return err
	}
	return nil
}
func (s *ArgoEventClient) initEventSource(namespace string, projectId string) error {
	es := defaultEventSource(namespace, projectId)
	_, err := s.esLister.EventSources(namespace).Get(es.Name)
	if err != nil && !errors.IsNotFound(err) {
		return err
	}
	if errors.IsNotFound(err) {
		stdlog.Info(fmt.Sprintf("namespace %s es %s no exist, create ...", namespace, es.Name))
		_, err = s.clientset.ArgoprojV1alpha1().EventSources(namespace).Create(context.Background(), es, metav1.CreateOptions{})
		return err
	}
	return nil
}
func (s *ArgoEventClient) initSecret(namespace string) error {
	tarSecret, err := MustGetK8sClient().SecretLister.Secrets(k8s.CurrentNamespaceInCluster()).Get("llmops-secret")
	if err != nil {
		return err
	}
	secret, err := MustGetK8sClient().SecretLister.Secrets(namespace).Get("llmops-secret")
	if err != nil && !errors.IsNotFound(err) {
		return err
	}
	if errors.IsNotFound(err) {
		stdlog.Info(fmt.Sprintf("namespace %s secret %s no exist, create ...", namespace, "llmops-secret"))
		tarSecret.ObjectMeta = metav1.ObjectMeta{
			Name:      tarSecret.Name,
			Namespace: namespace,
		}
		_, err := MustGetK8sClient().Clientset.CoreV1().Secrets(namespace).Create(context.Background(), tarSecret, metav1.CreateOptions{})
		return err
	}
	if len(secret.Data["redis_password"]) == 0 || !bytes.Equal(tarSecret.Data["redis_password"], secret.Data["redis_password"]) {
		stdlog.Info(fmt.Sprintf("namespace %s secret %s redis password is invalid, updating ...", namespace, "llmops-secret"))
		secret.Data = tarSecret.Data
	}
	return nil
}

func defaultEventBus(namespace string) *eventsv1alpha1.EventBus {
	return &eventsv1alpha1.EventBus{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "default",
			Namespace: namespace,
		},
		Spec: eventsv1alpha1.EventBusSpec{
			JetStream: &eventsv1alpha1.JetStreamBus{
				Version:  "2.10.10",
				Replicas: ptr.To(int32(3)),
			},
		},
	}
}

func defaultEventSource(namespace string, projectId string) *eventsv1alpha1.EventSource {
	return &eventsv1alpha1.EventSource{
		ObjectMeta: metav1.ObjectMeta{
			Name:      strings.ToLower(pb.EventType_FileAssets.String()),
			Namespace: namespace,
		},
		Spec: eventsv1alpha1.EventSourceSpec{
			Redis: map[string]eventsv1alpha1.RedisEventSource{
				"message": {
					HostAddress: conf.PipeineConfig.Pipeline.Redis.Addrs,
					Channels: []string{
						fmt.Sprintf("event-channel-%s-%s", k8s.CurrentNamespaceInCluster(), projectId),
					},
					Password: &corev1.SecretKeySelector{
						Key: "redis_password",
						LocalObjectReference: corev1.LocalObjectReference{
							Name: "llmops-secret",
						},
					},
					Namespace: namespace,
					JSONBody:  true,
					DB:        int32(conf.PipeineConfig.Pipeline.Redis.Database),
					Username:  conf.PipeineConfig.Pipeline.Redis.Username,
				},
			},
		},
	}
}

func intArrayToLua(array []int) string {
	sort.Ints(array)

	strs := make([]string, len(array))
	for i, v := range array {
		strs[i] = fmt.Sprintf("%d", v)
	}

	return fmt.Sprintf("{%s}", strings.Join(strs, ", "))
}

func (s *ArgoEventClient) StartPipelineVersion(ctx context.Context, pipeline *model.Pipeline, pipelineVersion *model.PipelineVersion, tenantUid string) (string, error) {
	err := s.InitNamespaceResource(tenantUid, pipeline.ProjectId)
	if err != nil {
		return "", err
	}
	err = checkTaskFlow(pipelineVersion.TaskFlow)
	if err != nil {
		return "", err
	}
	workflow, err := pipelineVersion.ToWorkflow(ctx, pipeline, tenantUid)
	if err != nil {
		return "", err
	}

	tarSensor, err := ToSensor(ctx, pipelineVersion, workflow, tenantUid)
	if err != nil {
		return "", err
	}

	curSensor, err := s.clientset.ArgoprojV1alpha1().Sensors(tenantUid).Get(ctx, genName(pipelineVersion.Id), metav1.GetOptions{})
	if err != nil && !errors.IsNotFound(err) {
		return "", err
	}

	if errors.IsNotFound(err) {
		_, err = s.clientset.ArgoprojV1alpha1().Sensors(tenantUid).Create(ctx, tarSensor, metav1.CreateOptions{})
		if err != nil {
			return "", err
		}
	} else {
		//curSensor.Spec = tarSensor.Spec
		//curSensor.Annotations = tarSensor.Annotations
		//curSensor.Labels = tarSensor.Labels
		tarSensor.ResourceVersion = curSensor.ResourceVersion
		_, err = s.clientset.ArgoprojV1alpha1().Sensors(tenantUid).Update(ctx, tarSensor, metav1.UpdateOptions{})
		if err != nil {
			return "", err
		}
	}

	return pipelineVersion.Id, nil
}

func (s *ArgoEventClient) StopPipelineVersion(ctx context.Context, id string, tenantUid string) (string, error) {
	sensorName := genName(id)
	_, err := s.clientset.ArgoprojV1alpha1().Sensors(tenantUid).Get(ctx, sensorName, metav1.GetOptions{})
	if err != nil && !errors.IsNotFound(err) {
		return "", err
	}

	if errors.IsNotFound(err) {
		stdlog.Warnf("sensor {%s} is not found", sensorName)
		return "", err
	}
	err = s.clientset.ArgoprojV1alpha1().Sensors(tenantUid).Delete(ctx, sensorName, metav1.DeleteOptions{})
	if err != nil {
		return "", err
	}
	return id, nil
}

func (s *ArgoEventClient) ListOnlinePipelineVersionId(ctx context.Context, tenantUid string) (map[string]interface{}, error) {
	list, err := s.clientset.ArgoprojV1alpha1().Sensors(tenantUid).List(ctx, metav1.ListOptions{})
	if err != nil && !errors.IsNotFound(err) {
		return nil, err
	}
	ids := make(map[string]interface{})
	for _, sensor := range list.Items {
		ids[strings.TrimPrefix(sensor.Name, _const.PrefixName)] = struct{}{}
	}
	return ids, nil
}

func genName(id string) string {
	return _const.PrefixName + id
}
func ToSensor(ctx context.Context, version *model.PipelineVersion, workflow *v1alpha1.Workflow, tenantUid string) (*eventsv1alpha1.Sensor, error) {
	taskId := strings.Split(version.EventConfig.FileAssetsParam.TaskTemplateId, "/")[1]
	task, err := MustGetCVClient().GetTaskDetail(ctx, taskId)
	if err != nil {
		return nil, err
	}
	dependencyName := "redis-event"
	workflowResource := eventsv1alpha1.NewK8SResource(workflow)
	var catalogLua string
	if len(task.CatalogParams.CatalogID) > 0 {
		catalogLua = fmt.Sprintf("contains(event.body.systemIds, \"%s\")", task.CatalogParams.CatalogID)
	} else {
		catalogLua = fmt.Sprintf("#event.body.systemIds==0")
	}
	lua := fmt.Sprintf(`
function unordered_arrays_equal(a, b)
  if type(a) ~= "table" or type(b) ~= "table" then
    return false
  end

  if #a ~= #b then
    return false
  end

  local countA = {}
  for _, v in ipairs(a) do
    countA[v] = (countA[v] or 0) + 1
  end

  for _, v in ipairs(b) do
    if not countA[v] then
      return false
    end
    countA[v] = countA[v] - 1
    if countA[v] < 0 then
      return false
    end
  end

  for _, v in pairs(countA) do
    if v ~= 0 then
      return false
    end
  end

  return true
end
function contains(arr, value)
  if type(arr) ~= "table" then return false end

  for _, v in ipairs(arr) do
    if v == value then
      return true
    end
  end
  return false
end
return event.body.type == "file-assets"
  and (event.body.operation == "re-parsed")
  and unordered_arrays_equal(event.body.labelIds, %s)
  and %s
`, intArrayToLua(task.CatalogParams.LabelIDs), catalogLua)
	tarSensor := &eventsv1alpha1.Sensor{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Sensor",
			APIVersion: "argoproj.io/v1alpha1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:        genName(version.Id),
			Namespace:   tenantUid,
			Labels:      workflow.Labels,
			Annotations: workflow.Annotations,
		},
		Spec: eventsv1alpha1.SensorSpec{
			Template: &eventsv1alpha1.Template{
				ServiceAccountName: model.PipelineServiceAccount,
			},
			Dependencies: []eventsv1alpha1.EventDependency{
				{
					Name:            dependencyName,
					EventSourceName: strings.ToLower(version.EventConfig.Type.String()),
					EventName:       "message",
					Filters: &eventsv1alpha1.EventDependencyFilter{
						Script: lua,
					},
				},
			},
			Triggers: []eventsv1alpha1.Trigger{
				{
					Template: &eventsv1alpha1.TriggerTemplate{
						Name: workflow.GenerateName,
						ArgoWorkflow: &eventsv1alpha1.ArgoWorkflowTrigger{
							Operation: eventsv1alpha1.Submit,
							Parameters: []eventsv1alpha1.TriggerParameter{
								{
									Src: &eventsv1alpha1.TriggerParameterSource{
										DependencyName: dependencyName,
										DataKey:        "body.assetId",
									},
									Dest: "spec.arguments.parameters.0.value",
								}, {
									Src: &eventsv1alpha1.TriggerParameterSource{
										DependencyName: dependencyName,
										DataKey:        "body.versionId",
									},
									Dest: "spec.arguments.parameters.1.value",
								},
							},
							Source: &eventsv1alpha1.ArtifactLocation{
								Resource: &workflowResource,
							},
						},
					},
				},
			},
		},
	}

	return tarSensor, nil
}
