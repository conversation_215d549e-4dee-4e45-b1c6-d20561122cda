package client

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/cenkalti/backoff"
	"github.com/minio/minio-go/pkg/credentials"
	"transwarp.io/mlops/mlops-std/stderr"

	minio "github.com/minio/minio-go"
)

type MinioClient struct {
	Client *minio.Client
}

func NewMinioClient(minioEndpoint, accessKey, secretKey string, secure bool, region string, initConnectionTimeout time.Duration) (*MinioClient, error) {
	var minioClient *minio.Client
	var err error
	var operation = func() error {
		minioClient, err = CreateMinioClient(minioEndpoint, accessKey, secretKey, secure, region)
		if err != nil {
			return err
		}
		return nil
	}
	b := backoff.NewExponentialBackOff()
	b.MaxElapsedTime = initConnectionTimeout
	err = backoff.Retry(operation, b)
	if err != nil {
		return nil, err
	}
	return &MinioClient{
		Client: minioClient,
	}, nil
}

func CreateMinioClient(endpoint string,
	accessKey string, secretKey string, secure bool, region string) (*minio.Client, error) {

	cred := createCredentialProvidersChain(endpoint, accessKey, secretKey)
	minioClient, err := minio.NewWithCredentials(endpoint, cred, secure, region)
	if err != nil {
		return nil, stderr.MinioConnectFail.Error(err, "Error while creating minio client: %+v", err)
	}
	return minioClient, nil
}

func createCredentialProvidersChain(endpoint, accessKey, secretKey string) *credentials.Credentials {
	// first try with static api key
	if accessKey != "" && secretKey != "" {
		return credentials.NewStaticV4(accessKey, secretKey, "")
	}
	// otherwise use a chained provider: minioEnv -> awsEnv -> IAM
	providers := []credentials.Provider{
		&credentials.EnvMinio{},
		&credentials.EnvAWS{},
		&credentials.IAM{
			Client: &http.Client{
				Transport: http.DefaultTransport,
			},
		},
	}
	return credentials.New(&credentials.Chain{Providers: providers})
}

func joinHostPort(host, port string) string {
	if port == "" {
		return host
	}
	return fmt.Sprintf("%s:%s", host, port)
}

func (c *MinioClient) PutObject(bucketName, objectName string, reader io.Reader, objectSize int64, opts minio.PutObjectOptions) (n int64, err error) {
	return c.Client.PutObject(bucketName, objectName, reader, objectSize, opts)
}

func (c *MinioClient) GetObject(bucketName, objectName string, opts minio.GetObjectOptions) (io.Reader, error) {
	return c.Client.GetObject(bucketName, objectName, opts)
}

func (c *MinioClient) DeleteObject(bucketName, objectName string) error {
	return c.Client.RemoveObject(bucketName, objectName)
}

func (c *MinioClient) StatObject(bucketName, objectName string, opts minio.StatObjectOptions) (minio.ObjectInfo, error) {
	return c.Client.StatObject(bucketName, objectName, opts)
}

func (c *MinioClient) GetObjectTail(bucketName, objectName string, maxSize int64) (io.Reader, error) {
	info, err := c.StatObject(bucketName, objectName, minio.StatObjectOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get object info: %w", err)
	}

	// 计算从文件末尾开始读取的偏移量
	objectSize := info.Size
	var start int64
	if objectSize <= maxSize {
		// 如果文件小于或等于 maxSize（2MB），可以直接从头到尾读取
		start = 0
	} else {
		// 否则，从文件末尾开始读取最多 maxSize 字节数据
		start = objectSize - maxSize
	}
	opts := minio.GetObjectOptions{}
	opts.SetRange(start, objectSize-1)
	// 获取对象流
	object, err := c.GetObject(bucketName, objectName, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to get object: %w", err)
	}
	return object, nil
}

const (
	MinioClientName = "minio"
)

func init() {
	if err := GlobalManager.Register(NewMinioClientFactory()); err != nil {
		panic(fmt.Sprintf("failed to register %s client factory: %w", MinioClientName, err))
	}
}

type MinioClientWrapper struct {
	client *MinioClient

	options *MinioClientOptions
}

type MinioClientFactory struct{}

func NewMinioClientFactory() ClientFactory {
	return &MinioClientFactory{}
}

func (f *MinioClientFactory) Name() string {
	return MinioClientName
}

func (f *MinioClientFactory) Create(options interface{}) (Client, error) {
	ops, ok := options.(*MinioClientOptions)
	if !ok {
		return nil, fmt.Errorf("invalid config type for minio client")
	}

	return &MinioClientWrapper{
		options: ops,
	}, nil
}

func (w *MinioClientWrapper) Name() string {
	return MinioClientName
}

func (w *MinioClientWrapper) Dependencies() []string {
	return []string{}
}

func (w *MinioClientWrapper) Initialize(ctx context.Context) error {
	client, err := NewMinioClient(w.options.MinioEndpoint,
		w.options.AccessKey,
		w.options.SecretKey,
		w.options.Secure,
		w.options.Gegion,
		w.options.InitConnectionTimeout)
	if err != nil {
		return fmt.Errorf("failed to create minio client: %w", err)
	}

	w.client = client
	return nil
}

func (w *MinioClientWrapper) Start(ctx context.Context) error {
	return nil
}

func (w *MinioClientWrapper) Stop(ctx context.Context) error {
	return nil
}

func (w *MinioClientWrapper) HealthCheck(ctx context.Context) error {
	if w.client == nil {
		return fmt.Errorf("minio client not initialized")
	}

	return nil
}

func (w *MinioClientWrapper) GetClient() *MinioClient {
	return w.client
}
