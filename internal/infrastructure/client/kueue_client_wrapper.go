package client

import (
	"context"
	"fmt"
)

const (
	KueueClientName = "kueue"
)

type KueueClientWrapper struct {
	client *KueueClient

	options *KueueClientOptions
}

type KueueClientFactory struct{}

func NewKueueClientFactory() ClientFactory {
	return &KueueClientFactory{}
}

func (f *KueueClientFactory) Name() string {
	return KueueClientName
}

func (f *KueueClientFactory) Create(options interface{}) (Client, error) {
	ops, ok := options.(*KueueClientOptions)
	if !ok {
		return nil, fmt.Errorf("invalid config type for kueue client")
	}

	return &KueueClientWrapper{
		options: ops,
	}, nil
}

func (w *KueueClientWrapper) Name() string {
	return KueueClientName
}

func (w *KueueClientWrapper) Dependencies() []string {
	return []string{}
}

func (w *KueueClientWrapper) Initialize(ctx context.Context) error {
	client, err := NewKueueClient()
	if err != nil {
		return fmt.Errorf("failed to create kueue client: %w", err)
	}

	w.client = client
	return nil
}

func (w *KueueClientWrapper) Start(ctx context.Context) error {
	return nil
}

func (w *KueueClientWrapper) Stop(ctx context.Context) error {
	return nil
}

func (w *KueueClientWrapper) HealthCheck(ctx context.Context) error {
	if w.client == nil {
		return fmt.Errorf("kueue client not initialized")
	}

	return nil
}

func (w *KueueClientWrapper) GetClient() *KueueClient {
	return w.client
}
