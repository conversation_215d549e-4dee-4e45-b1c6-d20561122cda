package client

import (
	"context"
	"encoding/json"
	"strings"
	"time"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/informers"
	"k8s.io/client-go/kubernetes"
	applisterv1 "k8s.io/client-go/listers/apps/v1"
	corelisterv1 "k8s.io/client-go/listers/core/v1"
	"k8s.io/client-go/tools/cache"
	"sigs.k8s.io/yaml"

	"bytes"
	"fmt"
	"io"

	v1 "k8s.io/api/apps/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/fields"

	util "transwarp.io/mlops/pipeline/internal/util"
)

const (
	Pod      = "Pod"
	Workflow = "Workflow"
)

const (
	K8sClientName = "k8s"
)

type K8sClientset struct {
	Clientset *kubernetes.Clientset

	NamespaceLister   corelisterv1.NamespaceLister
	PodLister         corelisterv1.PodLister
	SecretLister      corelisterv1.SecretLister
	DeployLister      applisterv1.DeploymentLister
	StatefulsetLister applisterv1.StatefulSetLister
	ReplicaSetLister  applisterv1.ReplicaSetLister
	SvcLister         corelisterv1.ServiceLister
	EventLister       corelisterv1.EventLister
	ConfigMapLister   corelisterv1.ConfigMapLister
	PodInformer       cache.SharedIndexInformer

	sharedInformerFactory informers.SharedInformerFactory
}

func NewK8sClient() (*K8sClientset, error) {
	config, err := util.GetKubeConfig()
	if err != nil {
		return nil, err
	}
	client := kubernetes.NewForConfigOrDie(config)

	c := &K8sClientset{
		Clientset:             client,
		sharedInformerFactory: informers.NewSharedInformerFactory(client, time.Minute*10),
	}

	c.NamespaceLister = c.sharedInformerFactory.Core().V1().Namespaces().Lister()
	c.PodInformer = c.sharedInformerFactory.Core().V1().Pods().Informer()
	c.PodLister = c.sharedInformerFactory.Core().V1().Pods().Lister()
	c.SecretLister = c.sharedInformerFactory.Core().V1().Secrets().Lister()
	c.SvcLister = c.sharedInformerFactory.Core().V1().Services().Lister()
	c.DeployLister = c.sharedInformerFactory.Apps().V1().Deployments().Lister()
	c.StatefulsetLister = c.sharedInformerFactory.Apps().V1().StatefulSets().Lister()
	c.ReplicaSetLister = c.sharedInformerFactory.Apps().V1().ReplicaSets().Lister()
	c.EventLister = c.sharedInformerFactory.Core().V1().Events().Lister()
	c.ConfigMapLister = c.sharedInformerFactory.Core().V1().ConfigMaps().Lister()

	return c, nil
}

func (k *K8sClientset) Start(ctx context.Context) error {
	stopCh := make(chan struct{})
	go func() {
		<-ctx.Done()
		close(stopCh)
	}()

	// Start informers
	k.sharedInformerFactory.Start(stopCh)

	return nil
}

func (k *K8sClientset) AddWatchHandler(handler cache.ResourceEventHandler) {
	k.PodInformer.AddEventHandler(handler)
}

func (k *K8sClientset) GetNamespace(ctx context.Context, name string) (*corev1.Namespace, error) {
	return k.NamespaceLister.Get(name)
}

func (k *K8sClientset) ListPods(ctx context.Context, namespace string) ([]*corev1.Pod, error) {
	pods, err := k.PodLister.Pods(namespace).List(labels.Everything())
	if err != nil {
		return nil, err
	}
	return pods, nil
}

func (k *K8sClientset) GetPod(ctx context.Context, namespace string, podName string) (*corev1.Pod, error) {
	pod, err := k.PodLister.Pods(namespace).Get(podName)
	if err != nil {
		return nil, err
	}
	return pod, nil
}

func (k *K8sClientset) GetReplicaSet(ctx context.Context, namespace string, name string) (*appsv1.ReplicaSet, error) {
	replicaSet, err := k.ReplicaSetLister.ReplicaSets(namespace).Get(name)
	if err != nil {
		return nil, err
	}
	return replicaSet, nil
}

func (k *K8sClientset) GetDeployment(ctx context.Context, namespace string, name string) (*appsv1.Deployment, error) {
	deployment, err := k.DeployLister.Deployments(namespace).Get(name)
	if err != nil {
		return nil, err
	}
	return deployment, nil
}

func (k *K8sClientset) GetStatefulSet(ctx context.Context, namespace string, name string) (*appsv1.StatefulSet, error) {
	statefulset, err := k.StatefulsetLister.StatefulSets(namespace).Get(name)
	if err != nil {
		return nil, err
	}
	return statefulset, nil
}

func (k *K8sClientset) GetPodInfo(ctx context.Context, namespace string, podName string) ([]byte, error) {
	pod, err := k.PodLister.Pods(namespace).Get(podName)
	if err != nil {
		return nil, err
	}
	return pod2Yaml(pod)
}

func (k *K8sClientset) GetConfigMapInfo(ctx context.Context, namespace string, cmName string) (*corev1.ConfigMap, error) {
	cm, err := k.ConfigMapLister.ConfigMaps(namespace).Get(cmName)
	if err != nil {
		return nil, err
	}
	return cm, nil
}

func pod2Yaml(pod *corev1.Pod) ([]byte, error) {
	json, _ := json.Marshal(pod)
	yaml, _ := yaml.JSONToYAML(json)
	return yaml, nil
}

func (k *K8sClientset) GetPodEvents(ctx context.Context, namespace, runId, podName string) ([]*corev1.Event, error) {
	res := make([]*corev1.Event, 0)
	event, err := k.EventLister.Events(namespace).List(labels.Everything())
	if err != nil {
		return nil, err
	}
	for _, e := range event {
		if !checkEventKind(e, runId, podName) {
			continue
		}
		res = append(res, e)
	}
	return res, nil
}

func checkEventKind(event *corev1.Event, runId, podName string) bool {
	if event == nil {
		return false
	}

	// workflow
	if len(runId) != 0 && Workflow == event.InvolvedObject.Kind && strings.Contains(event.InvolvedObject.Name, runId) {
		return true
	}

	// pod
	if len(podName) != 0 && Pod == event.InvolvedObject.Kind && strings.Contains(event.InvolvedObject.Name, podName) {
		return true
	}

	// pod workflow
	if len(runId) != 0 && Pod == event.InvolvedObject.Kind && strings.Contains(event.InvolvedObject.Name, runId) {
		return true
	}

	return false
}

func (k *K8sClientset) GetServiceByName(serviceName, namespace string) (*corev1.Service, error) {
	svc, err := k.SvcLister.Services(namespace).Get(serviceName)
	if err != nil {
		return nil, err
	}
	return svc, nil
}

func event2Yaml(event []*corev1.Event) ([]byte, error) {
	json, _ := json.Marshal(event)
	yaml, _ := yaml.JSONToYAML(json)
	return yaml, nil
}

func (k *K8sClientset) GetPodEventsYaml(ctx context.Context, namespace string, podName string) ([]byte, error) {
	event, err := k.Clientset.CoreV1().Events(namespace).List(ctx, metav1.ListOptions{
		FieldSelector: fmt.Sprintf("involvedObject.name=%s", podName),
	})
	if err != nil {
		return nil, err
	}
	events := make([]*corev1.Event, len(event.Items))
	for i, e := range event.Items {
		events[i] = &e
	}
	return event2Yaml(events)
}

func (k *K8sClientset) GetPodEventsByLabels(ctx context.Context, namespace string, labelsMap map[string]string) (*corev1.EventList, error) {
	event, err := k.Clientset.CoreV1().Events(namespace).List(ctx, metav1.ListOptions{
		FieldSelector: fields.Everything().String(),
		LabelSelector: labels.SelectorFromSet(labelsMap).String(),
	})
	if err != nil {
		return nil, err
	}
	return event, nil
}

type GetLogsOptions struct {
	TailLines  int64
	LimitBytes int64
}

func (k *K8sClientset) GetContainerLogs(ctx context.Context, namespace string, podName string, containerName string, ops *GetLogsOptions) (io.Reader, error) {
	podLogOps := &corev1.PodLogOptions{
		Container: containerName,
	}
	if ops != nil {
		if ops.LimitBytes > 0 {
			podLogOps.LimitBytes = &ops.LimitBytes
		}
		if ops.TailLines > 0 {
			podLogOps.TailLines = &ops.TailLines
		}
	}
	req := k.Clientset.CoreV1().Pods(namespace).GetLogs(podName, podLogOps)

	stream, err := req.Stream(ctx)
	if err != nil {
		return nil, err
	}
	defer stream.Close()

	buf := new(bytes.Buffer)
	_, err = io.Copy(buf, stream)
	if err != nil {
		return nil, err
	}
	return buf, nil
}

func (k *K8sClientset) GetDeploymentByDeploymentName(ctx context.Context, namespace string, name string) (*v1.Deployment, error) {
	req, err := k.Clientset.AppsV1().Deployments(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return nil, err
	}
	return req, nil
}

func (k *K8sClientset) GetPodsByDeploymentName(ctx context.Context, namespace string, deploymentName string) ([]corev1.Pod, error) {
	pods, err := k.Clientset.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{})
	var podList []corev1.Pod
	for _, pod := range pods.Items {
		if strings.Contains(pod.Name, deploymentName) {
			podList = append(podList, pod)
		}
	}
	if err != nil {
		return nil, err
	}
	return podList, nil
}

func (k *K8sClientset) ListSeldonPods(ctx context.Context, namespace string) ([]corev1.Pod, error) {
	pods, err := k.Clientset.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{LabelSelector: "serving"})
	if err != nil {
		return nil, err
	}
	return pods.Items, nil
}
