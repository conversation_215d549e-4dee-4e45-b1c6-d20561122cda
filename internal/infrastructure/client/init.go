package client

import (
	"context"
	"time"

	std_client "transwarp.io/mlops/mlops-std/client"
	"transwarp.io/mlops/mlops-std/stdlog"
	config "transwarp.io/mlops/pipeline/conf"
)

var (
	CasCli *std_client.CasClient
)

func Init() error {
	CasCli = &std_client.CasClient{}
	CasCli.Init(config.PipeineConfig.MLOPS.Cas.Host, config.PipeineConfig.MLOPS.Cas.Port)

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	manager := GetGlobalManager()

	// 准备配置映射
	configs := map[string]interface{}{
		"pipeline":     config.Pipeline,
		"k8s":          config.<PERSON>be,
		"k8s-informer": config.Kube,
		"kueue":        config.<PERSON>be,
		"minio":        config.External.Minio,
		"argo-event":   config.<PERSON><PERSON>,
		"cv":           config.External.Cvat,
	}

	// 初始化所有客户端
	if err := manager.Initialize(ctx, configs); err != nil {
		return err
	}

	// 启动所有客户端
	if err := manager.Start(ctx); err != nil {
		return err
	}

	stdlog.Info("All clients initialized and started successfully")
	return nil
}

func Shutdown() error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	manager := GetGlobalManager()
	return manager.Stop(ctx)
}

func HealthCheck() map[string]error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	manager := GetGlobalManager()
	return manager.HealthCheck(ctx)
}
