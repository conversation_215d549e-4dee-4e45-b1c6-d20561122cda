package client

import (
	"context"
	"time"

	std_client "transwarp.io/mlops/mlops-std/client"
	"transwarp.io/mlops/mlops-std/stdlog"
	config "transwarp.io/mlops/pipeline/conf"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

var (
	CasCli *std_client.CasClient
)

func Init() error {
	CasCli = &std_client.CasClient{}
	CasCli.Init(config.PipeineConfig.MLOPS.Cas.Host, config.PipeineConfig.MLOPS.Cas.Port)

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	configs := map[string]interface{}{
		PipelineClientName:  PipelineClientOptions{},
		K8sClientName:       K8sClientOptions{},
		KueueClientName:     KueueClientOptions{},
		ArgoEventClientName: ArgoEventClientOptions{},
		CVClientName:        HttpClientOptions{},
		CASClientName:       HttpClientOptions{},
	}

	if err := GlobalManager.Initialize(ctx, configs); err != nil {
		return err
	}
	if err := GlobalManager.Start(ctx); err != nil {
		return err
	}

	stdlog.Info("All clients initialized and started successfully")
	return nil
}

func Shutdown() error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	return GlobalManager.Stop(ctx)
}

func HealthCheck() map[string]error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	return GlobalManager.HealthCheck(ctx)
}

func GetPipelineClient() *PipelineClient {
	var client *PipelineClient
	err := GlobalManager.GetTyped(PipelineClientName, &client)
	if err != nil {
		zlog.SugarErrorf("failed to get pipeline client: %w", err)
		return nil
	}
	return client
}

func GetK8sClient() *K8sClientset {
	var client *K8sClientset
	err := GlobalManager.GetTyped(K8sClientName, &client)
	if err != nil {
		zlog.SugarErrorf("failed to get k8s client: %w", err)
		return nil
	}
	return client
}

func GetCVClient() *CVClient {
	var client *CVClient
	err := GlobalManager.GetTyped(CVClientName, &client)
	if err != nil {
		zlog.SugarErrorf("failed to get cv client: %w", err)
		return nil
	}
	return client
}

func GetArgoEventClient() *ArgoEventClient {
	var client *ArgoEventClient
	err := GlobalManager.GetTyped(ArgoEventClientName, &client)
	if err != nil {
		zlog.SugarErrorf("failed to get argo event client: %w", err)
		return nil
	}
	return client
}
