package client

import (
	"context"

	"transwarp.io/mlops/mlops-std/stdlog"
)

var (
	pipelineClient  *PipelineClient
	k8sClient       *K8sClientset
	kueueClient     *KueueClient
	argoEventClient *ArgoEventClient
	cvClient        *CVHttpClient
	casClient       *CASHttpClient
)

func init() {
	var err error

	if pipelineClient, err = NewPipelineClient(); err != nil {
		panic(err)
	}
	if k8sClient, err = NewK8sClient(); err != nil {
		panic(err)
	}
	k8sClient.Start(context.Background())

	if kueueClient, err = NewKueueClient(); err != nil {
		panic(err)
	}

	if argoEventClient, err = NewArgoEventClient(); err != nil {
		panic(err)
	}

	if cvClient, err = NewCVHttpClient(); err != nil {
		panic(err)
	}

	if casClient, err = NewCASHttpClient(); err != nil {
		panic(err)
	}

	stdlog.Info("All clients initialized and started successfully")
}

func MustGetPipelineClient() *PipelineClient {
	if pipelineClient == nil {
		panic("pipeline client not initialized")
	}
	return pipelineClient
}

func MustGetK8sClient() *K8sClientset {
	if k8sClient == nil {
		panic("k8s client not initialized")
	}
	return k8sClient
}

func MustGetCVClient() *CVHttpClient {
	if cvClient == nil {
		panic("cv client not initialized")
	}
	return cvClient
}

func MustGetArgoEventClient() *ArgoEventClient {
	if argoEventClient == nil {
		panic("argo event client not initialized")
	}
	return argoEventClient
}

func MustGetCASClient() *CASHttpClient {
	if casClient == nil {
		panic("cas client not initialized")
	}
	return casClient
}

func MustGetKueueClient() *KueueClient {
	if kueueClient == nil {
		panic("kueue client not initialized")
	}
	return kueueClient
}
