package client

import "context"

var (
	pipelineClient  *PipelineClient
	k8sClient       *K8sClientset
	kueueClient     *KueueClient
	argoEventClient *ArgoEventClient
	cvClient        *CVHttpClient
	casClient       *CASHttpClient
	lwsClient       *LeaderWorkerSetClient
)

func MustGetPipelineClient() *PipelineClient {
	if pipelineClient == nil {
		if cli, err := NewPipelineClient(); err != nil {
			panic(err)
		} else {
			pipelineClient = cli
		}
	}
	return pipelineClient
}

func MustGetK8sClient() *K8sClientset {
	if k8sClient == nil {
		if cli, err := NewK8sClient(); err != nil {
			panic(err)
		} else {
			k8sClient = cli
			k8sClient.Start(context.Background())
		}
	}
	return k8sClient
}

func MustGetCVClient() *CVHttpClient {
	if cvClient == nil {
		if cli, err := NewCVHttpClient(); err != nil {
			panic(err)
		} else {
			cvClient = cli
		}
	}
	return cvClient
}

func MustGetArgoEventClient() *ArgoEventClient {
	if argoEventClient == nil {
		if cli, err := NewArgoEventClient(); err != nil {
			panic(err)
		} else {
			argoEventClient = cli
		}
	}
	return argoEventClient
}

func MustGetCASClient() *CASHttpClient {
	if casClient == nil {
		if cli, err := NewCASHttpClient(); err != nil {
			panic(err)
		} else {
			casClient = cli
		}
	}
	return casClient
}

func MustGetKueueClient() *KueueClient {
	if kueueClient == nil {
		if cli, err := NewKueueClient(); err != nil {
			panic(err)
		} else {
			kueueClient = cli
		}
	}
	return kueueClient
}

func MustGetLeaderWorkerSetClient() *LeaderWorkerSetClient {
	if lwsClient == nil {
		if cli, err := NewLeaderWorkerSetClient(); err != nil {
			panic(err)
		} else {
			lwsClient = cli
		}
	}
	return lwsClient
}
