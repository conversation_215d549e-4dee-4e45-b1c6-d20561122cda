package client

import (
	"context"
	"fmt"
)

const (
	ArgoEventClientName = "argo-event"
)

type ArgoEventClientWrapper struct {
	client *ArgoEventClient

	options *ArgoEventClientOptions
}

type ArgoEventClientFactory struct{}

func NewArgoEventClientFactory() ClientFactory {
	return &ArgoEventClientFactory{}
}

func (f *ArgoEventClientFactory) Name() string {
	return ArgoEventClientName
}

func (f *ArgoEventClientFactory) Create(options interface{}) (Client, error) {
	ops, ok := options.(*ArgoEventClientOptions)
	if !ok {
		return nil, fmt.Errorf("invalid config type for argo event client")
	}

	return &ArgoEventClientWrapper{
		options: ops,
	}, nil
}

func (w *ArgoEventClientWrapper) Name() string {
	return ArgoEventClientName
}

func (w *ArgoEventClientWrapper) Dependencies() []string {
	return []string{}
}

func (w *ArgoEventClientWrapper) Initialize(ctx context.Context) error {
	client := &ArgoEventClient{}
	err := client.Init()
	if err != nil {
		return fmt.Errorf("failed to create argo event client: %w", err)
	}

	w.client = client
	return nil
}

func (w *ArgoEventClientWrapper) Start(ctx context.Context) error {
	return nil
}

func (w *ArgoEventClientWrapper) Stop(ctx context.Context) error {
	return nil
}

func (w *ArgoEventClientWrapper) HealthCheck(ctx context.Context) error {
	if w.client == nil {
		return fmt.Errorf("argo event client not initialized")
	}

	return nil
}

func (w *ArgoEventClientWrapper) GetClient() *ArgoEventClient {
	return w.client
}
