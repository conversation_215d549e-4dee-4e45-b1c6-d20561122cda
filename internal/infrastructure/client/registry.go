package client

import (
	"fmt"

	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

func registerBuiltinFactories(manager ClientManager) {
	factories := []ClientFactory{
		NewPipelineClientFactory(),
		NewK8sClientFactory(),
		NewKueueClientFactory(),
		NewMinioClientFactory(),
		NewArgoEventClientFactory(),
		NewCVClientFactory(),
		// add new client factory
	}

	for _, factory := range factories {
		if err := manager.Register(factory); err != nil {
			zlog.SugarErrorf("failed to register client factory %s: %w", factory.Name(), err)
			panic(fmt.Sprintf("failed to register client factory %s: %w", factory.Name(), err))
		}
	}
}

func GetPipelineClient() *PipelineClient {
	var client *PipelineClient
	err := GetGlobalManager().GetTyped(PipelineClientName, &client)
	if err != nil {
		zlog.SugarErrorf("failed to get pipeline client: %w", err)
		return nil
	}
	return client
}

func GetK8sClient() *K8sClientset {
	var client *K8sClientset
	err := GetGlobalManager().GetTyped(K8sClientName, &client)
	if err != nil {
		zlog.SugarErrorf("failed to get k8s client: %w", err)
		return nil
	}
	return client
}

func GetCVClient() *CVClient {
	var client *CVClient
	err := GetGlobalManager().GetTyped(CVClientName, &client)
	if err != nil {
		zlog.SugarErrorf("failed to get cv client: %w", err)
		return nil
	}
	return client
}

func GetArgoEventClient() *ArgoEventClient {
	var client *ArgoEventClient
	err := GetGlobalManager().GetTyped(ArgoEventClientName, &client)
	if err != nil {
		zlog.SugarErrorf("failed to get argo event client: %w", err)
		return nil
	}
	return client
}
