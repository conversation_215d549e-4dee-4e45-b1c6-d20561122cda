package util

const (
	LabelKeyCacheEnabled = "pipelines.kubeflow.org/cache_enabled"

	// const belong llmops product
	LabelLLMOpsNameKey   = "llmops.transwarp.io/name"
	LabelLLMOpsNameValue = "llmops"
	// value is llmops installed namespace
	LabelLLMOpsInstanceKey = "llmops.transwarp.io/instance"

	LabelLLMOpsPartOfKey = "llmops.transwarp.io/part-of"

	LabelLLMOpsComponentKey   = "llmops.transwarp.io/component"
	LabelLLMOpsComponentValue = "pipeline"

	// value is llmops installed namespace
	LabelLLMOpsManagedByKey = "llmops.transwarp.io/managed-by"

	LabelLLMOpsVersionKey = "llmops.transwarp.io/version"

	// task
	LabelTaskManagedByKey = "task.transwarp.io/managed-by"
)
