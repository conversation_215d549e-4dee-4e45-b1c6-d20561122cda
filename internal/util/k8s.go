package util

import (
	"os"
	"path/filepath"
	"strings"

	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
)

func GetKubeConfig() (*rest.Config, error) {
	if strings.ToUpper(os.Getenv("LOCAL_DEBUG_MODEL")) == "TRUE" {
		kubeConfigPath := filepath.Join(os.<PERSON>env("MLOPS_CONF_DIR"), "kubeconfig")
		return clientcmd.BuildConfigFromFlags("", kubeConfigPath)
	}
	return rest.InClusterConfig()
}
