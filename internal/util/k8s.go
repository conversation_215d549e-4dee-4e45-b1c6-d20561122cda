package util

import (
	"os"
	"path/filepath"
	"strings"

	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
)

func GetKubeConfig() (*rest.Config, error) {
	// os.Setenv("LOCAL_DEBUG_MODEL", "TRUE")
	// os.Setenv("MLOPS_CONF_DIR", "/home/<USER>/workspace/sophon/go/transwarp.io/aip/mlops-pipeline/conf")
	if strings.ToUpper(os.Getenv("LOCAL_DEBUG_MODEL")) == "TRUE" {
		kubeConfigPath := filepath.Join(os.Getenv("MLOPS_CONF_DIR"), "kubeconfig")
		return clientcmd.BuildConfigFromFlags("", kubeConfigPath)
	}
	return rest.InClusterConfig()
}

func GetCurrentNamespace() string {
	name := os.Getenv("CURRENT_NAMESPACE")
	if name != "" {
		return name
	}
	return "llmops"
}

func GetCurrentPodName() string {
	if name := os.Getenv("POD_NAME"); name != "" {
		return name
	} else if name, err := os.Hostname(); err == nil && name != "" {
		return name
	}
	return "pipeline"
}
