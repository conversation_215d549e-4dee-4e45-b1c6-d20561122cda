package util

import (
	"os"
	"path/filepath"
	"strings"
	"sync"

	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

var currentNamespace = sync.OnceValues(func() (string, error) {
	if e := os.Getenv("CURRENT_NAMESPACE"); e != "" {
		return e, nil
	}
	if e := os.Getenv("POD_NAMESPACE"); e != "" {
		return e, nil
	}
	nsFile := "/run/secrets/kubernetes.io/serviceaccount/namespace"
	ns, err := os.ReadFile(nsFile)
	if err != nil {
		zlog.SugarErrorf("Get ns from file %s failed: %v", nsFile, err)
		return "", err
	}
	return strings.TrimSuffix(string(ns), "\n"), err
})

var currentPodName = sync.OnceValues(func() (string, error) {
	if name := os.Getenv("POD_NAME"); name != "" {
		return name, nil
	} else if name, err := os.Hostname(); err == nil && name != "" {
		return name, nil
	}
	return "", nil
})

func GetKubeConfig() (*rest.Config, error) {
	// os.Setenv("LOCAL_DEBUG_MODEL", "TRUE")
	// os.Setenv("MLOPS_CONF_DIR", "/home/<USER>/workspace/sophon/go/transwarp.io/aip/mlops-pipeline/conf")
	if strings.ToUpper(os.Getenv("LOCAL_DEBUG_MODEL")) == "TRUE" {
		kubeConfigPath := filepath.Join(os.Getenv("MLOPS_CONF_DIR"), "kubeconfig")
		return clientcmd.BuildConfigFromFlags("", kubeConfigPath)
	}
	return rest.InClusterConfig()
}

func GetCurrentNamespace() string {
	ns, err := currentNamespace()
	if err != nil {
		zlog.SugarErrorf("Get current namespace failed: %v", err)
		// return default value
		return "llmops"
	}
	return ns
}

func GetCurrentPodName() string {
	name, err := currentPodName()
	if err != nil {
		zlog.SugarErrorf("Get current pod name failed: %v", err)
		return "pipeline"
	}
	return name
}
