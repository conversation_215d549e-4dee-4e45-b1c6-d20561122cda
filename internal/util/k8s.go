package util

import (
	"os"
	"path/filepath"
	"strings"
	"sync"

	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

var (
	currentNamespace string
	once             sync.Once
)

func GetKubeConfig() (*rest.Config, error) {
	// os.Setenv("LOCAL_DEBUG_MODEL", "TRUE")
	// os.Setenv("MLOPS_CONF_DIR", "/home/<USER>/workspace/sophon/go/transwarp.io/aip/mlops-pipeline/conf")
	if strings.ToUpper(os.Getenv("LOCAL_DEBUG_MODEL")) == "TRUE" {
		kubeConfigPath := filepath.Join(os.Getenv("MLOPS_CONF_DIR"), "kubeconfig")
		return clientcmd.BuildConfigFromFlags("", kubeConfigPath)
	}
	return rest.InClusterConfig()
}

func GetCurrentNamespace() string {
	once.Do(func() {
		if currentNamespace != "" {
			return
		}

		if os.Getenv("CURRENT_NAMESPACE") != "" {
			currentNamespace = os.Getenv("CURRENT_NAMESPACE")
			return
		}

		f := "/run/secrets/kubernetes.io/serviceaccount/namespace"
		ns, err := os.ReadFile(f)
		if err != nil {
			zlog.Errorf("Get ns from file %s failed: %v", f, err)
			return
		}
		currentNamespace = strings.TrimSuffix(string(ns), "\n")
	})

	return currentNamespace
}
