apiVersion: batch/v1
kind: Job
metadata:
  annotations:
    batch.kubernetes.io/job-tracking: ""
    creator: thinger
    description: test sameple job
    id: xxxxxxxx
    name: sample-job
    project-id: demo
    project-name: default demo
    tenant-id: test
    tenant-name: test
    type: train
  creationTimestamp: "2025-07-07T00:56:24Z"
  generation: 2
  labels:
    kueue.x-k8s.io/queue-name: task
  name: sample-job
  namespace: test
  resourceVersion: "29720191"
  uid: 0d6b6c82-1662-433b-bc71-9ea701a75e50
spec:
  backoffLimit: 6
  completionMode: NonIndexed
  completions: 2
  parallelism: 2
  selector:
    matchLabels:
      batch.kubernetes.io/controller-uid: 0d6b6c82-1662-433b-bc71-9ea701a75e50
  suspend: false
  template:
    metadata:
      creationTimestamp: null
      labels:
        batch.kubernetes.io/controller-uid: 0d6b6c82-1662-433b-bc71-9ea701a75e50
        batch.kubernetes.io/job-name: sample-job
        controller-uid: 0d6b6c82-1662-433b-bc71-9ea701a75e50
        job-name: sample-job
    spec:
      containers:
      - args:
        - -c
        - sleep 60
        command:
        - /bin/sh
        image: *************:5000/aip/deps/nginx:llm-1.4.3
        imagePullPolicy: IfNotPresent
        name: dummy-job
        resources:
          requests:
            cpu: "1"
            memory: 200Mi
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Never
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
status:
  completionTime: "2025-07-07T00:57:28Z"
  conditions:
  - lastProbeTime: "2025-07-07T00:56:25Z"
    lastTransitionTime: "2025-07-07T00:56:25Z"
    message: Job resumed
    reason: JobResumed
    status: "False"
    type: Suspended
  - lastProbeTime: "2025-07-07T00:57:28Z"
    lastTransitionTime: "2025-07-07T00:57:28Z"
    status: "True"
    type: Complete
  ready: 0
  startTime: "2025-07-07T00:56:25Z"
  succeeded: 2
  uncountedTerminatedPods: {}