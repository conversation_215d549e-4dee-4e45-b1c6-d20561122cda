package util

import (
	"context"
	"errors"
	"os"
	"path/filepath"
	"strings"

	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"

	"k8s.io/client-go/dynamic"
	kueuev1beta1 "sigs.k8s.io/kueue/apis/kueue/v1beta1"

	"transwarp.io/mlops/pipeline/internal/core/task/model"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

var AnnotationKeys = struct {
	Creator     string
	Description string
	ID          string
	Name        string
	ProjectID   string
	ProjectName string
	TenantID    string
	TenantName  string
	Type        string
}{
	Creator:     "creator",
	Description: "description",
	ID:          "id",
	Name:        "name",
	ProjectID:   "project-id",
	ProjectName: "project-name",
	TenantID:    "tenant-id",
	TenantName:  "tenant-name",
	Type:        "type",
}

var extractor Extractor

func init() {
	var kubeConfigPath string
	var config *rest.Config
	var err error
	if strings.ToUpper(os.Getenv("LOCAL_DEBUG_MODEL")) == "TRUE" {
		kubeConfigPath = filepath.Join(os.Getenv("MLOPS_CONF_DIR"), "kubeconfig")
	}
	config, err = clientcmd.BuildConfigFromFlags("", kubeConfigPath)
	if err != nil {
		zlog.SugarErrorf("error while building kueue client config: %v", err)
	}

	dynamicClient, err := dynamic.NewForConfig(config)
	if err != nil {
		zlog.SugarErrorf("error while creating dynamic client: %v", err)
	}
	extractor = NewExtractor(dynamicClient)
}

// TODO
func FromKueueWorkload(kw *kueuev1beta1.Workload) (*model.Task, error) {
	if kw == nil {
		return nil, errors.New("kueue workload is nil")
	}

	annotations, err := extractor.ExtractAnnotations(context.Background(), kw)
	if err != nil {
		return nil, err
	}

	taskType := parseTaskType(annotations)

	status := parseWorkloadStatus(kw)

	return &model.Task{
		ID:          string(kw.OwnerReferences[0].UID),
		Name:        kw.OwnerReferences[0].Name,
		Type:        taskType,
		Priority:    model.TaskPriority(*kw.Spec.Priority),
		Creator:     annotations[AnnotationKeys.Creator],
		CreateAt:    kw.CreationTimestamp.Time,
		UpdateAt:    nil,
		Description: kw.Annotations[AnnotationKeys.Description],

		TenantID:   annotations[AnnotationKeys.TenantID],
		TenantName: annotations[AnnotationKeys.TenantName],

		ProjectName: annotations[AnnotationKeys.ProjectName],
		ProjectID:   annotations[AnnotationKeys.ProjectID],

		QueueName:        string(kw.Spec.QueueName),
		ClusterQueueName: string(kw.Status.Admission.ClusterQueue),

		Resources: nil,
		Status:    &status,

		Namespace: kw.Namespace,
	}, nil
}

func parseTaskType(labels map[string]string) model.TaskType {
	if typeStr, ok := labels[AnnotationKeys.Type]; ok {
		return model.TaskType(typeStr)
	}
	return model.TaskTypeCustom
}

// TODO
func parseWorkloadStatus(kw *kueuev1beta1.Workload) model.TaskStatus {
	// 检查是否被暂停
	if kw.Spec.Active != nil && !*kw.Spec.Active {
		return model.StatusSuspended
	}

	// 检查准入状态
	if kw.Status.Admission == nil {
		return model.StatusPending
	}

	return model.StatusPending
}
