package util

import (
	"context"
	"fmt"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	kueuev1beta1 "sigs.k8s.io/kueue/apis/kueue/v1beta1"

	"transwarp.io/mlops/mlops-std/stdlog"
)

type Extractor interface {
	ExtractAnnotations(ctx context.Context, workload *kueuev1beta1.Workload) (map[string]string, error)

	ExtractLabels(ctx context.Context, workload *kueuev1beta1.Workload) (map[string]string, error)

	ExtractMetadata(ctx context.Context, workload *kueuev1beta1.Workload) (*ExtractedMetadata, error)
}

type ExtractedMetadata struct {
	Annotations map[string]string `json:"annotations"`
	Labels      map[string]string `json:"labels"`
	OwnerInfo   *OwnerInfo        `json:"owner_info"`
}

type OwnerInfo struct {
	APIVersion string `json:"api_version"`
	Kind       string `json:"kind"`
	Name       string `json:"name"`
	UID        string `json:"uid"`
	Namespace  string `json:"namespace"`
}

type ResourceExtractor interface {
	CanExtract(gvk schema.GroupVersionKind) bool

	Extract(ctx context.Context, namespace, name string, gvk schema.GroupVersionKind) (*ExtractedMetadata, error)
}

type DefaultExtractor struct {
	dynamicClient dynamic.Interface
	extractors    map[string]ResourceExtractor
}

func NewExtractor(dynamicClient dynamic.Interface) Extractor {
	extractor := &DefaultExtractor{
		dynamicClient: dynamicClient,
		extractors:    make(map[string]ResourceExtractor),
	}

	extractor.RegisterExtractor("batch/v1/Job", NewJobExtractor(dynamicClient))
	extractor.RegisterExtractor("apps/v1/Deployment", NewDeploymentExtractor(dynamicClient))
	extractor.RegisterExtractor("v1/Pod", NewPodExtractor(dynamicClient))

	return extractor
}

func (e *DefaultExtractor) RegisterExtractor(resourceType string, extractor ResourceExtractor) {
	e.extractors[resourceType] = extractor
}

func (e *DefaultExtractor) ExtractAnnotations(ctx context.Context, workload *kueuev1beta1.Workload) (map[string]string, error) {
	metadata, err := e.ExtractMetadata(ctx, workload)
	if err != nil {
		return nil, err
	}
	return metadata.Annotations, nil
}

func (e *DefaultExtractor) ExtractLabels(ctx context.Context, workload *kueuev1beta1.Workload) (map[string]string, error) {
	metadata, err := e.ExtractMetadata(ctx, workload)
	if err != nil {
		return nil, err
	}
	return metadata.Labels, nil
}

func (e *DefaultExtractor) ExtractMetadata(ctx context.Context, workload *kueuev1beta1.Workload) (*ExtractedMetadata, error) {
	if workload == nil {
		return nil, fmt.Errorf("workload is nil")
	}

	if len(workload.OwnerReferences) == 0 {
		stdlog.Warn("workload has no owner references", "workload", workload.Name)
		return &ExtractedMetadata{
			Annotations: make(map[string]string),
			Labels:      make(map[string]string),
		}, nil
	}

	ownerRef := workload.OwnerReferences[0]

	gv, err := schema.ParseGroupVersion(ownerRef.APIVersion)
	if err != nil {
		return nil, fmt.Errorf("failed to parse API version %s: %w", ownerRef.APIVersion, err)
	}

	gvk := schema.GroupVersionKind{
		Group:   gv.Group,
		Version: gv.Version,
		Kind:    ownerRef.Kind,
	}

	resourceType := fmt.Sprintf("%s/%s", ownerRef.APIVersion, ownerRef.Kind)
	extractor, exists := e.extractors[resourceType]
	if !exists {
		extractor = NewGenericExtractor(e.dynamicClient)
	}

	metadata, err := extractor.Extract(ctx, workload.Namespace, ownerRef.Name, gvk)
	if err != nil {
		return nil, fmt.Errorf("failed to extract metadata from %s/%s: %w", ownerRef.Kind, ownerRef.Name, err)
	}

	metadata.OwnerInfo = &OwnerInfo{
		APIVersion: ownerRef.APIVersion,
		Kind:       ownerRef.Kind,
		Name:       ownerRef.Name,
		UID:        string(ownerRef.UID),
		Namespace:  workload.Namespace,
	}

	return metadata, nil
}

type JobExtractor struct {
	dynamicClient dynamic.Interface
}

func NewJobExtractor(dynamicClient dynamic.Interface) ResourceExtractor {
	return &JobExtractor{dynamicClient: dynamicClient}
}

func (e *JobExtractor) CanExtract(gvk schema.GroupVersionKind) bool {
	return gvk.Group == "batch" && gvk.Version == "v1" && gvk.Kind == "Job"
}

func (e *JobExtractor) Extract(ctx context.Context, namespace, name string, gvk schema.GroupVersionKind) (*ExtractedMetadata, error) {
	gvr := schema.GroupVersionResource{
		Group:    gvk.Group,
		Version:  gvk.Version,
		Resource: "jobs",
	}

	obj, err := e.dynamicClient.Resource(gvr).Namespace(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get Job %s/%s: %w", namespace, name, err)
	}

	annotations := obj.GetAnnotations()
	if annotations == nil {
		annotations = make(map[string]string)
	}

	labels := obj.GetLabels()
	if labels == nil {
		labels = make(map[string]string)
	}

	return &ExtractedMetadata{
		Annotations: annotations,
		Labels:      labels,
	}, nil
}

type DeploymentExtractor struct {
	dynamicClient dynamic.Interface
}

func NewDeploymentExtractor(dynamicClient dynamic.Interface) ResourceExtractor {
	return &DeploymentExtractor{dynamicClient: dynamicClient}
}

func (e *DeploymentExtractor) CanExtract(gvk schema.GroupVersionKind) bool {
	return gvk.Group == "apps" && gvk.Version == "v1" && gvk.Kind == "Deployment"
}

func (e *DeploymentExtractor) Extract(ctx context.Context, namespace, name string, gvk schema.GroupVersionKind) (*ExtractedMetadata, error) {
	gvr := schema.GroupVersionResource{
		Group:    gvk.Group,
		Version:  gvk.Version,
		Resource: "deployments",
	}

	obj, err := e.dynamicClient.Resource(gvr).Namespace(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get Deployment %s/%s: %w", namespace, name, err)
	}

	annotations := obj.GetAnnotations()
	if annotations == nil {
		annotations = make(map[string]string)
	}

	labels := obj.GetLabels()
	if labels == nil {
		labels = make(map[string]string)
	}

	return &ExtractedMetadata{
		Annotations: annotations,
		Labels:      labels,
	}, nil
}

type PodExtractor struct {
	dynamicClient dynamic.Interface
}

func NewPodExtractor(dynamicClient dynamic.Interface) ResourceExtractor {
	return &PodExtractor{dynamicClient: dynamicClient}
}

func (e *PodExtractor) CanExtract(gvk schema.GroupVersionKind) bool {
	return gvk.Group == "" && gvk.Version == "v1" && gvk.Kind == "Pod"
}

func (e *PodExtractor) Extract(ctx context.Context, namespace, name string, gvk schema.GroupVersionKind) (*ExtractedMetadata, error) {
	gvr := schema.GroupVersionResource{
		Group:    "",
		Version:  "v1",
		Resource: "pods",
	}

	obj, err := e.dynamicClient.Resource(gvr).Namespace(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get Pod %s/%s: %w", namespace, name, err)
	}

	annotations := obj.GetAnnotations()
	if annotations == nil {
		annotations = make(map[string]string)
	}

	labels := obj.GetLabels()
	if labels == nil {
		labels = make(map[string]string)
	}

	return &ExtractedMetadata{
		Annotations: annotations,
		Labels:      labels,
	}, nil
}

type GenericExtractor struct {
	dynamicClient dynamic.Interface
}

func NewGenericExtractor(dynamicClient dynamic.Interface) ResourceExtractor {
	return &GenericExtractor{dynamicClient: dynamicClient}
}

func (e *GenericExtractor) CanExtract(gvk schema.GroupVersionKind) bool {
	return true
}

func (e *GenericExtractor) Extract(ctx context.Context, namespace, name string, gvk schema.GroupVersionKind) (*ExtractedMetadata, error) {
	resourceName := inferResourceName(gvk.Kind)

	gvr := schema.GroupVersionResource{
		Group:    gvk.Group,
		Version:  gvk.Version,
		Resource: resourceName,
	}

	obj, err := e.dynamicClient.Resource(gvr).Namespace(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get %s %s/%s: %w", gvk.Kind, namespace, name, err)
	}

	annotations := obj.GetAnnotations()
	if annotations == nil {
		annotations = make(map[string]string)
	}

	labels := obj.GetLabels()
	if labels == nil {
		labels = make(map[string]string)
	}

	return &ExtractedMetadata{
		Annotations: annotations,
		Labels:      labels,
	}, nil
}

func inferResourceName(kind string) string {
	switch kind {
	case "Job":
		return "jobs"
	case "Deployment":
		return "deployments"
	case "Pod":
		return "pods"
	case "Service":
		return "services"
	case "ConfigMap":
		return "configmaps"
	case "Secret":
		return "secrets"
	case "StatefulSet":
		return "statefulsets"
	case "DaemonSet":
		return "daemonsets"
	case "ReplicaSet":
		return "replicasets"
	default:
		return kind + "s"
	}
}
