package query

// StatusFilter 实现
func (f *StatusFilter) Apply(tasks []*Task) []*Task {
	if len(f.Status) == 0 {
		return tasks
	}

	filtered := make([]*Task, 0)
	for _, task := range tasks {
		for _, status := range f.Status {
			if string(task.Status) == status {
				filtered = append(filtered, task)
				break
			}
		}
	}
	return filtered
}

// ProjectFilter 实现
func (f *ProjectFilter) Apply(tasks []*Task) []*Task {
	if f.ProjectName == "" && f.ProjectID == "" {
		return tasks
	}

	filtered := make([]*Task, 0)
	for _, task := range tasks {
		if f.ProjectName != "" && task.ProjectName == f.ProjectName {
			filtered = append(filtered, task)
		}
		if f.ProjectID != "" && task.ProjectID == f.ProjectID {
			filtered = append(filtered, task)
		}
	}
	return filtered
}
