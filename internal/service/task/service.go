package task

import (
	"context"
	"fmt"
	"time"

	"transwarp.io/mlops/pipeline/internal/core/task/model"
	"transwarp.io/mlops/pipeline/internal/core/task/query"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
	"transwarp.io/mlops/pipeline/internal/service/task/util"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

type TaskService struct {
	kueueClient   *client.KueueClient
	queryExecutor *query.QueryExecutor[*model.Task]
	dataProvider  *TaskDataProvider
}

type TaskDataProvider struct {
	kueueClient *client.KueueClient
}

func NewTaskDataProvider(kueueClient *client.KueueClient) *TaskDataProvider {
	return &TaskDataProvider{
		kueueClient: kueueClient,
	}
}

func (tdp *TaskDataProvider) GetData(ctx context.Context) ([]*model.Task, error) {
	kueueWorkloads, err := tdp.kueueClient.ListWorkloads("")
	if err != nil {
		return nil, fmt.Errorf("failed to list kueue workloads: %w", err)
	}

	tasks := make([]*model.Task, len(kueueWorkloads))
	for i, kw := range kueueWorkloads {
		tasks[i], _ = util.FromKueueWorkload(kw)
	}

	return tasks, nil
}

func (tdp *TaskDataProvider) GetDataByNamespace(ctx context.Context, namespace string) ([]*model.Task, error) {
	kueueWorkloads, err := tdp.kueueClient.ListWorkloads(namespace)
	if err != nil {
		return nil, fmt.Errorf("failed to list kueue workloads for namespace %s: %w", namespace, err)
	}

	tasks := make([]*model.Task, len(kueueWorkloads))
	for i, kw := range kueueWorkloads {
		tasks[i], _ = util.FromKueueWorkload(kw)
	}

	return tasks, nil
}

func (tdp *TaskDataProvider) Count(ctx context.Context) (int32, error) {
	tasks, err := tdp.GetData(ctx)
	if err != nil {
		return 0, err
	}
	return int32(len(tasks)), nil
}

func NewTaskService() *TaskService {
	kueueClient := client.NewKueueClient()
	dataProvider := NewTaskDataProvider(kueueClient)

	executorConfig := &query.ExecutorConfig{
		QueryTimeout:       30 * time.Second,
		SlowQueryThreshold: 1 * time.Second,
		MaxConcurrency:     10,
	}

	queryExecutor := query.NewQueryExecutor[*model.Task](dataProvider, executorConfig)

	return &TaskService{
		kueueClient:   kueueClient,
		queryExecutor: queryExecutor,
		dataProvider:  dataProvider,
	}
}

func (ws *TaskService) ListTasks(ctx context.Context, req *model.ListTasksReq) (*model.ListTasksResp, error) {
	zlog.Info(fmt.Sprintf("Listing tasks with request: %+v", req))

	queryBuilder := query.NewTaskQuery()
	ws.applyFilters(queryBuilder, req)
	ws.applySorting(queryBuilder, req)
	if req.Page > 0 && req.PageSize > 0 {
		queryBuilder.Limit(req.Page, req.PageSize)
	}

	var result *query.QueryResult[*model.Task]
	var err error

	if req.Namespace != "" {
		result, err = ws.queryExecutor.ExecuteWithNamespace(ctx, req.Namespace, queryBuilder.QueryBuilder)
	} else {
		result, err = ws.queryExecutor.Execute(ctx, queryBuilder.QueryBuilder)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to execute task query: %w", err)
	}

	return &model.ListTasksResp{
		Items:      result.Items,
		Total:      result.Total,
		Page:       result.Page,
		PageSize:   result.PageSize,
		TotalPages: result.TotalPages,
	}, nil
}

func (ws *TaskService) applyFilters(queryBuilder *query.TaskQuery, req *model.ListTasksReq) {
	if req.ProjectName != "" {
		queryBuilder.WhereProjectName(req.ProjectName)
	}

	if len(req.Status) > 0 {
		statuses := make([]model.TaskStatus, len(req.Status))
		for i, s := range req.Status {
			statuses[i] = model.TaskStatus(s)
		}
		queryBuilder.WhereStatuses(statuses)
	}

	if len(req.Type) > 0 {
		types := make([]model.TaskType, len(req.Type))
		for i, t := range req.Type {
			types[i] = model.TaskType(t)
		}
		queryBuilder.WhereTypes(types)
	}

	if len(req.Priority) > 0 {
		priorities := make([]model.TaskPriority, len(req.Priority))
		for i, p := range req.Priority {
			priorities[i] = model.TaskPriority(p)
		}
		queryBuilder.WherePriorities(priorities)
	}

	if req.Creator != "" {
		queryBuilder.WhereCreator(req.Creator)
	}

	if req.QueueName != "" {
		queryBuilder.WhereQueueName(req.QueueName)
	}

	if req.CreateAtStart != nil {
		startTime := time.Unix(*req.CreateAtStart, 0)
		queryBuilder.WhereCreatedAfter(startTime)
	}

	if req.CreateAtEnd != nil {
		endTime := time.Unix(*req.CreateAtEnd, 0)
		queryBuilder.WhereCreatedBefore(endTime)
	}

	if req.SearchKeyword != "" {
		queryBuilder.WhereKeywordSearch(req.SearchKeyword)
	}
}

func (ws *TaskService) applySorting(queryBuilder *query.TaskQuery, req *model.ListTasksReq) {
	sortBy := req.SortBy
	if sortBy == "" {
		sortBy = "create_at"
	}

	sortOrder := req.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}

	direction := query.SortAsc
	if sortOrder == "desc" {
		direction = query.SortDesc
	}

	switch sortBy {
	case "name":
		queryBuilder.OrderByName(direction)
	case "create_at":
		queryBuilder.OrderByCreateTime(direction)
	case "start_at":
		queryBuilder.OrderByStartTime(direction)
	case "priority":
		queryBuilder.OrderByPriority(direction)
	case "status":
		queryBuilder.OrderByStatus(direction)
	case "duration":
		queryBuilder.OrderByDuration(direction)
	default:
		queryBuilder.OrderByCreateTime(direction)
	}
}

func (ws *TaskService) GetTask(ctx context.Context, namespace, name string) (*model.Task, error) {
	kw, err := ws.kueueClient.GetWorkload(namespace, name)
	if err != nil {
		return nil, fmt.Errorf("failed to get kueue workload: %w", err)
	}

	return util.FromKueueWorkload(kw)
}

func (ws *TaskService) QueryTasks(ctx context.Context, queryBuilder *query.TaskQuery) (*query.QueryResult[*model.Task], error) {
	return ws.queryExecutor.Execute(ctx, queryBuilder.QueryBuilder)
}

func (ws *TaskService) CountTasks(ctx context.Context, queryBuilder *query.TaskQuery) (int32, error) {
	return ws.queryExecutor.Count(ctx, queryBuilder.QueryBuilder)
}

func (ws *TaskService) ExistsTask(ctx context.Context, queryBuilder *query.TaskQuery) (bool, error) {
	return ws.queryExecutor.Exists(ctx, queryBuilder.QueryBuilder)
}

func (ws *TaskService) DeleteTask(ctx context.Context, namespace, name string) error {
	return ws.kueueClient.DeleteWorkload(ctx, namespace, name)
}
