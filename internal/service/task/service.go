package task

import (
	"context"
	"fmt"
	"time"

	kueuev1beta1 "sigs.k8s.io/kueue/apis/kueue/v1beta1"

	"transwarp.io/mlops/pipeline/internal/core/task/model"
	"transwarp.io/mlops/pipeline/internal/core/task/query"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
	"transwarp.io/mlops/pipeline/internal/service/task/util"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

// TaskService 工作负载领域服务
type TaskService struct {
	kueueClient   *client.KueueClient
	queryExecutor *query.QueryExecutor[*model.Task]
	dataProvider  *TaskDataProvider
}

// TaskDataProvider 工作负载数据提供者
type TaskDataProvider struct {
	kueueClient *client.KueueClient
}

// NewTaskDataProvider 创建工作负载数据提供者
func NewTaskDataProvider(kueueClient *client.KueueClient) *TaskDataProvider {
	return &TaskDataProvider{
		kueueClient: kueueClient,
	}
}

// GetData 获取所有工作负载数据
func (tdp *TaskDataProvider) GetData(ctx context.Context) ([]*model.Task, error) {
	kueueWorkloads, err := tdp.kueueClient.ListWorkloads("")
	if err != nil {
		return nil, fmt.Errorf("failed to list kueue workloads: %w", err)
	}

	tasks := make([]*model.Task, len(kueueWorkloads))
	for i, kw := range kueueWorkloads {
		tasks[i], _ = util.FromKueueWorkload(kw)
	}

	return tasks, nil
}

// GetDataByNamespace 按命名空间获取工作负载数据
func (tdp *TaskDataProvider) GetDataByNamespace(ctx context.Context, namespace string) ([]*model.Task, error) {
	kueueWorkloads, err := tdp.kueueClient.ListWorkloads(namespace)
	if err != nil {
		return nil, fmt.Errorf("failed to list kueue workloads for namespace %s: %w", namespace, err)
	}

	tasks := make([]*model.Task, len(kueueWorkloads))
	for i, kw := range kueueWorkloads {
		tasks[i], _ = util.FromKueueWorkload(kw)
	}

	return tasks, nil
}

// GetCount 获取工作负载总数
func (tdp *TaskDataProvider) GetCount(ctx context.Context) (int32, error) {
	tasks, err := tdp.GetData(ctx)
	if err != nil {
		return 0, err
	}
	return int32(len(tasks)), nil
}

// NewTaskService 创建工作负载服务
func NewTaskService() *TaskService {
	kueueClient := client.NewKueueClient()
	dataProvider := NewTaskDataProvider(kueueClient)

	// 配置查询执行器
	executorConfig := &query.ExecutorConfig{
		EnableCache:        true,
		CacheTTL:           5 * time.Minute,
		MaxCacheSize:       1000,
		QueryTimeout:       30 * time.Second,
		EnableMetrics:      true,
		SlowQueryThreshold: 1 * time.Second,
		MaxConcurrency:     10,
	}

	queryExecutor := query.NewQueryExecutor[*model.Task](dataProvider, executorConfig)

	return &TaskService{
		kueueClient:   kueueClient,
		queryExecutor: queryExecutor,
		dataProvider:  dataProvider,
	}
}

// ListTasks 获取工作负载列表（使用新的查询框架）
func (ws *TaskService) ListTasks(ctx context.Context, req *model.ListTasksReq) (*model.ListTasksResp, error) {
	zlog.Info(fmt.Sprintf("Listing tasks with request: %+v", req))

	// 创建查询构建器
	queryBuilder := query.NewTaskQuery()

	// 应用过滤条件
	ws.applyFilters(queryBuilder, req)

	// 应用排序
	ws.applySorting(queryBuilder, req)

	// 应用分页
	if req.Page > 0 && req.PageSize > 0 {
		queryBuilder.Limit(req.Page, req.PageSize)
	}

	// 执行查询
	var result *query.QueryResult[*model.Task]
	var err error

	if req.Namespace != "" {
		result, err = ws.queryExecutor.ExecuteWithNamespace(ctx, req.Namespace, queryBuilder.QueryBuilder)
	} else {
		result, err = ws.queryExecutor.Execute(ctx, queryBuilder.QueryBuilder)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to execute task query: %w", err)
	}

	return &model.ListTasksResp{
		Items:      result.Items,
		Total:      result.Total,
		Page:       result.Page,
		PageSize:   result.PageSize,
		TotalPages: result.TotalPages,
	}, nil
}

// applyFilters 应用过滤条件
func (ws *TaskService) applyFilters(queryBuilder *query.TaskQuery, req *model.ListTasksReq) {
	// 项目过滤
	if req.ProjectName != "" {
		queryBuilder.WhereProject(req.ProjectName)
	}

	// 状态过滤
	if len(req.Status) > 0 {
		statuses := make([]model.TaskStatus, len(req.Status))
		for i, s := range req.Status {
			statuses[i] = model.TaskStatus(s)
		}
		queryBuilder.WhereStatuses(statuses)
	}

	// 类型过滤
	if len(req.Type) > 0 {
		types := make([]model.TaskType, len(req.Type))
		for i, t := range req.Type {
			types[i] = model.TaskType(t)
		}
		queryBuilder.WhereTypes(types)
	}

	// 优先级过滤
	if len(req.Priority) > 0 {
		priorities := make([]model.TaskPriority, len(req.Priority))
		for i, p := range req.Priority {
			priorities[i] = model.TaskPriority(p)
		}
		queryBuilder.WherePriorities(priorities)
	}

	// 创建者过滤
	if req.Creator != "" {
		queryBuilder.WhereCreator(req.Creator)
	}

	// 队列过滤
	if req.QueueName != "" {
		queryBuilder.WhereQueueName(req.QueueName)
	}

	// 时间范围过滤
	if req.CreateTimeStart != nil {
		startTime := time.Unix(*req.CreateTimeStart, 0)
		queryBuilder.WhereCreatedAfter(startTime)
	}

	if req.CreateTimeEnd != nil {
		endTime := time.Unix(*req.CreateTimeEnd, 0)
		queryBuilder.WhereCreatedBefore(endTime)
	}

	// 关键词搜索
	if req.SearchKeyword != "" {
		queryBuilder.WhereKeywordSearch(req.SearchKeyword)
	}
}

// applySorting 应用排序
func (ws *TaskService) applySorting(queryBuilder *query.TaskQuery, req *model.ListTasksReq) {
	sortBy := req.SortBy
	if sortBy == "" {
		sortBy = "create_at"
	}

	sortOrder := req.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}

	direction := query.SortAsc
	if sortOrder == "desc" {
		direction = query.SortDesc
	}

	switch sortBy {
	case "name":
		queryBuilder.OrderByName(direction)
	case "create_at":
		queryBuilder.OrderByCreateTime(direction)
	case "start_at":
		queryBuilder.OrderByStartTime(direction)
	case "priority":
		queryBuilder.OrderByPriority(direction)
	case "status":
		queryBuilder.OrderByStatus(direction)
	case "duration":
		queryBuilder.OrderByDuration(direction)
	default:
		queryBuilder.OrderByCreateTime(direction)
	}
}

func (ws *TaskService) GetTask(ctx context.Context, namespace, name string) (*model.Task, error) {
	kw, err := ws.kueueClient.GetWorkload(namespace, name)
	if err != nil {
		return nil, fmt.Errorf("failed to get kueue workload: %w", err)
	}

	return util.FromKueueWorkload(kw)
}

// QueryTasks 使用查询构建器查询工作负载
func (ws *TaskService) QueryTasks(ctx context.Context, queryBuilder *query.TaskQuery) (*query.QueryResult[*model.Task], error) {
	return ws.queryExecutor.Execute(ctx, queryBuilder.QueryBuilder)
}

// CountTasks 统计符合条件的工作负载数量
func (ws *TaskService) CountTasks(ctx context.Context, queryBuilder *query.TaskQuery) (int32, error) {
	return ws.queryExecutor.Count(ctx, queryBuilder.QueryBuilder)
}

// ExistsTask 检查是否存在符合条件的工作负载
func (ws *TaskService) ExistsTask(ctx context.Context, queryBuilder *query.TaskQuery) (bool, error) {
	return ws.queryExecutor.Exists(ctx, queryBuilder.QueryBuilder)
}

// FindFirstTask 查找第一个符合条件的工作负载
// func (ws *TaskService) FindFirstTask(ctx context.Context, queryBuilder *query.TaskQuery) (*model.Task, error) {
// 	return ws.queryExecutor.First(ctx, queryBuilder.QueryBuilder)
// }
//
// // GetTaskStats 获取工作负载统计信息
// func (ws *TaskService) GetTaskStats(ctx context.Context, namespace string) (*TaskStatsResp, error) {
// 	// 使用查询框架获取统计信息
// 	allQuery := query.NewTaskQuery()
// 	if namespace != "" {
// 		allQuery.WhereNamespace(namespace)
// 	}
//
// 	// 获取所有工作负载
// 	allResult, err := ws.QueryTasks(ctx, allQuery)
// 	if err != nil {
// 		return nil, fmt.Errorf("failed to get tasks for stats: %w", err)
// 	}
//
// 	// 计算统计信息
// 	stats := &TaskStatsResp{
// 		Total:      allResult.Total,
// 		ByStatus:   make(map[string]int),
// 		ByPriority: make(map[int32]int),
// 		Resources: &ResourceStatsResp{
// 			CPU:       ResourceMetrics{},
// 			Memory:    ResourceMetrics{},
// 			GPU:       ResourceMetrics{},
// 			GPUMemory: ResourceMetrics{},
// 		},
// 	}
//
// 	// 统计各种维度
// 	for _, wl := range allResult.Items {
// 		// 状态统计
// 		stats.ByStatus[string(wl.Status)]++
//
// 		// 优先级统计
// 		stats.ByPriority[int32(wl.Priority)]++
//
// 		// 资源统计
// 		if wl.Resources != nil {
// 			if wl.Resources.CPU != nil {
// 				stats.Resources.CPU.Requested += wl.Resources.CPU.Allocated
// 				stats.Resources.CPU.Used += wl.Resources.CPU.Used
// 				stats.Resources.CPU.Limit += wl.Resources.CPU.Limit
// 			}
//
// 			if wl.Resources.Memory != nil {
// 				stats.Resources.Memory.Requested += wl.Resources.Memory.Allocated
// 				stats.Resources.Memory.Used += wl.Resources.Memory.Used
// 				stats.Resources.Memory.Limit += wl.Resources.Memory.Limit
// 			}
//
// 			if wl.Resources.GpuCore != nil {
// 				stats.Resources.GPU.Requested += wl.Resources.GpuCore.Allocated
// 				stats.Resources.GPU.Used += wl.Resources.GpuCore.Used
// 				stats.Resources.GPU.Limit += wl.Resources.GpuCore.Limit
// 			}
//
// 			if wl.Resources.GpuMemory != nil {
// 				stats.Resources.GPUMemory.Requested += wl.Resources.GpuMemory.Allocated
// 				stats.Resources.GPUMemory.Used += wl.Resources.GpuMemory.Used
// 				stats.Resources.GPUMemory.Limit += wl.Resources.GpuMemory.Limit
// 			}
// 		}
// 	}
//
// 	return stats, nil
// }

// CreateTask 创建工作负载
// func (ws *TaskService) CreateTask(ctx context.Context, task *model.Task) (*model.Task, error) {
// 	// 转换为Kueue Workload对象
// 	kw := ws.toKueueWorkload(task)
//
// 	// 创建Kueue Workload
// 	createdKw, err := ws.kueueClient.CreateWorkload(ctx, kw)
// 	if err != nil {
// 		return nil, fmt.Errorf("failed to create kueue workload: %w", err)
// 	}
//
// 	return FromKueueWorkload(createdKw), nil
// }

// UpdateTask 更新工作负载
// func (ws *TaskService) UpdateTask(ctx context.Context, task *model.Task) (*model.Task, error) {
// 	// 转换为Kueue Workload对象
// 	kw := ws.toKueueWorkload(task)
//
// 	// 更新Kueue Workload
// 	updatedKw, err := ws.kueueClient.UpdateWorkload(ctx, kw)
// 	if err != nil {
// 		return nil, fmt.Errorf("failed to update kueue workload: %w", err)
// 	}
//
// 	return FromKueueWorkload(updatedKw), nil
// }

// DeleteTask 删除工作负载
func (ws *TaskService) DeleteTask(ctx context.Context, namespace, name string) error {
	return ws.kueueClient.DeleteWorkload(ctx, namespace, name)
}

// ClearQueryCache 清空查询缓存
func (ws *TaskService) ClearQueryCache() {
	ws.queryExecutor.ClearCache()
}

// GetQueryMetrics 获取查询指标
func (ws *TaskService) GetQueryMetrics() *query.QueryStats {
	return ws.queryExecutor.GetMetrics()
}

// GetCacheStats 获取缓存统计
func (ws *TaskService) GetCacheStats() query.CacheStats {
	return ws.queryExecutor.GetCacheStats()
}

// toKueueWorkload 转换为Kueue Workload对象
func (ws *TaskService) toKueueWorkload(task *model.Task) *kueuev1beta1.Workload {
	// 这里需要实现从领域对象到Kueue Workload的转换
	// 由于这是一个复杂的转换过程，这里提供一个基本的框架
	kw := &kueuev1beta1.Workload{}
	kw.Name = task.Name
	// kw.Namespace = task.Namespace
	kw.Labels = task.Labels
	kw.Annotations = task.Annotations

	// 设置队列
	// if task.QueueName != "" {
	// 	kw.Spec.QueueName = task.QueueName
	// }

	// 设置优先级
	priority := int32(task.Priority)
	kw.Spec.Priority = &priority

	// 设置是否激活
	active := task.Status != model.StatusSuspended
	kw.Spec.Active = &active

	// 这里需要根据实际需求设置PodSets
	// 暂时留空，需要根据具体的业务逻辑来实现

	return kw
}
