package task

import (
	"errors"
	"time"

	kueuev1beta1 "sigs.k8s.io/kueue/apis/kueue/v1beta1"
	"transwarp.io/mlops/pipeline/internal/core/task/model"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

func FromKueueWorkload(kw *kueuev1beta1.Workload) (*model.Task, error) {
	if kw == nil {
		return nil, errors.New("kueue workload is nil")
	}

	// 解析任务类型
	taskType := parseTaskType(kw.Labels)

	// 解析任务状态
	status := parseWorkloadStatus(kw)

	// 解析时间
	startTime := parseStartTime(kw)
	endTime := parseEndTime(kw)

	// 解析创建者
	creator := parseCreator(kw.Annotations)

	// 解析项目信息
	projectName, projectID := parseProjectInfo(kw.Labels)

	// 解析资源配置
	resources := parseResources(kw.Spec.PodSets)

	return &model.Task{
		ID:          string(kw.OwnerReferences[0].UID),
		Name:        kw.OwnerReferences[0].Name,
		Type:        taskType,
		Priority:    model.TaskPriority(*kw.Spec.Priority),
		Status:      status,
		Creator:     creator,
		CreateAt:    kw.CreationTimestamp.Time,
		StartAt:     startTime,
		EndAt:       endTime,
		Description: kw.Annotations["description"],
		ProjectName: projectName,
		ProjectID:   projectID,
		Resources:   resources,

		QueueName:        string(kw.Spec.QueueName),
		ClusterQueueName: string(kw.Status.Admission.ClusterQueue),

		Labels:      kw.Labels,
		Annotations: kw.Annotations,
		Namespace:   kw.Namespace,
	}, nil
}

// 解析任务类型
func parseTaskType(labels map[string]string) model.TaskType {
	if typeStr, ok := labels["task-type"]; ok {
		return model.TaskType(typeStr)
	}
	return model.TaskTypeUnknown
}

// 解析工作负载状态
func parseWorkloadStatus(kw *kueuev1beta1.Workload) model.TaskStatus {
	// 检查是否被暂停
	if kw.Spec.Active != nil && !*kw.Spec.Active {
		return model.StatusSuspended
	}

	// 检查准入状态
	if kw.Status.Admission == nil {
		return model.StatusPending
	}

	// 检查是否在运行
	if kw.Status.Running != nil {
		return model.StatusRunning
	}

	// 检查是否完成
	if kw.Status.Completed != nil {
		if kw.Status.Completed.Result == kueuev1beta1.WorkloadSucceeded {
			return model.StatusSucceeded
		}
		return model.StatusFailed
	}

	// 检查是否在排队
	if kw.Status.Admission != nil && kw.Status.Admission.ClusterQueue != "" {
		return model.StatusQueued
	}

	return model.StatusPending
}

// 解析开始时间
func parseStartTime(kw *kueuev1beta1.Workload) *time.Time {
	if kw.Status.Running != nil {
		return &kw.Status.Running.StartTime.Time
	}
	return nil
}

// 解析结束时间
func parseEndTime(kw *kueuev1beta1.Workload) *time.Time {
	if kw.Status.Completed != nil {
		return &kw.Status.Completed.Time.Time
	}
	return nil
}

// 解析创建者
func parseCreator(annotations map[string]string) string {
	if creator, ok := annotations["created-by"]; ok {
		return creator
	}
	return ""
}

// 解析项目信息
func parseProjectInfo(labels map[string]string) (string, string) {
	projectName := labels["project-name"]
	projectID := labels["project-id"]
	return projectName, projectID
}

// 解析资源配置
func parseResources(podSets []kueuev1beta1.PodSet) *model.TaskResources {
	if len(podSets) == 0 {
		return nil
	}

	resources := &model.TaskResources{
		CPU:       &model.ResourceUsage{},
		Memory:    &model.ResourceUsage{},
		GpuCore:   &model.ResourceUsage{},
		GpuMemory: &model.ResourceUsage{},
	}

	// 累加所有 PodSet 的资源
	for _, podSet := range podSets {
		count := int32(1)
		if podSet.Count != nil {
			count = *podSet.Count
		}

		// CPU 资源
		if cpu := podSet.Template.Spec.Containers[0].Resources.Requests.Cpu(); cpu != nil {
			resources.CPU.Allocated += float64(cpu.MilliValue()) * float64(count) / 1000
		}
		if cpu := podSet.Template.Spec.Containers[0].Resources.Limits.Cpu(); cpu != nil {
			resources.CPU.Limit += float64(cpu.MilliValue()) * float64(count) / 1000
		}

		// 内存资源
		if mem := podSet.Template.Spec.Containers[0].Resources.Requests.Memory(); mem != nil {
			resources.Memory.Allocated += float64(mem.Value()) / (1024 * 1024 * 1024) * float64(count)
		}
		if mem := podSet.Template.Spec.Containers[0].Resources.Limits.Memory(); mem != nil {
			resources.Memory.Limit += float64(mem.Value()) / (1024 * 1024 * 1024) * float64(count)
		}

		// GPU 资源
		if gpu := podSet.Template.Spec.Containers[0].Resources.Requests.Name("nvidia.com/gpu", resource.DecimalSI); gpu != nil {
			resources.GpuCore.Allocated += float64(gpu.Value()) * float64(count)
		}
		if gpu := podSet.Template.Spec.Containers[0].Resources.Limits.Name("nvidia.com/gpu", resource.DecimalSI); gpu != nil {
			resources.GpuCore.Limit += float64(gpu.Value()) * float64(count)
		}
	}

	return resources
}
