package converter

import (
    "fmt"
    batchv1 "k8s.io/api/batch/v1"
    corev1 "k8s.io/api/core/v1"
    "k8s.io/apimachinery/pkg/runtime"
    kueuev1beta1 "sigs.k8s.io/kueue/apis/kueue/v1beta1"
)

// AnnotationExtractor 注解提取器接口
type AnnotationExtractor interface {
    ExtractAnnotations(obj runtime.Object) (map[string]string, error)
}

// JobAnnotationExtractor Job注解提取器
type JobAnnotationExtractor struct{}

func (e *JobAnnotationExtractor) ExtractAnnotations(obj runtime.Object) (map[string]string, error) {
    job, ok := obj.(*batchv1.Job)
    if !ok {
        return nil, fmt.Errorf("object is not a Job")
    }
    return job.Annotations, nil
}

// PodAnnotationExtractor Pod注解提取器
type PodAnnotationExtractor struct{}

func (e *PodAnnotationExtractor) ExtractAnnotations(obj runtime.Object) (map[string]string, error) {
    pod, ok := obj.(*corev1.Pod)
    if !ok {
        return nil, fmt.Errorf("object is not a Pod")
    }
    return pod.Annotations, nil
}

// AnnotationExtractorFactory 注解提取器工厂
type AnnotationExtractorFactory struct {
    extractors map[string]AnnotationExtractor
}

func NewAnnotationExtractorFactory() *AnnotationExtractorFactory {
    factory := &AnnotationExtractorFactory{
        extractors: make(map[string]AnnotationExtractor),
    }
    
    // 注册默认提取器
    factory.Register("Job", &JobAnnotationExtractor{})
    factory.Register("Pod", &PodAnnotationExtractor{})
    
    return factory
}

func (f *AnnotationExtractorFactory) Register(kind string, extractor AnnotationExtractor) {
    f.extractors[kind] = extractor
}

func (f *AnnotationExtractorFactory) GetExtractor(kind string) (AnnotationExtractor, error) {
    extractor, exists := f.extractors[kind]
    if !exists {
        return nil, fmt.Errorf("no extractor registered for kind: %s", kind)
    }
    return extractor, nil
}