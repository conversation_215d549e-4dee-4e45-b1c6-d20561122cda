package core

import (
	"transwarp.io/mlops/pipeline/core/workload"
)

var PipelineMgr IPipelineManager
var PipelineVersionMgr IPipelineVersionManager
var ComponentMgr IComponentManager
var RunMgr IRunManager

var WorkloadMgr *workload.Manager

func Init() {
	PipelineMgr = NewPipelineManager()
	PipelineVersionMgr = NewPipelineVersionManager()
	RunMgr = NewRunManager()
	ComponentMgr = NewComponentManager()

	WorkloadMgr = workload.NewManager()
}
