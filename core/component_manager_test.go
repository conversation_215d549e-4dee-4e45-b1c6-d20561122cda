package core

import (
	"context"
	"github.com/magiconair/properties/assert"
	"os"
	"testing"
	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	"transwarp.io/mlops/mlops-std/util"
	"transwarp.io/mlops/pipeline/core/model"
)

func TestGetComponents(t *testing.T) {
	err := os.Chdir("..")
	if err != nil {
		panic(err)
	}
	componentManager := InitializeComponentManager()
	model.ComponentMap = componentManager.getComponents()
	c, err := componentManager.exportComponents()
	print(c)
	a, err := componentManager.GetComponents(context.Background())
	print(a)
	assert.Equal(t, err, nil)
}

func InitializeComponentManager() *ComponentManager {

	componentManager := &ComponentManager{
		imageTag: "master",
	}
	return componentManager
}

func TestJson(t *testing.T) {
	json := pb.SavePipelineVersionBody{
		Style: pb.PipelineVersion_TIMING,
		TimingConfig: &pb.TimingConfig{
			Cron:         "0 0 * * * ? *",
			ScheduleType: pb.TimingConfig_Advanced,
		},
		TaskFlow: &pb.TaskFlow{
			Nodes: []*pb.Node{
				{
					Id: "KJGHSADA",
					TemplateSource: &pb.TemplateSource{
						TemplateType: pb.TaskSourceType_PIPELINE,
						SourceId:     "C00001",
					},
					Inputs: nil,
					Outputs: []*pb.ArtifactConfig{
						{
							Name: "data",
							Path: "/artifact/output0",
							Type: pb.ArtifactType_OUTPUT,
						},
					},
					Params:    map[string]string{"datasource-id": "288", "datasource-type": "sample"},
					Name:      "获取数据cx",
					Container: nil,
					Position: &pb.Position{
						X: 100,
						Y: 100,
					},
					NodeSelector:   nil,
					AdvancedConfig: nil,
					Volume:         nil,
				}, {
					Id: "AFDASFF",
					TemplateSource: &pb.TemplateSource{
						TemplateType: pb.TaskSourceType_PIPELINE,
						SourceId:     "C00002",
					},
					Inputs: []*pb.ArtifactConfig{
						{
							Name: "input",
							Path: "/artifact/input0",
							Type: pb.ArtifactType_INPUT,
							From: &pb.From{
								NodeId: "KJGHSADA",
								ArtifactName: "data",
							},
						},
					},
					Outputs: []*pb.ArtifactConfig{
						{
							Name: "output",
							Path: "/artifact/output0",
							Type: pb.ArtifactType_OUTPUT,
						},
					},
					Params:    map[string]string{"service-id": "service-befd4a18-ffda-4550-a528-02287373f2d1", "port": "1884", "endpoint": "/api/v1", "workers": "2"},
					Name:      "批量预测cx",
					Container: nil,
					Position: &pb.Position{
						X: 200,
						Y: 200,
					},
					NodeSelector:   nil,
					AdvancedConfig: nil,
					Volume:         nil,
				}, {
					Id: "GSDGSGS",
					TemplateSource: &pb.TemplateSource{
						TemplateType: pb.TaskSourceType_PIPELINE,
						SourceId:     "C00003",
					},
					Inputs: []*pb.ArtifactConfig{
						{
							Name: "data",
							Path: "/artifact/input0",
							Type: pb.ArtifactType_INPUT,
							From: &pb.From{
								NodeId: "AFDASFF",
								ArtifactName: "output",
							},
						},
					},
					Outputs: []*pb.ArtifactConfig{
						{
							Name: "dataset-id",
							Path: "/artifact/output0",
							Type: pb.ArtifactType_OUTPUT,
						}, {
							Name: "storage-dir",
							Path: "/artifact/output1",
							Type: pb.ArtifactType_OUTPUT,
						},
					},
					Params:    map[string]string{"name": "结果输出", "datasource-type": "sample"},
					Name:      "结果输出cx",
					Container: nil,
					Position: &pb.Position{
						X: 300,
						Y: 300,
					},
					NodeSelector:   nil,
					AdvancedConfig: nil,
					Volume:         nil,
				},
			},
			Dependencies: []*pb.Dependency{
				{
					NodeId:    "AFDASFF",
					DepNodeId: []string{"KJGHSADA"},
				}, {
					NodeId:    "GSDGSGS",
					DepNodeId: []string{"AFDASFF"},
				},
			},
		},
	}
	a := util.ToJson(json)
	print(a)
}
