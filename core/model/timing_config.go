package model

import pb "transwarp.io/aip/llmops-common/pb/pipeline"

type TimingConfig struct {
	Cron              string
	Status            pb.TimingConfig_Status
	ScheduleType      pb.TimingConfig_ScheduleType
	MaximumConcurrent int32
}

func (c *TimingConfig) FromPb(pb *pb.TimingConfig) *TimingConfig {
	if pb == nil {
		return c
	}
	c.Cron = pb.Cron
	c.Status = pb.Status
	c.ScheduleType = pb.ScheduleType
	c.MaximumConcurrent = pb.MaximumConcurrent
	return c
}

func (c *TimingConfig) ToPb() *pb.TimingConfig {
	if c == nil {
		return nil
	}
	return &pb.TimingConfig{
		Cron:              c.Cron,
		Status:            c.Status,
		ScheduleType:      c.ScheduleType,
		MaximumConcurrent: c.MaximumConcurrent,
	}
}
