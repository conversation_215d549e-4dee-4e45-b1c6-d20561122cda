package model

import (
	apiv1 "k8s.io/api/core/v1"
	"transwarp.io/aip/llmops-common/pb/common"
	"transwarp.io/mlops/mlops-std/stderr"
	"transwarp.io/mlops/mlops-std/util"
	config "transwarp.io/mlops/pipeline/conf"
)

type VolumeCfgs []*common.VolumeCfg

func (volumeCfgs *VolumeCfgs) ToK8s() ([]apiv1.Volume, error) {
	if volumeCfgs == nil {
		return nil, nil
	}
	var volumes []apiv1.Volume

	for _, volumeCfg := range *volumeCfgs {
		var volumeSource apiv1.VolumeSource
		switch volumeCfg.VolumeType {
		case common.MLOpsVolumeType_MLOPS_File_TYPE_HOST_PATH:
			volumeSource = apiv1.VolumeSource{
				HostPath: &apiv1.HostPathVolumeSource{
					Path: volumeCfg.HostpathCfg.Path,
				},
			}
		case common.MLOpsVolumeType_MLOPS_File_TYPE_FILE_SYSTEM,
			common.MLOpsVolumeType_MLOPS_File_TYPE_MODEL_CUBE,
			common.MLOpsVolumeType_MLOPS_File_TYPE_SAMPLE_CUBE,
			common.MLOpsVolumeType_MLOPS_File_TYPE_SFS:
			volumeSource = apiv1.VolumeSource{
				PersistentVolumeClaim: &apiv1.PersistentVolumeClaimVolumeSource{
					ClaimName: config.PipeineConfig.MLOPS.Storage.Sfs.PvcName,
				},
			}
		case common.MLOpsVolumeType_MLOPS_File_TYPE_PVC:
			volumeSource = apiv1.VolumeSource{
				PersistentVolumeClaim: &apiv1.PersistentVolumeClaimVolumeSource{
					ClaimName: volumeCfg.PvcCfg.PvcName,
				},
			}
		case common.MLOpsVolumeType_MLOPS_File_TYPE_CONFIG_MAP:
			items := make([]apiv1.KeyToPath, 0)
			for _, keyPath := range volumeCfg.ConfigMapCfg.KeyPaths {
				items = append(items, apiv1.KeyToPath{
					Key:  keyPath.Key,
					Path: keyPath.Path,
				})
			}

			volumeSource = apiv1.VolumeSource{
				ConfigMap: &apiv1.ConfigMapVolumeSource{
					LocalObjectReference: apiv1.LocalObjectReference{
						Name: volumeCfg.ConfigMapCfg.Name,
					},
					DefaultMode: volumeCfg.ConfigMapCfg.DefaultMode,
					Items:       items,
				},
			}
		case common.MLOpsVolumeType_MLOPS_File_TYPE_EMPTYDIR:
			volumeSource = apiv1.VolumeSource{
				EmptyDir: &apiv1.EmptyDirVolumeSource{
					Medium: apiv1.StorageMedium(volumeCfg.EmptyDirCfg.Medium),
				},
			}
		default:
			return nil, stderr.NodeVolumeCfgInValid.Error(util.ToJson(volumeCfg))
		}
		volumes = append(volumes, apiv1.Volume{
			Name:         volumeCfg.Name,
			VolumeSource: volumeSource,
		})
	}
	return volumes, nil
}

func (volumeCfgs *VolumeCfgs) FromK8s(volumes []apiv1.Volume) (*VolumeCfgs, error) {
	*volumeCfgs = []*common.VolumeCfg{}
	for _, volume := range volumes {
		volumeCfg := &common.VolumeCfg{
			Name: volume.Name,
		}
		if volume.HostPath != nil {
			volumeCfg.VolumeType = common.MLOpsVolumeType_MLOPS_File_TYPE_HOST_PATH
			volumeCfg.HostpathCfg = &common.HostPathVolumeCfg{
				Path: volume.HostPath.Path,
			}
		} else if volume.PersistentVolumeClaim != nil {
			volumeCfg.VolumeType = common.MLOpsVolumeType_MLOPS_File_TYPE_PVC
			volumeCfg.PvcCfg = &common.PvcVolumeCfg{
				PvcName: volume.PersistentVolumeClaim.ClaimName,
			}
		} else if volume.ConfigMap != nil {
			volumeCfg.VolumeType = common.MLOpsVolumeType_MLOPS_File_TYPE_CONFIG_MAP
			kpcs := make([]*common.KeyPathCfg, 0)
			for _, item := range volume.ConfigMap.Items {
				kpcs = append(kpcs, &common.KeyPathCfg{
					Key:  item.Key,
					Path: item.Path,
				})
			}
			volumeCfg.ConfigMapCfg = &common.ConfigMapVolumeCfg{
				Name:        volume.ConfigMap.Name,
				DefaultMode: volume.ConfigMap.DefaultMode,
				KeyPaths:    kpcs,
			}

		} else if volume.EmptyDir != nil {
			volumeCfg.VolumeType = common.MLOpsVolumeType_MLOPS_File_TYPE_EMPTYDIR
			volumeCfg.EmptyDirCfg = &common.EmptyDirVolumeCfg{
				Medium: string(volume.EmptyDir.Medium),
			}

		} else {
			return nil, stderr.NodeVolumeCfgInValid.Error(util.ToJson(volume))
		}

		*volumeCfgs = append(*volumeCfgs, volumeCfg)
	}
	return volumeCfgs, nil
}
