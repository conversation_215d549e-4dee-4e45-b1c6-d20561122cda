package model

import (
	v1 "k8s.io/api/core/v1"
	"transwarp.io/aip/llmops-common/pb/common"
)

type Probe struct {
	Handler             Handler
	InitialDelaySeconds int32
	TimeoutSeconds      int32
	PeriodSeconds       int32
	SuccessThreshold    int32
	FailureThreshold    int32
}

func (l *Probe) ToK8s() *v1.Probe {
	if l == nil {
		return nil
	}
	lifecycle := &v1.Probe{
		Handler:             *l.Handler.ToK8s(),
		InitialDelaySeconds: l.InitialDelaySeconds,
		TimeoutSeconds:      l.TimeoutSeconds,
		PeriodSeconds:       l.PeriodSeconds,
		SuccessThreshold:    l.SuccessThreshold,
		FailureThreshold:    l.FailureThreshold,
	}
	return lifecycle
}

func (l *Probe) FromK8s(probe *v1.Probe) *Probe {
	l.Handler = *(&Handler{}).FromK8s(&probe.Handler)
	l.InitialDelaySeconds = probe.InitialDelaySeconds
	l.TimeoutSeconds = probe.TimeoutSeconds
	l.PeriodSeconds = probe.PeriodSeconds
	l.SuccessThreshold = probe.SuccessThreshold
	l.FailureThreshold = probe.FailureThreshold
	return l
}

func (l *Probe) FromPb(pb *common.Probe) *Probe {
	l.Handler = *(&Handler{}).FromPb(pb.Handler)
	l.InitialDelaySeconds = pb.InitialDelaySeconds
	l.TimeoutSeconds = pb.TimeoutSeconds
	l.PeriodSeconds = pb.PeriodSeconds
	l.SuccessThreshold = pb.SuccessThreshold
	l.FailureThreshold = pb.FailureThreshold
	return l
}

func (l *Probe) ToPb() *common.Probe {
	if l == nil {
		return nil
	}
	return &common.Probe{
		Handler:             l.Handler.ToPb(),
		InitialDelaySeconds: l.InitialDelaySeconds,
		TimeoutSeconds:      l.TimeoutSeconds,
		PeriodSeconds:       l.PeriodSeconds,
		SuccessThreshold:    l.SuccessThreshold,
		FailureThreshold:    l.FailureThreshold,
	}
}
