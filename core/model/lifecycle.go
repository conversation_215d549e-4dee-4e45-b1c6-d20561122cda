package model

import (
	v1 "k8s.io/api/core/v1"
	"transwarp.io/aip/llmops-common/pb/common"
)

type Lifecycle struct {
	PostStart *Handler
	PreStop   *Handler
}

func (l *Lifecycle) ToK8s() *v1.Lifecycle {
	if l == nil {
		return nil
	}
	lifecycle := &v1.Lifecycle{
		PostStart: l.PostStart.ToK8s(),
		PreStop:   l.PreStop.ToK8s(),
	}
	return lifecycle
}

func (l *Lifecycle) FromK8s(lifecycle *v1.Lifecycle) *Lifecycle {
	if lifecycle.PreStop != nil {
		l.PreStop = (&Handler{}).FromK8s(lifecycle.PreStop)
	}
	if lifecycle.PostStart != nil {
		l.PostStart = (&Handler{}).FromK8s(lifecycle.PostStart)
	}
	return l
}

func (l *Lifecycle) FromPb(pb *common.Lifecycle) *Lifecycle{
	if pb.PostStart != nil {
		l.PostStart = (&Handler{}).FromPb(pb.PostStart)
	}
	if pb.PreStop != nil {
		l.PreStop = (&Handler{}).FromPb(pb.PreStop)
	}
	return l
}

func (l *Lifecycle) ToPb() *common.Lifecycle{
	if l == nil {
		return nil
	}
	return &common.Lifecycle{
		PostStart: l.PostStart.ToPb(),
		PreStop: l.PreStop.ToPb(),
	}
}