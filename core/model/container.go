package model

import (
	"context"
	v1 "k8s.io/api/core/v1"
	"transwarp.io/aip/llmops-common/pb/common"
)

type Container struct {
	Name            string
	Command         []string
	Image           string
	Args            []string
	Env             map[string]string
	Resources       *ResourceRequirements
	PullPolicy      v1.PullPolicy
	SecurityContext *SecurityContext
	MountPaths      *MountPaths
	Lifecycle       *Lifecycle
	ResourceId      *int32
	LivenessProbe   *Probe
	ReadinessProbe  *Probe
}

func (c *Container) FromPb(pb *common.Container) {
	c.Name = pb.Name
	c.Command = pb.Cmds
	c.Image = pb.Image
	c.Args = pb.Args
	c.Env = pb.Envs
	if pb.ResourceRequirements != nil {
		c.Resources = &ResourceRequirements{}
		c.Resources.FromPb(pb.ResourceRequirements)
	}
	switch pb.ImagePullPolicy {
	case 1:
		c.PullPolicy = v1.PullNever
	case 2:
		c.PullPolicy = v1.PullIfNotPresent
	default:
		c.PullPolicy = v1.PullAlways
	}
	c.SecurityContext = &SecurityContext{}
	c.SecurityContext.FromPb(pb.SecurityContext)
	if pb.MountPaths != nil {
		c.MountPaths = &MountPaths{}
		*c.MountPaths = pb.MountPaths
	}
	if pb.Lifecycle != nil {
		c.Lifecycle = (&Lifecycle{}).FromPb(pb.Lifecycle)
	}

	if pb.ResourceId != nil {
		c.ResourceId = pb.ResourceId
	}

	if pb.LivenessProbe != nil {
		c.LivenessProbe = (&Probe{}).FromPb(pb.LivenessProbe)
	}

	if pb.ReadinessProbe != nil {
		c.ReadinessProbe = (&Probe{}).FromPb(pb.ReadinessProbe)
	}
}

func (c *Container) ToPb() *common.Container {
	if c == nil {
		return nil
	}

	container := &common.Container{
		Name:           c.Name,
		Cmds:           c.Command,
		Image:          c.Image,
		Args:           c.Args,
		Envs:           c.Env,
		Lifecycle:      c.Lifecycle.ToPb(),
		ReadinessProbe: c.ReadinessProbe.ToPb(),
		LivenessProbe:  c.LivenessProbe.ToPb(),
	}
	if c.MountPaths != nil {
		container.MountPaths = *c.MountPaths
	}
	if c.Resources != nil {
		container.ResourceRequirements = c.Resources.ToPb()
	}
	if c.SecurityContext != nil {
		container.SecurityContext = c.SecurityContext.ToPb()
	}

	if c.ResourceId != nil {
		container.ResourceId = c.ResourceId
	}

	switch c.PullPolicy {
	case v1.PullNever:
		container.ImagePullPolicy = common.Container_PullNever
	case v1.PullIfNotPresent:
		container.ImagePullPolicy = common.Container_PullIfNotPresent
	default:
		container.ImagePullPolicy = common.Container_PullAlways
	}
	return container
}

func (c *Container) ToK8s(ctx context.Context) (*v1.Container, error) {
	var env = []v1.EnvVar{}
	for key, value := range c.Env {
		env = append(env, v1.EnvVar{
			Name:  key,
			Value: value,
		})
	}
	var securityContext *v1.SecurityContext
	var mountPaths []v1.VolumeMount
	if c.SecurityContext != nil {
		securityContext = c.SecurityContext.ToK8s()
	}
	if c.MountPaths != nil {
		mountPaths = c.MountPaths.ToK8s()
	}

	container := &v1.Container{
		Name:            c.Name,
		Image:           c.Image,
		Args:            c.Args,
		Env:             env,
		Command:         c.Command,
		ImagePullPolicy: c.PullPolicy,
		SecurityContext: securityContext,
		VolumeMounts:    mountPaths,
		Lifecycle:       c.Lifecycle.ToK8s(),
		LivenessProbe:   c.LivenessProbe.ToK8s(),
		ReadinessProbe:  c.ReadinessProbe.ToK8s(),
	}
	if c.Resources != nil {
		var resources *v1.ResourceRequirements
		var err error
		resources, err = c.Resources.ToK8s(ctx)
		if err != nil {
			return nil, err
		}
		container.Resources = *resources
	}
	if c.PullPolicy != "" {
		container.ImagePullPolicy = v1.PullAlways
	}
	return container, nil
}

func (c *Container) FromK8s(container *v1.Container, src string) *Container {
	envs := map[string]string{}
	for _, envVar := range container.Env {
		envs[envVar.Name] = envVar.Value
	}

	c.Name = container.Name
	c.Command = container.Command
	c.Image = container.Image
	c.Args = container.Args
	c.Env = envs
	c.Resources = (&ResourceRequirements{}).FromK8s(&container.Resources, src)
	c.PullPolicy = container.ImagePullPolicy
	if container.SecurityContext != nil {
		c.SecurityContext = (&SecurityContext{}).FromK8s(container.SecurityContext)
	}
	c.MountPaths = (&MountPaths{}).FromK8s(container.VolumeMounts)
	if container.Lifecycle != nil {
		c.Lifecycle = (&Lifecycle{}).FromK8s(container.Lifecycle)
	}
	if container.LivenessProbe != nil {
		c.LivenessProbe = (&Probe{}).FromK8s(container.LivenessProbe)
	}
	if container.ReadinessProbe != nil {
		c.ReadinessProbe = (&Probe{}).FromK8s(container.ReadinessProbe)
	}
	return c
}
