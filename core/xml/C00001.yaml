component_advanced_config: {}
component_source:
  template_type: 1
container:
  cmds:
  - python3
  - retrieve-data/retrieve_data.py
  args:
  - --datasource-id
  - '{{inputs.parameters.datasource-id}}'
  - --datasource-type
  - '{{inputs.parameters.datasource-type}}'
  image: retrieve-data
  mount_paths:
  - mount_path: /data/nfs/
    volume_name: sfs-volume
  resource_requirements:
    resource_limit:
      cpu: 500
      memory: 512
    resource_request:
      cpu: 500
      memory: 512
  security_context: {}
desc: 获取数据组件，支持从样本仓库选择数据集。
desc_en: retrieve data component, which supports selecting data sets from the sample
  warehouse.
id: C00001
name: 获取数据
name_en: retrieve data
outputs:
  data: /artifact/output0
params:
  datasource-id: ''
  datasource-type: 'sample'
volume:
- name: sfs-volume
  volume_type: 3
