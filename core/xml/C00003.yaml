component_advanced_config: {}
component_source:
  template_type: 1
container:
  cmds:
  - python3
  - save-data/save_data.py
  args:
  - --name
  - '{{inputs.parameters.name}}'
  - --datasource-type
  - '{{inputs.parameters.datasource-type}}'
  image: save-data
  mount_paths:
  - mount_path: /data/nfs/
    volume_name: sfs-volume
  resource_requirements:
    resource_limit:
      cpu: 2000
      memory: 2048
    resource_request:
      cpu: 1000
      memory: 1024
  security_context: {}
desc: 结果输出
desc_en: batch prediction
id: C00003
inputs:
  data: /artifact/input0
name: 结果输出
name_en: result output
outputs:
  dataset-id: /artifact/output0
  storage-dir: /artifact/output1
params:
  name: ''
  datasource-type: 'sample'
volume:
- name: sfs-volume
  volume_type: 3
