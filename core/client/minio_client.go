package client

import (
	"fmt"
	"github.com/cenkalti/backoff"
	"github.com/minio/minio-go/pkg/credentials"
	"io"
	"net/http"
	"time"
	"transwarp.io/mlops/mlops-std/stderr"

	minio "github.com/minio/minio-go"
)

type MinioClient struct {
	Client *minio.Client
}

func NewMinioClient(minioEndpoint string, accessKey string, secretKey string, secure bool, region string, initConnectionTimeout time.Duration) (*MinioClient, error) {
	var minioClient *minio.Client
	var err error
	var operation = func() error {
		minioClient, err = CreateMinioClient(minioEndpoint, accessKey, secretKey, secure, region)
		if err != nil {
			return err
		}
		return nil
	}
	b := backoff.NewExponentialBackOff()
	b.MaxElapsedTime = initConnectionTimeout
	err = backoff.Retry(operation, b)
	if err != nil {
		return nil, err
	}
	return &MinioClient{
		Client: minioClient,
	}, nil
}

func CreateMinioClient(endpoint string,
	accessKey string, secretKey string, secure bool, region string) (*minio.Client, error) {

	cred := createCredentialProvidersChain(endpoint, accessKey, secretKey)
	minioClient, err := minio.NewWithCredentials(endpoint, cred, secure, region)
	if err != nil {
		return nil, stderr.MinioConnectFail.Error(err, "Error while creating minio client: %+v", err)
	}
	return minioClient, nil
}

func createCredentialProvidersChain(endpoint, accessKey, secretKey string) *credentials.Credentials {
	// first try with static api key
	if accessKey != "" && secretKey != "" {
		return credentials.NewStaticV4(accessKey, secretKey, "")
	}
	// otherwise use a chained provider: minioEnv -> awsEnv -> IAM
	providers := []credentials.Provider{
		&credentials.EnvMinio{},
		&credentials.EnvAWS{},
		&credentials.IAM{
			Client: &http.Client{
				Transport: http.DefaultTransport,
			},
		},
	}
	return credentials.New(&credentials.Chain{Providers: providers})
}

func joinHostPort(host, port string) string {
	if port == "" {
		return host
	}
	return fmt.Sprintf("%s:%s", host, port)
}

func (c *MinioClient) PutObject(bucketName, objectName string, reader io.Reader, objectSize int64, opts minio.PutObjectOptions) (n int64, err error) {
	return c.Client.PutObject(bucketName, objectName, reader, objectSize, opts)
}

func (c *MinioClient) GetObject(bucketName, objectName string, opts minio.GetObjectOptions) (io.Reader, error) {
	return c.Client.GetObject(bucketName, objectName, opts)
}

func (c *MinioClient) DeleteObject(bucketName, objectName string) error {
	return c.Client.RemoveObject(bucketName, objectName)
}

func (c *MinioClient) StatObject(bucketName, objectName string, opts minio.StatObjectOptions) (minio.ObjectInfo, error) {
	return c.Client.StatObject(bucketName, objectName, opts)
}

func (c *MinioClient) GetObjectTail(bucketName, objectName string, maxSize int64) (io.Reader, error) {
	info, err := c.StatObject(bucketName, objectName, minio.StatObjectOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get object info: %w", err)
	}

	// 计算从文件末尾开始读取的偏移量
	objectSize := info.Size
	var start int64
	if objectSize <= maxSize {
		// 如果文件小于或等于 maxSize（2MB），可以直接从头到尾读取
		start = 0
	} else {
		// 否则，从文件末尾开始读取最多 maxSize 字节数据
		start = objectSize - maxSize
	}
	opts := minio.GetObjectOptions{}
	opts.SetRange(start, objectSize-1)
	// 获取对象流
	object, err := c.GetObject(bucketName, objectName, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to get object: %w", err)
	}
	return object, nil
}