package client

import (
	"fmt"
	std_client "transwarp.io/mlops/mlops-std/client"
	client "transwarp.io/mlops/mlops-std/k8s"
	"transwarp.io/mlops/mlops-std/stdlog"
	config "transwarp.io/mlops/pipeline/conf"
)

var (
	PipelineClientInst *PipelineClient
	K8sClient          *client.K8sClient
	CasCli             *std_client.CasClient
	K8sInformerCli     *K8sInformerClient
)

func Init() {
	var err error
	PipelineClientInst, err = NewPipelineClient()
	if err != nil {
		panic(err)
	}
	stdlog.Info(fmt.Sprintf("kube config init path:%s", config.PipeineConfig.MLOPS.KubeConfig.Path))
	K8sClient, err = client.NewK8sClient(config.PipeineConfig.MLOPS.KubeConfig.Path, config.PipeineConfig.MLOPS.KubeConfig.KubeConfigPermit)
	if err != nil {
		panic(err)
	}
	CasCli = &std_client.CasClient{}
	CasCli.Init(config.PipeineConfig.MLOPS.Cas.Host, config.PipeineConfig.MLOPS.Cas.Port)
	K8sInformerCli = &K8sInformerClient{}
	if err := K8sInformerCli.Init(); err != nil {
		panic(err)
	}
}
