package app

import (
	"context"
	"flag"

	"github.com/spf13/cobra"
	"github.com/spf13/viper"
	utilerrors "k8s.io/apimachinery/pkg/util/errors"

	grpcserver "transwarp.io/mlops/pipeline/api/grpc"
	httpserver "transwarp.io/mlops/pipeline/api/http"
	"transwarp.io/mlops/pipeline/cmd/app/options"
	"transwarp.io/mlops/pipeline/conf"
	core "transwarp.io/mlops/pipeline/internal/core/pipeline"
	dao "transwarp.io/mlops/pipeline/internal/dao"
	"transwarp.io/mlops/pipeline/internal/infrastructure/client"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

func NewMLOPSPipelineCommand() *cobra.Command {
	s := options.NewServerRunOptions()
	cmd := &cobra.Command{
		Use:   "mlops-pipeline",
		Short: "Controls job and manage compute resources",
		PersistentPreRun: func(cmd *cobra.Command, args []string) {
			initConfig()
		},
		RunE: func(cmd *cobra.Command, args []string) error {
			completedOptions, err := s.Complete()
			if err != nil {
				return err
			}

			if errs := completedOptions.Validate(); len(errs) != 0 {
				return utilerrors.NewAggregate(errs)
			}

			return Run(cmd.Context(), completedOptions)
		},
	}

	pflag := cmd.PersistentFlags()
	pflag.Int("port", 8080, "Port for the HTTP server")
	viper.BindPFlags(pflag)

	return cmd
}

func Run(ctx context.Context, opts options.CompletedOptions) error {
	var configFile string
	flag.StringVar(&configFile, "config", "",
		"The server will load its initial configuration from this file. "+
			"Omit this flag to use the default configuration values. ")

	// init logger
	zlog.Get().SugarInfoln("init logger done.")

	// 2. deal config
	apply(configFile)
	conf.Init()
	zlog.SugarInfoln("init config done.")

	if err := client.Init(); err != nil {
		zlog.SugarInfoln("init client failed: %v.", err)
		return err
	}
	defer func() {
		if err := client.Shutdown(); err != nil {
			zlog.SugarErrorf("failed to shutdown client: %v", err)
		}
	}()
	zlog.SugarInfoln("init client done.")

	dao.Init()
	core.Init()

	// stdlog.Init(conf.PipeineConfig.MLOPS.LogConfig, "pipeline")
	// 3. start server
	// 创建一个 go-restful WebService
	errChan := make(chan error, 2)
	go func() {
		grpc := grpcserver.NewGRPCServer(&grpcserver.Option{
			Port: conf.PipeineConfig.MLOPS.GRPCServer.Port,
		})
		ech := grpc.Start(ctx)
		select {
		case e := <-ech:
			errChan <- e
		}
	}()

	go func() {
		http := httpserver.NewHTTPServer(&httpserver.Option{
			Port: conf.PipeineConfig.MLOPS.HttpServer.Port,
		})
		ech := http.Start(ctx)
		select {
		case e := <-ech:
			errChan <- e
		}
	}()

	select {
	case err := <-errChan:
		return err
	}

	// http := httpserver.NewHTTPServer(&httpserver.Option{
	// 	Port: conf.PipeineConfig.MLOPS.HttpServer.Port,
	// })
	// ech := http.Start(ctx)
	// select {
	// case err := <-ech:
	// 	return err
	// }
}

func initConfig() {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("/opt/config")
	viper.AddConfigPath(".")

	viper.AutomaticEnv()
	viper.SetEnvPrefix("MLOPS-PIPELINE")

	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			zlog.SugarWarnf("Error reading config:", err)
		}
	}
}

func initLogger() error {

	return nil
}

// read config from file return serveroptions and configuaration
func apply(configFile string) (ctrl.Options, configapi.Configuration, error) {
}
