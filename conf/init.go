package conf

import (
	"fmt"
	"os"
	"transwarp.io/applied-ai/aiot/vision-std/registry"
	"transwarp.io/mlops/mlops-std/conf"
	"transwarp.io/mlops/mlops-std/stderr"
)

var PipeineConfig = new(Config)
var SophonServiceId string

func Init() {
	SophonServiceId = os.Getenv(registry.SophonServiceId)
	configPath := os.Getenv("MLOPS_CONF_DIR")
	_, err := loadConfig("mlops-pipeline.yaml", configPath+"/pipeline")
	if err != nil {
		panic(fmt.Sprintf("error while initializing config, %s", err.Error()))
	}
}

func loadConfig(configName string, configPath string) (*Config, error) {
	c := new(conf.CommonConfig)
	c.LoadConfig(configName, []string{configPath}, "yaml")
	err := c.<PERSON>(PipeineConfig)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "fail to load envs")
	}
	return PipeineConfig, nil
}

type Config struct {
	MLOPS    conf.MLOPS
	Pipeline Pipeine
}

type Pipeine struct {
	Cvat Cvat
	Redis Redis
}

type Cvat struct {
	Host string
	Port string
}


type Redis struct {
	Addrs      string `json:"addrs" yaml:"addrs"`
	Database   int    `json:"database" yaml:"database"`
	Username   string `json:"username" yaml:"username"`
	Password   string `json:"password" yaml:"password"`
	Timeout    int    `json:"timeout" yaml:"timeout"`
	MasterName string `json:"masterName" yaml:"masterName"`
}