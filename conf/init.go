package conf

import (
	"fmt"
	"os"
	"transwarp.io/applied-ai/aiot/vision-std/registry"
	"transwarp.io/mlops/mlops-std/conf"
	"transwarp.io/mlops/mlops-std/stderr"
)

var PipeineConfig = new(conf.Config)
var SophonServiceId string

func Init() {
	SophonServiceId = os.Getenv(registry.SophonServiceId)
	configPath := os.Getenv("MLOPS_CONF_DIR")
	_, err := loadConfig("mlops-pipeline.yaml", configPath+"/pipeline")
	if err != nil {
		panic(fmt.Sprintf("error while initializing config, %s", err.Error()))
	}
}

func loadConfig(configName string, configPath string) (*conf.Config, error) {
	c := new(conf.CommonConfig)
	c.LoadConfig(configName, []string{configPath}, "yaml")
	err := c.<PERSON>(PipeineConfig)
	if err != nil {
		return nil, stderr.Internal.Cause(err, "fail to load envs")
	}
	return PipeineConfig, nil
}
