prometheus:
  endpoint: "http://*************:31601"
  username: ""
  password: ""
  timeout: 30s
  max_retries: 3
  retry_delay: 1s
  enable_tls: false
  tls_config:
    cert_file: ""
    key_file: ""
    ca_file: ""
    insecure_skip_verify: false

cache:
  enabled: true
  ttl: 5m
  max_size: 1000
  cleanup_interval: 10m

metrics:
  node_cpu_usage:
    name: "node_cpu_usage"
    query: '100 - (avg by (instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)'
    description: "Node CPU usage percentage"
    unit: "percent"
    type: "gauge"
    labels: ["instance", "node"]
    interval: 30s
    timeout: 10s

  node_memory_usage:
    name: "node_memory_usage"
    query: '(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100'
    description: "Node memory usage percentage"
    unit: "percent"
    type: "gauge"
    labels: ["instance", "node"]
    interval: 30s
    timeout: 10s

  pod_cpu_usage:
    name: "pod_cpu_usage"
    query: 'sum(rate(container_cpu_usage_seconds_total{container!="POD",container!=""}[5m])) by (pod, namespace, node)'
    description: "Pod CPU usage"
    unit: "cores"
    type: "gauge"
    labels: ["pod", "namespace", "node"]
    interval: 30s
    timeout: 10s

  pod_memory_usage:
    name: "pod_memory_usage"
    query: 'sum(container_memory_working_set_bytes{container!="POD",container!=""}) by (pod, namespace, node)'
    description: "Pod memory usage"
    unit: "bytes"
    type: "gauge"
    labels: ["pod", "namespace", "node"]
    interval: 30s
    timeout: 10s

  gpu_utilization:
    name: "gpu_utilization"
    query: 'DCGM_FI_DEV_GPU_UTIL'
    description: "GPU utilization percentage"
    unit: "percent"
    type: "gauge"
    labels: ["gpu", "instance", "modelName"]
    interval: 15s
    timeout: 10s

  gpu_memory_usage:
    name: "gpu_memory_usage"
    query: 'DCGM_FI_DEV_FB_USED'
    description: "GPU memory usage"
    unit: "bytes"
    type: "gauge"
    labels: ["gpu", "instance", "modelName"]
    interval: 15s
    timeout: 10s

  gpu_memory_total:
    name: "gpu_memory_total"
    query: 'DCGM_FI_DEV_FB_TOTAL'
    description: "GPU memory total"
    unit: "bytes"
    type: "gauge"
    labels: ["gpu", "instance", "modelName"]
    interval: 15s
    timeout: 10s

collectors:
  - name: "resource_collector"
    enabled: true
    interval: 30s
    metric_names:
      - "node_cpu_usage"
      - "node_memory_usage"
      - "pod_cpu_usage"
      - "pod_memory_usage"

  - name: "gpu_collector"
    enabled: true
    interval: 15s
    metric_names:
      - "gpu_utilization"
      - "gpu_memory_usage"
      - "gpu_memory_total"