logging:
  level: "info"
  appenders:
    - name: "stdout"
      type: "stdout"
      encoder: "console"
      filter:
        min_level: "info"
        max_level: "error"
    
    - name: "file-info"
      type: "file"
      path: "/var/log/mlops-pipeline/info.log"
      encoder: "json"
      policy:
        type: "size"
        max_size: 100MB
        max_backups: 14
        max_age: 14d
      filter:
        min_level: "info"
        max_level: "info"
  
    - name: "file-error"
      type: "file"
      path: "/var/log/mlops-pipeline/error.log"
      encoder: "json"
      policy:
        type: "time"
        pattern: "%Y-%m-%d-%H-%M-%S"
        interval: 24h
        max_age: 14d
      filter:
        min_level: "error"
  #        max_level: "error"
  
  loggers:
    appender_refs: ["stdout", "file-info", "file-error"]