apiVersion: v1
kind: Config
clusters:
  - cluster:
      certificate-authority: /srv/kubernetes207/ca.pem
      server: https://172.17.120.207:6443
    name: kubernetes
contexts:
  - context:
      cluster: kubernetes
      user: k8s
    name: kubelet-to-kubernetes
current-context: kubelet-to-kubernetes
users:
  - name: k8s
    user:
      client-certificate: /srv/kubernetes207/admin.pem
      client-key: /srv/kubernetes207/admin-key.pem
