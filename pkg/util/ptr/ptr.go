package ptr

import "time"

func ToPtr[T any](v T) *T {
	return &v
}

func ToValue[T any](v *T) T {
	if v == nil {
		var zero T
		return zero
	}
	return *v
}

func ToBoolPtr(v bool) *bool {
	return &v
}

func ToIntPtr(v int) *int {
	return &v
}

func ToStringPtr(v string) *string {
	return &v
}

func ToTimePtr(v time.Time) *time.Time {
	return &v
}

func DerefBool(v *bool) bool {
	if v == nil {
		return false
	}
	return *v
}

func DerefInt(v *int) int {
	if v == nil {
		return 0
	}
	return *v
}

func DerefString(v *string) string {
	if v == nil {
		return ""
	}
	return *v
}

func DerefTime(v *time.Time) time.Time {
	if v == nil {
		return time.Time{}
	}
	return *v
}
