package httpclient

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"time"

	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

const (
	defaultTimeout               = 30 * time.Second
	defaultMaxIdleConns          = 100
	defaultMaxIdleConnsPerHost   = 100
	defaultIdleConnTimeout       = 90 * time.Second
	defaultTLSHandshakeTimeout   = 10 * time.Second
	defaultExpectContinueTimeout = 1 * time.Second
)

type HttpClient struct {
	*http.Client
	baseURL string
}

type ClientOption func(*HttpClient)

func NewHttpClient(baseURL string, options ...ClientOption) *HttpClient {
	transport := &http.Transport{
		Proxy: http.ProxyFromEnvironment,
		DialContext: (&net.Dialer{
			Timeout:   30 * time.Second,
			KeepAlive: 30 * time.Second,
		}).DialContext,
		MaxIdleConns:          defaultMaxIdleConns,
		MaxIdleConnsPerHost:   defaultMaxIdleConnsPerHost,
		IdleConnTimeout:       defaultIdleConnTimeout,
		TLSHandshakeTimeout:   defaultTLSHandshakeTimeout,
		ExpectContinueTimeout: defaultExpectContinueTimeout,
	}

	client := &HttpClient{
		Client: &http.Client{
			Transport: transport,
			Timeout:   defaultTimeout,
		},
		baseURL: baseURL,
	}

	for _, option := range options {
		option(client)
	}

	return client
}

func WithTimeout(timeout time.Duration) ClientOption {
	return func(c *HttpClient) {
		c.Timeout = timeout
	}
}

func WithTLSConfig(config *tls.Config) ClientOption {
	return func(c *HttpClient) {
		if transport, ok := c.Transport.(*http.Transport); ok {
			transport.TLSClientConfig = config
		}
	}
}

func WithInsecureSkipVerify() ClientOption {
	return func(c *HttpClient) {
		if transport, ok := c.Transport.(*http.Transport); ok {
			if transport.TLSClientConfig == nil {
				transport.TLSClientConfig = &tls.Config{}
			}
			transport.TLSClientConfig.InsecureSkipVerify = true
		}
	}
}

type Request struct {
	Method  string
	Path    string
	Query   url.Values
	Body    interface{}
	Headers map[string]string
}

type Response struct {
	StatusCode int
	Headers    http.Header
	Body       []byte
}

func (r *Response) Decode(v interface{}) error {
	if len(r.Body) == 0 {
		return nil
	}
	return json.Unmarshal(r.Body, v)
}

func (r *Response) Success() bool {
	return r.StatusCode >= 200 && r.StatusCode < 300
}

func (c *HttpClient) do(ctx context.Context, req *Request) (*Response, error) {
	url := c.buildURL(req)

	var body io.Reader
	if req.Body != nil {
		jsonBody, err := json.Marshal(req.Body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request body: %w", err)
		}
		body = bytes.NewBuffer(jsonBody)
	}

	httpReq, err := http.NewRequestWithContext(ctx, req.Method, url, body)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")

	for key, value := range req.Headers {
		httpReq.Header.Set(key, value)
	}

	resp, err := c.Client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	return &Response{
		StatusCode: resp.StatusCode,
		Headers:    resp.Header,
		Body:       respBody,
	}, nil
}

func (c *HttpClient) buildURL(req *Request) string {
	u, err := url.Parse(c.baseURL)
	if err != nil {
		zlog.SugarErrorf("Failed to parse base URL: %v", err)
		return c.baseURL + req.Path
	}

	u.Path = req.Path
	if req.Query != nil {
		u.RawQuery = req.Query.Encode()
	}

	return u.String()
}

func (c *HttpClient) Get(ctx context.Context, path string, query url.Values, headers map[string]string) (*Response, error) {
	req := &Request{
		Method:  http.MethodGet,
		Path:    path,
		Query:   query,
		Headers: headers,
	}
	return c.do(ctx, req)
}

func (c *HttpClient) Post(ctx context.Context, path string, body interface{}, headers map[string]string) (*Response, error) {
	req := &Request{
		Method:  http.MethodPost,
		Path:    path,
		Body:    body,
		Headers: headers,
	}
	return c.do(ctx, req)
}

func (c *HttpClient) Put(ctx context.Context, path string, body interface{}, headers map[string]string) (*Response, error) {
	req := &Request{
		Method:  http.MethodPut,
		Path:    path,
		Body:    body,
		Headers: headers,
	}
	return c.do(ctx, req)
}

func (c *HttpClient) Delete(ctx context.Context, path string, headers map[string]string) (*Response, error) {
	req := &Request{
		Method:  http.MethodDelete,
		Path:    path,
		Headers: headers,
	}
	return c.do(ctx, req)
}

func GetT[T any](ctx context.Context, c *HttpClient, path string, query url.Values, headers map[string]string) (*T, error) {
	resp, err := c.Get(ctx, path, query, headers)
	if err != nil {
		return nil, err
	}

	if !resp.Success() {
		return nil, fmt.Errorf("request failed with status code: %d, body: %s", resp.StatusCode, string(resp.Body))
	}

	var result T
	if err := resp.Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &result, nil
}

func PostT[T any](ctx context.Context, c *HttpClient, path string, body interface{}, headers map[string]string) (*T, error) {
	resp, err := c.Post(ctx, path, body, headers)
	if err != nil {
		return nil, err
	}

	if !resp.Success() {
		return nil, fmt.Errorf("request failed with status code: %d, body: %s", resp.StatusCode, string(resp.Body))
	}

	var result T
	if err := resp.Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &result, nil
}

func PutT[T any](ctx context.Context, c *HttpClient, path string, body interface{}, headers map[string]string) (*T, error) {
	resp, err := c.Put(ctx, path, body, headers)
	if err != nil {
		return nil, err
	}

	if !resp.Success() {
		return nil, fmt.Errorf("request failed with status code: %d, body: %s", resp.StatusCode, string(resp.Body))
	}

	var result T
	if err := resp.Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &result, nil
}

func DeleteT[T any](ctx context.Context, c *HttpClient, path string, headers map[string]string) (*T, error) {
	resp, err := c.Delete(ctx, path, headers)
	if err != nil {
		return nil, err
	}

	if !resp.Success() {
		return nil, fmt.Errorf("request failed with status code: %d, body: %s", resp.StatusCode, string(resp.Body))
	}

	var result T
	if err := resp.Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &result, nil
}
