package errors

import (
	"fmt"
	"net/http"
)

// ErrorCode 错误码类型
type ErrorCode string

const (
	// 通用错误码
	ErrCodeInternal        ErrorCode = "INTERNAL_ERROR"
	ErrCodeInvalidRequest  ErrorCode = "INVALID_REQUEST"
	ErrCodeUnauthorized    ErrorCode = "UNAUTHORIZED"
	ErrCodeForbidden       ErrorCode = "FORBIDDEN"
	ErrCodeNotFound        ErrorCode = "NOT_FOUND"
	ErrCodeConflict        ErrorCode = "CONFLICT"
	ErrCodeTooManyRequests ErrorCode = "TOO_MANY_REQUESTS"
	
	// 业务错误码
	ErrCodePipelineNotFound     ErrorCode = "PIPELINE_NOT_FOUND"
	ErrCodePipelineInvalidState ErrorCode = "PIPELINE_INVALID_STATE"
	ErrCodeWorkloadNotFound     ErrorCode = "WORKLOAD_NOT_FOUND"
	ErrCodeWorkloadInvalidState ErrorCode = "WORKLOAD_INVALID_STATE"
	ErrCodeRunNotFound          ErrorCode = "RUN_NOT_FOUND"
	ErrCodeRunInvalidState      ErrorCode = "RUN_INVALID_STATE"
	
	// 资源错误码
	ErrCodeResourceQuotaExceeded ErrorCode = "RESOURCE_QUOTA_EXCEEDED"
	ErrCodeResourceNotAvailable  ErrorCode = "RESOURCE_NOT_AVAILABLE"
	ErrCodeInvalidResourceSpec   ErrorCode = "INVALID_RESOURCE_SPEC"
	
	// Kubernetes错误码
	ErrCodeK8sClientError    ErrorCode = "K8S_CLIENT_ERROR"
	ErrCodeK8sResourceError  ErrorCode = "K8S_RESOURCE_ERROR"
	ErrCodeKueueClientError  ErrorCode = "KUEUE_CLIENT_ERROR"
)

// AppError 应用错误结构
type AppError struct {
	Code       ErrorCode `json:"code"`
	Message    string    `json:"message"`
	Details    string    `json:"details,omitempty"`
	HTTPStatus int       `json:"-"`
	Cause      error     `json:"-"`
}

// Error 实现error接口
func (e *AppError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("%s: %s (%s)", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// Unwrap 支持errors.Unwrap
func (e *AppError) Unwrap() error {
	return e.Cause
}

// New 创建新的应用错误
func New(code ErrorCode, message string) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		HTTPStatus: getHTTPStatus(code),
	}
}

// Newf 创建格式化的应用错误
func Newf(code ErrorCode, format string, args ...interface{}) *AppError {
	return &AppError{
		Code:       code,
		Message:    fmt.Sprintf(format, args...),
		HTTPStatus: getHTTPStatus(code),
	}
}

// Wrap 包装现有错误
func Wrap(err error, code ErrorCode, message string) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		HTTPStatus: getHTTPStatus(code),
		Cause:      err,
	}
}

// Wrapf 包装现有错误并格式化消息
func Wrapf(err error, code ErrorCode, format string, args ...interface{}) *AppError {
	return &AppError{
		Code:       code,
		Message:    fmt.Sprintf(format, args...),
		HTTPStatus: getHTTPStatus(code),
		Cause:      err,
	}
}

// WithDetails 添加详细信息
func (e *AppError) WithDetails(details string) *AppError {
	e.Details = details
	return e
}

// WithDetailsf 添加格式化的详细信息
func (e *AppError) WithDetailsf(format string, args ...interface{}) *AppError {
	e.Details = fmt.Sprintf(format, args...)
	return e
}

// WithHTTPStatus 设置HTTP状态码
func (e *AppError) WithHTTPStatus(status int) *AppError {
	e.HTTPStatus = status
	return e
}

// IsAppError 检查是否为应用错误
func IsAppError(err error) bool {
	_, ok := err.(*AppError)
	return ok
}

// AsAppError 转换为应用错误
func AsAppError(err error) (*AppError, bool) {
	appErr, ok := err.(*AppError)
	return appErr, ok
}

// getHTTPStatus 根据错误码获取HTTP状态码
func getHTTPStatus(code ErrorCode) int {
	switch code {
	case ErrCodeInvalidRequest:
		return http.StatusBadRequest
	case ErrCodeUnauthorized:
		return http.StatusUnauthorized
	case ErrCodeForbidden:
		return http.StatusForbidden
	case ErrCodeNotFound, ErrCodePipelineNotFound, ErrCodeWorkloadNotFound, ErrCodeRunNotFound:
		return http.StatusNotFound
	case ErrCodeConflict:
		return http.StatusConflict
	case ErrCodeTooManyRequests:
		return http.StatusTooManyRequests
	case ErrCodeResourceQuotaExceeded:
		return http.StatusUnprocessableEntity
	case ErrCodePipelineInvalidState, ErrCodeWorkloadInvalidState, ErrCodeRunInvalidState,
		 ErrCodeResourceNotAvailable, ErrCodeInvalidResourceSpec:
		return http.StatusBadRequest
	case ErrCodeK8sClientError, ErrCodeK8sResourceError, ErrCodeKueueClientError:
		return http.StatusServiceUnavailable
	default:
		return http.StatusInternalServerError
	}
}

// 预定义的常用错误
var (
	ErrInternal        = New(ErrCodeInternal, "Internal server error")
	ErrInvalidRequest  = New(ErrCodeInvalidRequest, "Invalid request")
	ErrUnauthorized    = New(ErrCodeUnauthorized, "Unauthorized")
	ErrForbidden       = New(ErrCodeForbidden, "Forbidden")
	ErrNotFound        = New(ErrCodeNotFound, "Resource not found")
	ErrConflict        = New(ErrCodeConflict, "Resource conflict")
	ErrTooManyRequests = New(ErrCodeTooManyRequests, "Too many requests")
)

// 业务相关错误
var (
	ErrPipelineNotFound     = New(ErrCodePipelineNotFound, "Pipeline not found")
	ErrPipelineInvalidState = New(ErrCodePipelineInvalidState, "Pipeline is in invalid state")
	ErrWorkloadNotFound     = New(ErrCodeWorkloadNotFound, "Workload not found")
	ErrWorkloadInvalidState = New(ErrCodeWorkloadInvalidState, "Workload is in invalid state")
	ErrRunNotFound          = New(ErrCodeRunNotFound, "Run not found")
	ErrRunInvalidState      = New(ErrCodeRunInvalidState, "Run is in invalid state")
)

// 资源相关错误
var (
	ErrResourceQuotaExceeded = New(ErrCodeResourceQuotaExceeded, "Resource quota exceeded")
	ErrResourceNotAvailable  = New(ErrCodeResourceNotAvailable, "Resource not available")
	ErrInvalidResourceSpec   = New(ErrCodeInvalidResourceSpec, "Invalid resource specification")
)

// Kubernetes相关错误
var (
	ErrK8sClientError   = New(ErrCodeK8sClientError, "Kubernetes client error")
	ErrK8sResourceError = New(ErrCodeK8sResourceError, "Kubernetes resource error")
	ErrKueueClientError = New(ErrCodeKueueClientError, "Kueue client error")
)
