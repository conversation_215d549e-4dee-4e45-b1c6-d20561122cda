# MLOps Pipeline 项目重构架构设计

## 1. 整体架构设计

### 1.1 分层架构
```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   REST API      │  │    gRPC API     │  │   Web UI     │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   Application Layer                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  Pipeline Svc   │  │  Workload Svc   │  │   Run Svc    │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    Domain Layer                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  Pipeline Dom   │  │  Workload Dom   │  │   Run Dom    │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                Infrastructure Layer                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Repository    │  │   K8s Client    │  │   Message    │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 新目录结构
```
mlops-pipeline/
├── cmd/                          # 应用程序入口
│   └── server/
│       └── main.go
├── internal/                     # 内部包，不对外暴露
│   ├── app/                      # 应用层
│   │   ├── service/              # 应用服务
│   │   ├── dto/                  # 数据传输对象
│   │   └── handler/              # HTTP/gRPC处理器
│   ├── domain/                   # 领域层
│   │   ├── pipeline/             # 流水线领域
│   │   ├── workload/             # 工作负载领域
│   │   ├── run/                  # 运行领域
│   │   └── shared/               # 共享领域对象
│   └── infrastructure/           # 基础设施层
│       ├── repository/           # 数据访问
│       ├── k8s/                  # Kubernetes客户端
│       ├── config/               # 配置管理
│       └── logger/               # 日志管理
├── pkg/                          # 公共包，可对外暴露
│   ├── errors/                   # 错误处理
│   ├── middleware/               # 中间件
│   ├── utils/                    # 工具函数
│   └── validator/                # 验证器
├── api/                          # API定义
│   ├── proto/                    # protobuf定义
│   ├── openapi/                  # OpenAPI规范
│   └── generated/                # 生成的代码
├── configs/                      # 配置文件
├── deployments/                  # 部署相关
├── docs/                         # 文档
├── scripts/                      # 脚本
└── tests/                        # 测试
    ├── unit/                     # 单元测试
    ├── integration/              # 集成测试
    └── e2e/                      # 端到端测试
```

## 2. 核心设计原则

### 2.1 SOLID原则
- **单一职责原则**: 每个模块只负责一个功能
- **开闭原则**: 对扩展开放，对修改关闭
- **里氏替换原则**: 子类可以替换父类
- **接口隔离原则**: 使用多个专门的接口
- **依赖倒置原则**: 依赖抽象而不是具体实现

### 2.2 领域驱动设计(DDD)
- **聚合根**: Pipeline, Workload, Run
- **值对象**: ResourceUsage, TaskSource
- **领域服务**: 复杂业务逻辑
- **仓储模式**: 数据访问抽象

### 2.3 依赖注入
- 使用接口定义依赖
- 通过构造函数注入
- 避免全局变量

## 3. Workload管理设计

### 3.1 Kueue集成架构
```
┌─────────────────────────────────────────────────────────────┐
│                    Workload API Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   REST API      │  │    gRPC API     │  │   GraphQL    │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                 Workload Service Layer                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  CRUD Service   │  │  Query Service  │  │ Event Svc    │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                 Workload Domain Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Workload      │  │   Queue         │  │  Resource    │ │
│  │   Aggregate     │  │   Management    │  │  Management  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│              Kueue Infrastructure Layer                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  Kueue Client   │  │   K8s Client    │  │  Repository  │ │
│  │   (v0.11.7)     │  │                 │  │              │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 Workload CRD管理
- 支持Kueue 0.11.7的所有CRD资源
- 提供统一的资源管理接口
- 实现资源配额和优先级管理
- 支持多队列调度策略

## 4. 技术栈选择

### 4.1 核心技术
- **Go 1.23**: 主要编程语言
- **Gin/Echo**: HTTP框架
- **gRPC**: RPC框架
- **GORM**: ORM框架
- **Kubernetes Client-go**: K8s客户端
- **Kueue v0.11.7**: 工作负载调度

### 4.2 中间件和工具
- **Prometheus**: 监控指标
- **Jaeger**: 分布式追踪
- **Zap**: 结构化日志
- **Viper**: 配置管理
- **Testify**: 测试框架

## 5. 接口设计

### 5.1 Workload管理API
```go
// Workload CRUD接口
type WorkloadService interface {
    Create(ctx context.Context, req *CreateWorkloadRequest) (*WorkloadResponse, error)
    Get(ctx context.Context, id string) (*WorkloadResponse, error)
    List(ctx context.Context, req *ListWorkloadRequest) (*ListWorkloadResponse, error)
    Update(ctx context.Context, req *UpdateWorkloadRequest) (*WorkloadResponse, error)
    Delete(ctx context.Context, id string) error
    
    // 状态管理
    Suspend(ctx context.Context, id string) error
    Resume(ctx context.Context, id string) error
    
    // 资源管理
    GetResourceUsage(ctx context.Context, id string) (*ResourceUsageResponse, error)
    UpdateResourceQuota(ctx context.Context, req *UpdateQuotaRequest) error
}
```

### 5.2 分页查询支持
```go
type ListWorkloadRequest struct {
    Page     int32             `json:"page"`
    PageSize int32             `json:"page_size"`
    Filter   *WorkloadFilter   `json:"filter"`
    Sort     *SortOptions      `json:"sort"`
}

type WorkloadFilter struct {
    ProjectID   string           `json:"project_id"`
    Status      []WorkloadStatus `json:"status"`
    Type        []WorkloadType   `json:"type"`
    Priority    []int32          `json:"priority"`
    CreateUser  string           `json:"create_user"`
    DateRange   *DateRange       `json:"date_range"`
}
```

## 6. 实施计划

### 阶段1: 基础架构重构
1. 创建新的目录结构
2. 实现依赖注入容器
3. 重构核心模块接口

### 阶段2: Workload管理实现
1. 集成Kueue客户端
2. 实现Workload领域模型
3. 开发CRUD API

### 阶段3: 功能增强
1. 添加分页查询
2. 实现资源监控
3. 添加事件通知

### 阶段4: 测试和文档
1. 完善单元测试
2. 集成测试
3. API文档生成
