# MLOps Pipeline 项目重构文档

## 重构概述

本次重构旨在提高MLOps Pipeline项目的可扩展性、可维护性和兼容性，并添加基于Kueue的工作负载管理功能。

## 重构目标

1. **重构整个项目的组织结构** - 采用清晰的分层架构和领域驱动设计
2. **提高可扩展性和兼容性** - 通过依赖注入和接口抽象实现松耦合
3. **添加Workload管理功能** - 集成Kueue 0.11.7，支持工作负载的完整生命周期管理
4. **实现分页条件查询** - 提供灵活的查询和过滤功能

## 新架构设计

### 分层架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   REST API      │  │    gRPC API     │  │   Web UI     │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   Application Layer                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  Pipeline Svc   │  │  Workload Svc   │  │   Run Svc    │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    Domain Layer                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  Pipeline Dom   │  │  Workload Dom   │  │   Run Dom    │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                Infrastructure Layer                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Repository    │  │   K8s Client    │  │   Message    │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 目录结构

```
mlops-pipeline/
├── cmd/                          # 应用程序入口
│   └── server/
│       └── main.go
├── internal/                     # 内部包，不对外暴露
│   ├── app/                      # 应用层
│   │   ├── service/              # 应用服务
│   │   ├── dto/                  # 数据传输对象
│   │   └── handler/              # HTTP/gRPC处理器
│   ├── domain/                   # 领域层
│   │   ├── pipeline/             # 流水线领域
│   │   ├── workload/             # 工作负载领域
│   │   ├── run/                  # 运行领域
│   │   └── shared/               # 共享领域对象
│   └── infrastructure/           # 基础设施层
│       ├── repository/           # 数据访问
│       ├── k8s/                  # Kubernetes客户端
│       ├── config/               # 配置管理
│       └── logger/               # 日志管理
├── pkg/                          # 公共包，可对外暴露
│   ├── errors/                   # 错误处理
│   ├── middleware/               # 中间件
│   ├── utils/                    # 工具函数
│   └── validator/                # 验证器
├── api/                          # API定义
├── configs/                      # 配置文件
├── docs/                         # 文档
└── tests/                        # 测试
```

## 核心功能

### 1. Workload管理

#### 支持的操作
- **CRUD操作**: 创建、读取、更新、删除工作负载
- **状态管理**: 暂停、恢复、取消工作负载
- **资源管理**: 监控和管理资源使用情况
- **队列管理**: 分配工作负载到不同的队列

#### API接口

```http
# 创建工作负载
POST /api/v1/workloads

# 获取工作负载列表（支持分页和过滤）
GET /api/v1/workloads?page=1&page_size=20&status=Running&type=TRAINING

# 获取工作负载详情
GET /api/v1/workloads/{id}

# 更新工作负载
PUT /api/v1/workloads/{id}

# 删除工作负载
DELETE /api/v1/workloads/{id}

# 状态管理
PUT /api/v1/workloads/{id}/status
POST /api/v1/workloads/{id}/suspend
POST /api/v1/workloads/{id}/resume
POST /api/v1/workloads/{id}/cancel

# 资源使用情况
GET /api/v1/workloads/{id}/resources
```

#### 分页查询支持

支持以下查询参数：
- `page`: 页码（默认1）
- `page_size`: 每页大小（默认20，最大1000）
- `namespace`: 命名空间过滤
- `project`: 项目过滤
- `status`: 状态过滤（支持多个值）
- `type`: 类型过滤（支持多个值）
- `priority`: 优先级过滤（支持多个值）
- `create_user`: 创建用户过滤
- `queue_name`: 队列名称过滤
- `search_keyword`: 搜索关键词
- `sort_by`: 排序字段
- `sort_order`: 排序方向（asc/desc）

### 2. Kueue集成

#### 支持的CRD资源
- **Workload**: 工作负载资源
- **LocalQueue**: 本地队列
- **ClusterQueue**: 集群队列
- **ResourceFlavor**: 资源类型

#### 版本兼容性
- 支持Kueue v0.11.7
- 向后兼容Kueue v0.10.x

## 技术栈

### 核心技术
- **Go 1.23**: 主要编程语言
- **Gin**: HTTP框架
- **gRPC**: RPC框架
- **Kubernetes Client-go**: K8s客户端
- **Kueue v0.11.7**: 工作负载调度

### 中间件和工具
- **Zap**: 结构化日志
- **Viper**: 配置管理
- **Prometheus**: 监控指标
- **Swagger**: API文档

## 设计原则

### 1. SOLID原则
- **单一职责原则**: 每个模块只负责一个功能
- **开闭原则**: 对扩展开放，对修改关闭
- **里氏替换原则**: 子类可以替换父类
- **接口隔离原则**: 使用多个专门的接口
- **依赖倒置原则**: 依赖抽象而不是具体实现

### 2. 领域驱动设计(DDD)
- **聚合根**: Workload, Pipeline, Run
- **值对象**: ResourceUsage, WorkloadID
- **领域服务**: 复杂业务逻辑
- **仓储模式**: 数据访问抽象

### 3. 依赖注入
- 使用接口定义依赖
- 通过构造函数注入
- 避免全局变量

## 错误处理

### 统一错误处理
- 定义标准错误码和错误类型
- 支持错误链和错误包装
- 提供详细的错误信息和上下文

### 错误码分类
- **通用错误**: INTERNAL_ERROR, INVALID_REQUEST, NOT_FOUND
- **业务错误**: WORKLOAD_NOT_FOUND, WORKLOAD_INVALID_STATE
- **资源错误**: RESOURCE_QUOTA_EXCEEDED, RESOURCE_NOT_AVAILABLE
- **K8s错误**: K8S_CLIENT_ERROR, KUEUE_CLIENT_ERROR

## 日志管理

### 结构化日志
- 使用Zap进行结构化日志记录
- 支持多种输出格式（JSON、Console）
- 支持日志轮转和压缩

### 日志级别
- **Debug**: 调试信息
- **Info**: 一般信息
- **Warn**: 警告信息
- **Error**: 错误信息
- **Fatal**: 致命错误

## 配置管理

### 配置文件
- 支持YAML格式配置
- 支持环境变量覆盖
- 支持多环境配置

### 配置项
- 服务器配置（HTTP/gRPC端口、超时等）
- 数据库配置
- Kubernetes配置
- Kueue配置
- 日志配置
- 监控配置

## 测试策略

### 测试类型
- **单元测试**: 测试单个函数和方法
- **集成测试**: 测试模块间的集成
- **端到端测试**: 测试完整的用户场景

### 测试工具
- **Testify**: 测试断言和模拟
- **GoMock**: 接口模拟
- **Ginkgo**: BDD测试框架

## 部署和运维

### 容器化
- 使用多阶段Docker构建
- 最小化镜像大小
- 支持健康检查

### Kubernetes部署
- 提供完整的K8s部署清单
- 支持ConfigMap和Secret
- 支持HPA和VPA

### 监控和告警
- Prometheus指标收集
- Grafana仪表板
- 告警规则配置

## 迁移指南

### 从旧版本迁移
1. **数据迁移**: 将现有数据迁移到新的数据结构
2. **API兼容性**: 提供向后兼容的API
3. **配置迁移**: 更新配置文件格式
4. **部署更新**: 更新部署脚本和配置

### 迁移步骤
1. 备份现有数据和配置
2. 部署新版本服务
3. 运行数据迁移脚本
4. 验证功能正常
5. 切换流量到新服务
6. 清理旧版本资源

## 开发指南

### 开发环境设置
```bash
# 初始化项目
make init

# 安装依赖
make deps

# 运行开发环境
make dev

# 运行测试
make test

# 生成文档
make swagger
```

### 代码规范
- 遵循Go官方代码规范
- 使用golangci-lint进行代码检查
- 编写完整的单元测试
- 添加详细的注释和文档

## 性能优化

### 查询优化
- 使用索引优化数据库查询
- 实现查询结果缓存
- 支持分页和限制查询

### 资源优化
- 使用连接池管理数据库连接
- 实现优雅关闭和资源清理
- 监控内存和CPU使用情况

## 安全考虑

### 认证和授权
- 支持JWT token认证
- 实现基于角色的访问控制
- 提供API密钥管理

### 数据安全
- 敏感数据加密存储
- 支持HTTPS和TLS
- 实现审计日志

## 未来规划

### 短期目标
- 完善测试覆盖率
- 优化性能和稳定性
- 添加更多监控指标

### 长期目标
- 支持多集群管理
- 实现自动扩缩容
- 添加机器学习工作流支持

## 总结

本次重构通过采用现代化的架构设计和最佳实践，显著提升了MLOps Pipeline项目的质量和可维护性。新的架构不仅支持现有功能，还为未来的扩展提供了坚实的基础。
