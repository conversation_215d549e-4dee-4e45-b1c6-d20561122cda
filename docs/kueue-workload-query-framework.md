# Kueue Workload 查询框架重构总结

## 概述

本次重构实现了一个灵活、高效的Kueue工作负载查询框架，类似于数据库查询的方式，支持复杂的条件组合、分页、排序和缓存。

## 架构设计

### 核心组件

1. **查询构建器 (QueryBuilder)** - `internal/core/query/builder.go`
   - 通用的查询构建器，支持泛型
   - 链式API设计，易于使用
   - 支持过滤、排序、分页

2. **过滤器系统 (Filter)** - `internal/core/query/filter.go`
   - 支持多种操作符：等于、不等于、大于、小于、包含、正则等
   - 复合过滤器支持AND/OR逻辑组合
   - 自定义函数过滤器

3. **排序器系统 (Sorter)** - `internal/core/query/sorter.go`
   - 多字段排序支持
   - 自定义排序函数
   - 预定义常用排序器

4. **分页器 (Paginator)** - `internal/core/query/paginator.go`
   - 偏移分页
   - 游标分页
   - 多级分页配置

5. **标签选择器 (LabelSelector)** - `internal/core/query/label_selector.go`
   - Kubernetes风格的标签查询
   - 标签匹配器
   - 预定义标签选择器

6. **查询执行引擎 (Executor)** - `internal/core/query/executor.go`
   - 查询执行和优化
   - 缓存管理
   - 性能监控

7. **缓存系统 (Cache)** - `internal/core/query/cache.go`
   - 内存缓存
   - TTL缓存
   - 多级缓存

### 专用组件

8. **Workload查询器** - `internal/core/query/workload_query.go`
   - 专门针对Workload的查询构建器
   - 预定义的业务查询方法
   - 领域特定的过滤器

## 主要特性

### 🔍 灵活的查询条件组合
```go
// 复杂查询示例
query := NewWorkloadQuery().
    WhereProject("ml-platform").
    WhereTypes([]WorkloadType{WorkloadTypeTraining, WorkloadTypeInference}).
    WherePriorities([]WorkloadPriority{PriorityHigh, PriorityUrgent}).
    WhereCreatedAfter(time.Now().Add(-7*24*time.Hour)).
    WhereActive().
    OrderByCreateTime(SortDesc).
    Limit(1, 20)
```

### 📄 多种分页方式
- **偏移分页**: 适用于一般场景
- **游标分页**: 适用于大数据集
- **配置化分页**: 支持自定义分页参数

### 🔄 多字段排序
```go
query.OrderByPriority(SortDesc).
      OrderByCreateTime(SortDesc).
      OrderByName(SortAsc)
```

### 🏷️ 标签选择器支持
```go
// Kubernetes风格的标签查询
query.WhereLabelMatches("environment in (production,staging)")
query.WhereLabelEquals("project", "ml-platform")
```

### ⚡ 查询缓存
- 内存缓存，支持LRU淘汰
- TTL过期机制
- 多级缓存支持
- 缓存统计和监控

### 📊 性能监控
- 查询指标收集
- 慢查询检测
- 缓存命中率统计
- 查询时间分析

## 文件结构

```
internal/
├── core/
│   ├── query/
│   │   ├── builder.go           # 通用查询构建器
│   │   ├── filter.go            # 过滤器系统
│   │   ├── sorter.go            # 排序器系统
│   │   ├── paginator.go         # 分页器
│   │   ├── label_selector.go    # 标签选择器
│   │   ├── executor.go          # 查询执行引擎
│   │   ├── cache.go             # 缓存系统
│   │   ├── types.go             # 类型定义
│   │   ├── workload_query.go    # Workload专用查询器
│   │   ├── examples.go          # 使用示例
│   │   └── README.md            # 文档
│   └── workload/
│       └── manager.go           # 工作负载管理器
├── domain/
│   └── workload/
│       ├── entity.go            # 领域实体
│       ├── service.go           # 领域服务
│       └── dto.go               # 数据传输对象
└── infrastructure/
    └── client/
        ├── manager.go           # 客户端管理器
        ├── kueue_client.go      # Kueue客户端
        ├── minio_client.go      # Minio客户端
        ├── pipeline_client.go   # Pipeline客户端
        └── k8s_informer.go      # K8s Informer客户端

api/
└── http/
    └── workload/
        ├── mount.go             # 路由挂载
        └── define.go            # API实现
```

## 使用示例

### 基础查询
```go
// 查询运行中的工作负载
runningWorkloads := query.RunningWorkloads().
    OrderByCreateTime(query.SortDesc).
    Limit(1, 20)

result, err := workloadService.QueryWorkloads(ctx, runningWorkloads)
```

### 复杂过滤
```go
// 查询高优先级的GPU训练任务
gpuTrainingJobs := query.NewWorkloadQuery().
    WhereType(workload.WorkloadTypeTraining).
    WhereHasGPU().
    HighPriority().
    Recent()
```

### 时间范围查询
```go
// 查询最近24小时内创建的工作负载
yesterday := time.Now().Add(-24 * time.Hour)
recentWorkloads := query.NewWorkloadQuery().
    WhereCreatedAfter(yesterday).
    OrderByCreateTime(query.SortDesc)
```

### 自定义过滤
```go
// 运行时间超过1小时的工作负载
longRunningFilter := query.NewNamedFuncFilter(
    "long_running",
    func(wl *workload.Workload) bool {
        return wl.CalculateDuration() > time.Hour
    },
)

query := query.NewWorkloadQuery().Where(longRunningFilter)
```

## API接口

### RESTful API
- `GET /api/v1/workload` - 工作负载列表（支持复杂查询）
- `GET /api/v1/workload/{namespace}/{name}` - 工作负载详情
- `GET /api/v1/workload/stats` - 工作负载统计
- `GET /api/v1/workload/running` - 运行中的工作负载
- `GET /api/v1/workload/pending` - 等待中的工作负载
- `GET /api/v1/workload/completed` - 已完成的工作负载
- `GET /api/v1/workload/gpu` - GPU工作负载
- `POST /api/v1/workload/query` - 高级查询
- `GET /api/v1/workload/metrics` - 查询指标
- `DELETE /api/v1/workload/cache` - 清空缓存

### 查询参数
- `page`, `page_size` - 分页参数
- `namespace`, `project` - 过滤参数
- `status`, `type`, `priority` - 状态过滤
- `creator`, `queue_name` - 用户和队列过滤
- `search_keyword` - 关键词搜索
- `sort_by`, `sort_order` - 排序参数
- `create_time_start`, `create_time_end` - 时间范围

## 性能优化

### 缓存策略
- L1缓存：内存缓存，快速访问
- L2缓存：可扩展到Redis等外部缓存
- 缓存键生成：基于查询条件的哈希
- TTL管理：自动过期和清理

### 查询优化
- 索引字段优先排序
- 分页限制保护
- 查询超时控制
- 慢查询监控

### 并发控制
- 查询执行器支持并发限制
- 缓存读写锁保护
- 线程安全的指标收集

## 扩展性

### 新增过滤器
```go
type CustomFilter[T any] struct {
    // 自定义字段
}

func (cf *CustomFilter[T]) Match(item T) bool {
    // 自定义匹配逻辑
    return true
}
```

### 新增排序器
```go
type CustomSorter[T any] struct {
    // 自定义字段
}

func (cs *CustomSorter[T]) Compare(a, b T) int {
    // 自定义比较逻辑
    return 0
}
```

### 新增数据源
```go
type CustomDataProvider[T any] struct {
    // 自定义数据源
}

func (cdp *CustomDataProvider[T]) GetData(ctx context.Context) ([]T, error) {
    // 自定义数据获取逻辑
    return nil, nil
}
```

## 测试策略

### 单元测试
- 过滤器测试
- 排序器测试
- 分页器测试
- 缓存测试

### 集成测试
- 查询执行器测试
- API接口测试
- 性能测试

### 基准测试
- 查询性能基准
- 缓存性能基准
- 内存使用基准

## 监控和运维

### 指标监控
- 查询总数
- 缓存命中率
- 平均查询时间
- 慢查询数量
- 错误查询数量

### 日志记录
- 查询执行日志
- 慢查询日志
- 错误日志
- 缓存操作日志

### 运维工具
- 缓存清理接口
- 查询指标查看
- 配置动态调整

## 最佳实践

1. **使用预定义查询** - 优先使用预定义的查询方法
2. **合理使用缓存** - 对于频繁查询的数据启用缓存
3. **避免过度分页** - 限制页大小，避免一次查询过多数据
4. **使用索引字段排序** - 优先使用有索引的字段进行排序
5. **监控慢查询** - 定期检查慢查询并优化
6. **合理设置超时** - 为查询设置合理的超时时间

## 总结

这个查询框架提供了强大而灵活的工作负载查询能力，支持复杂的业务场景，同时保持了良好的性能和可维护性。通过模块化的设计，框架具有很好的扩展性，可以轻松适应未来的需求变化。
