FROM --platform=${BUILDPLATFORM} harbor.transwarp.io/gold/aip-mm/base/golang-builder:1.23-ubuntu24.04 AS build
ARG TARGETARCH

ARG FTP_SERVER

ARG PACKAGE=transwarp.io/mlops/pipeline
ARG BUILD_VERSION=0.0.0
ARG BUILD_TIME=99999999
ARG CI_COMMIT_SHA=00000000
ARG ARCH=${TARGETARCH}

WORKDIR /build


COPY mlops-pipeline mlops-pipeline
COPY llmops-common llmops-common
COPY mlops-std mlops-std
COPY vision-std vision-std


RUN apt-get update && apt-get install -y wget curl

RUN wget -P /tmp ftp://${FTP_SERVER}/ci/juicefs/alpine/juicefs  \
    && cp /tmp/juicefs /usr/local/bin/  \
    && chmod 777 /usr/local/bin/juicefs

RUN chmod +x ./mlops-pipeline/generate_version.sh
RUN export PATH=$PATH:/go/bin && cd mlops-pipeline && \
    go env -w GOPROXY=http://*************:1111,https://goproxy.io/,https://mirrors.aliyun.com/goproxy/,https://goproxy.cn/,direct && \
    go mod tidy && \
    GO111MODULE=on \
    GOOS=linux \
    GOARCH=${ARCH} \
    go build -ldflags "-X ${PACKAGE}/version.BuildName=${PACKAGE} -X ${PACKAGE}/version.BuildVersion=${BUILD_VERSION} -X ${PACKAGE}/version.BuildTime=${BUILD_TIME} -X ${PACKAGE}/version.CommitID=${CI_COMMIT_SHA}" \
    -o pipeline ./main.go && \
    ./generate_version.sh


FROM  harbor.transwarp.io/gold/aip-mm/base/ubuntu-runner:24.04

ARG TARGETARCH

ARG ARCH=${TARGETARCH}

WORKDIR /app

COPY --from=build /build/mlops-pipeline/bin/boot.sh /bin/boot.sh
COPY --from=build /build/mlops-pipeline/core/xml ./core/xml
COPY --from=build /build/mlops-pipeline/.version ./
COPY --from=build /build/mlops-pipeline/public public
COPY --from=build /build/mlops-pipeline/pipeline ./
COPY --from=build /build/mlops-pipeline/bin/${ARCH}-confd /usr/local/bin/confd
RUN chmod 755 /bin/boot.sh /usr/local/bin/confd

EXPOSE 8750

CMD ["./pipeline"]