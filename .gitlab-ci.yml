image: harbor.transwarp.io/gold/aip-mm/base/golang-gitlab-builder:1.24-ubuntu24.04

stages:
  - test
  - build
  - deploy

unit_tests:
  stage: test
  script:
    - go mod tidy
    - go test -v -count=1 -short -gcflags=all=-l $(go list ./... | grep -v /vendor/)
  tags:
    - k8s

include:
  - project: applied-ai/aiot/common-ci
    file: go-build.yml
  - project: applied-ai/aiot/common-ci
    file: deploy.yml

