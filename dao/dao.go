package dao

import (
	"gorm.io/gorm"
	"time"
	"transwarp.io/mlops/pipeline/dao/query"
)

type CommonDAO struct {
	db *gorm.DB
	*query.Query
}

func NewDAO(datasource *gorm.DB) (*CommonDAO, error) {
	DAO := &CommonDAO{
		db: datasource,
	}
	DAO.Query = query.Use(datasource)
	return DAO, nil
}

type Model struct {
	ID         string `gorm:"type:VARCHAR(50);primaryKey; comment:ID"`
	CreateTime time.Time  `gorm:"type:TIMESTAMP; default:CURRENT_TIMESTAMP; comment:创建时间"`
	UpdateTime time.Time  `gorm:"type:TIMESTAMP ON UPDATE CURRENT_TIMESTAMP; default:CURRENT_TIMESTAMP; comment:更新时间"`
}

type Pipeline struct {
	Model
	Name       string `gorm:"size:255; not null; uniqueIndex:unique_pipeline_name; comment:名称"`
	Desc       string `gorm:"type:TEXT; comment:描述"`
	CreateUser string `gorm:"size:255; comment:创建用户"`
	ProjectId  string `gorm:"size:50; uniqueIndex:unique_pipeline_name; comment:项目ID"`
}

type PipelineVersion struct {
	Model
	PipelineId               string `gorm:"size:50; not null; uniqueIndex:unique_pipeline_version_name; comment:pipeline ID"`
	Name                     string `gorm:"size:255; not null; uniqueIndex:unique_pipeline_version_name; comment:名称"`
	TaskFlow                 string `gorm:"type:MEDIUMTEXT; comment:工作流程图json"`
	Desc                     string `gorm:"type:TEXT; comment:描述"`
	SchedulingStyle          uint32 `gorm:"type:int;not null; default:0; comment:调度方式，0手动调度，1定时调度"`
	TimingConfigCron         string `gorm:"size:50; comment:调度周期cron表达式"`
	TimingConfigScheduleType uint32 `gorm:"type:int;not null; default:0; comment:定时调度设置，0基础 1高级"`
	WorkflowAdvancedConfig   string `gorm:"type:TEXT; comment:工作流高级配置json"`
}

type Metric struct {
	Model
	RunId string `gorm:"size:50; not null; comment:run ID"`
	NodeId string `gorm:"size:50; not null; comment:node ID"`
	Key string `gorm:"size:50; not null; comment:key"`
	Value string `gorm:"type:TEXT; not null; comment:value"`
}

func (dao *CommonDAO) AutoMigrate() {
	dao.db.AutoMigrate(&Pipeline{})
	dao.db.AutoMigrate(&PipelineVersion{})
	dao.db.AutoMigrate(&Metric{})
}
