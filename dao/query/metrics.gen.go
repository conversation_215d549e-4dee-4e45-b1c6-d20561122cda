// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/mlops/pipeline/dao/model"
)

func newMetric(db *gorm.DB, opts ...gen.DOOption) metric {
	_metric := metric{}

	_metric.metricDo.UseDB(db, opts...)
	_metric.metricDo.UseModel(&model.Metric{})

	tableName := _metric.metricDo.TableName()
	_metric.ALL = field.NewAsterisk(tableName)
	_metric.ID = field.NewString(tableName, "id")
	_metric.CreateTime = field.NewTime(tableName, "create_time")
	_metric.UpdateTime = field.NewTime(tableName, "update_time")
	_metric.RunID = field.NewString(tableName, "run_id")
	_metric.NodeID = field.NewString(tableName, "node_id")
	_metric.Key = field.NewString(tableName, "key")
	_metric.Value = field.NewString(tableName, "value")

	_metric.fillFieldMap()

	return _metric
}

type metric struct {
	metricDo

	ALL        field.Asterisk
	ID         field.String // ID
	CreateTime field.Time   // 创建时间
	UpdateTime field.Time   // 更新时间
	RunID      field.String // run ID
	NodeID     field.String // node ID
	Key        field.String // key
	Value      field.String // value

	fieldMap map[string]field.Expr
}

func (m metric) Table(newTableName string) *metric {
	m.metricDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m metric) As(alias string) *metric {
	m.metricDo.DO = *(m.metricDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *metric) updateTableName(table string) *metric {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewString(table, "id")
	m.CreateTime = field.NewTime(table, "create_time")
	m.UpdateTime = field.NewTime(table, "update_time")
	m.RunID = field.NewString(table, "run_id")
	m.NodeID = field.NewString(table, "node_id")
	m.Key = field.NewString(table, "key")
	m.Value = field.NewString(table, "value")

	m.fillFieldMap()

	return m
}

func (m *metric) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *metric) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 7)
	m.fieldMap["id"] = m.ID
	m.fieldMap["create_time"] = m.CreateTime
	m.fieldMap["update_time"] = m.UpdateTime
	m.fieldMap["run_id"] = m.RunID
	m.fieldMap["node_id"] = m.NodeID
	m.fieldMap["key"] = m.Key
	m.fieldMap["value"] = m.Value
}

func (m metric) clone(db *gorm.DB) metric {
	m.metricDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m metric) replaceDB(db *gorm.DB) metric {
	m.metricDo.ReplaceDB(db)
	return m
}

type metricDo struct{ gen.DO }

func (m metricDo) Debug() *metricDo {
	return m.withDO(m.DO.Debug())
}

func (m metricDo) WithContext(ctx context.Context) *metricDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m metricDo) ReadDB() *metricDo {
	return m.Clauses(dbresolver.Read)
}

func (m metricDo) WriteDB() *metricDo {
	return m.Clauses(dbresolver.Write)
}

func (m metricDo) Session(config *gorm.Session) *metricDo {
	return m.withDO(m.DO.Session(config))
}

func (m metricDo) Clauses(conds ...clause.Expression) *metricDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m metricDo) Returning(value interface{}, columns ...string) *metricDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m metricDo) Not(conds ...gen.Condition) *metricDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m metricDo) Or(conds ...gen.Condition) *metricDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m metricDo) Select(conds ...field.Expr) *metricDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m metricDo) Where(conds ...gen.Condition) *metricDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m metricDo) Order(conds ...field.Expr) *metricDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m metricDo) Distinct(cols ...field.Expr) *metricDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m metricDo) Omit(cols ...field.Expr) *metricDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m metricDo) Join(table schema.Tabler, on ...field.Expr) *metricDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m metricDo) LeftJoin(table schema.Tabler, on ...field.Expr) *metricDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m metricDo) RightJoin(table schema.Tabler, on ...field.Expr) *metricDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m metricDo) Group(cols ...field.Expr) *metricDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m metricDo) Having(conds ...gen.Condition) *metricDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m metricDo) Limit(limit int) *metricDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m metricDo) Offset(offset int) *metricDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m metricDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *metricDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m metricDo) Unscoped() *metricDo {
	return m.withDO(m.DO.Unscoped())
}

func (m metricDo) Create(values ...*model.Metric) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m metricDo) CreateInBatches(values []*model.Metric, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m metricDo) Save(values ...*model.Metric) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m metricDo) First() (*model.Metric, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Metric), nil
	}
}

func (m metricDo) Take() (*model.Metric, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Metric), nil
	}
}

func (m metricDo) Last() (*model.Metric, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Metric), nil
	}
}

func (m metricDo) Find() ([]*model.Metric, error) {
	result, err := m.DO.Find()
	return result.([]*model.Metric), err
}

func (m metricDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Metric, err error) {
	buf := make([]*model.Metric, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m metricDo) FindInBatches(result *[]*model.Metric, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m metricDo) Attrs(attrs ...field.AssignExpr) *metricDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m metricDo) Assign(attrs ...field.AssignExpr) *metricDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m metricDo) Joins(fields ...field.RelationField) *metricDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m metricDo) Preload(fields ...field.RelationField) *metricDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m metricDo) FirstOrInit() (*model.Metric, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Metric), nil
	}
}

func (m metricDo) FirstOrCreate() (*model.Metric, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Metric), nil
	}
}

func (m metricDo) FindByPage(offset int, limit int) (result []*model.Metric, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m metricDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m metricDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m metricDo) Delete(models ...*model.Metric) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *metricDo) withDO(do gen.Dao) *metricDo {
	m.DO = *do.(*gen.DO)
	return m
}
