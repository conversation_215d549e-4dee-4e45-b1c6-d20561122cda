// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/mlops/pipeline/dao/model"
)

func newExperiment(db *gorm.DB, opts ...gen.DOOption) experiment {
	_experiment := experiment{}

	_experiment.experimentDo.UseDB(db, opts...)
	_experiment.experimentDo.UseModel(&model.Experiment{})

	tableName := _experiment.experimentDo.TableName()
	_experiment.ALL = field.NewAsterisk(tableName)
	_experiment.UUID = field.NewString(tableName, "UUID")
	_experiment.Name = field.NewString(tableName, "Name")
	_experiment.Description = field.NewString(tableName, "Description")
	_experiment.CreatedAtInSec = field.NewInt64(tableName, "CreatedAtInSec")
	_experiment.Namespace = field.NewString(tableName, "Namespace")
	_experiment.StorageState = field.NewString(tableName, "StorageState")

	_experiment.fillFieldMap()

	return _experiment
}

type experiment struct {
	experimentDo

	ALL            field.Asterisk
	UUID           field.String
	Name           field.String
	Description    field.String
	CreatedAtInSec field.Int64
	Namespace      field.String
	StorageState   field.String

	fieldMap map[string]field.Expr
}

func (e experiment) Table(newTableName string) *experiment {
	e.experimentDo.UseTable(newTableName)
	return e.updateTableName(newTableName)
}

func (e experiment) As(alias string) *experiment {
	e.experimentDo.DO = *(e.experimentDo.As(alias).(*gen.DO))
	return e.updateTableName(alias)
}

func (e *experiment) updateTableName(table string) *experiment {
	e.ALL = field.NewAsterisk(table)
	e.UUID = field.NewString(table, "UUID")
	e.Name = field.NewString(table, "Name")
	e.Description = field.NewString(table, "Description")
	e.CreatedAtInSec = field.NewInt64(table, "CreatedAtInSec")
	e.Namespace = field.NewString(table, "Namespace")
	e.StorageState = field.NewString(table, "StorageState")

	e.fillFieldMap()

	return e
}

func (e *experiment) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := e.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (e *experiment) fillFieldMap() {
	e.fieldMap = make(map[string]field.Expr, 6)
	e.fieldMap["UUID"] = e.UUID
	e.fieldMap["Name"] = e.Name
	e.fieldMap["Description"] = e.Description
	e.fieldMap["CreatedAtInSec"] = e.CreatedAtInSec
	e.fieldMap["Namespace"] = e.Namespace
	e.fieldMap["StorageState"] = e.StorageState
}

func (e experiment) clone(db *gorm.DB) experiment {
	e.experimentDo.ReplaceConnPool(db.Statement.ConnPool)
	return e
}

func (e experiment) replaceDB(db *gorm.DB) experiment {
	e.experimentDo.ReplaceDB(db)
	return e
}

type experimentDo struct{ gen.DO }

func (e experimentDo) Debug() *experimentDo {
	return e.withDO(e.DO.Debug())
}

func (e experimentDo) WithContext(ctx context.Context) *experimentDo {
	return e.withDO(e.DO.WithContext(ctx))
}

func (e experimentDo) ReadDB() *experimentDo {
	return e.Clauses(dbresolver.Read)
}

func (e experimentDo) WriteDB() *experimentDo {
	return e.Clauses(dbresolver.Write)
}

func (e experimentDo) Session(config *gorm.Session) *experimentDo {
	return e.withDO(e.DO.Session(config))
}

func (e experimentDo) Clauses(conds ...clause.Expression) *experimentDo {
	return e.withDO(e.DO.Clauses(conds...))
}

func (e experimentDo) Returning(value interface{}, columns ...string) *experimentDo {
	return e.withDO(e.DO.Returning(value, columns...))
}

func (e experimentDo) Not(conds ...gen.Condition) *experimentDo {
	return e.withDO(e.DO.Not(conds...))
}

func (e experimentDo) Or(conds ...gen.Condition) *experimentDo {
	return e.withDO(e.DO.Or(conds...))
}

func (e experimentDo) Select(conds ...field.Expr) *experimentDo {
	return e.withDO(e.DO.Select(conds...))
}

func (e experimentDo) Where(conds ...gen.Condition) *experimentDo {
	return e.withDO(e.DO.Where(conds...))
}

func (e experimentDo) Order(conds ...field.Expr) *experimentDo {
	return e.withDO(e.DO.Order(conds...))
}

func (e experimentDo) Distinct(cols ...field.Expr) *experimentDo {
	return e.withDO(e.DO.Distinct(cols...))
}

func (e experimentDo) Omit(cols ...field.Expr) *experimentDo {
	return e.withDO(e.DO.Omit(cols...))
}

func (e experimentDo) Join(table schema.Tabler, on ...field.Expr) *experimentDo {
	return e.withDO(e.DO.Join(table, on...))
}

func (e experimentDo) LeftJoin(table schema.Tabler, on ...field.Expr) *experimentDo {
	return e.withDO(e.DO.LeftJoin(table, on...))
}

func (e experimentDo) RightJoin(table schema.Tabler, on ...field.Expr) *experimentDo {
	return e.withDO(e.DO.RightJoin(table, on...))
}

func (e experimentDo) Group(cols ...field.Expr) *experimentDo {
	return e.withDO(e.DO.Group(cols...))
}

func (e experimentDo) Having(conds ...gen.Condition) *experimentDo {
	return e.withDO(e.DO.Having(conds...))
}

func (e experimentDo) Limit(limit int) *experimentDo {
	return e.withDO(e.DO.Limit(limit))
}

func (e experimentDo) Offset(offset int) *experimentDo {
	return e.withDO(e.DO.Offset(offset))
}

func (e experimentDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *experimentDo {
	return e.withDO(e.DO.Scopes(funcs...))
}

func (e experimentDo) Unscoped() *experimentDo {
	return e.withDO(e.DO.Unscoped())
}

func (e experimentDo) Create(values ...*model.Experiment) error {
	if len(values) == 0 {
		return nil
	}
	return e.DO.Create(values)
}

func (e experimentDo) CreateInBatches(values []*model.Experiment, batchSize int) error {
	return e.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (e experimentDo) Save(values ...*model.Experiment) error {
	if len(values) == 0 {
		return nil
	}
	return e.DO.Save(values)
}

func (e experimentDo) First() (*model.Experiment, error) {
	if result, err := e.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Experiment), nil
	}
}

func (e experimentDo) Take() (*model.Experiment, error) {
	if result, err := e.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Experiment), nil
	}
}

func (e experimentDo) Last() (*model.Experiment, error) {
	if result, err := e.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Experiment), nil
	}
}

func (e experimentDo) Find() ([]*model.Experiment, error) {
	result, err := e.DO.Find()
	return result.([]*model.Experiment), err
}

func (e experimentDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Experiment, err error) {
	buf := make([]*model.Experiment, 0, batchSize)
	err = e.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (e experimentDo) FindInBatches(result *[]*model.Experiment, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return e.DO.FindInBatches(result, batchSize, fc)
}

func (e experimentDo) Attrs(attrs ...field.AssignExpr) *experimentDo {
	return e.withDO(e.DO.Attrs(attrs...))
}

func (e experimentDo) Assign(attrs ...field.AssignExpr) *experimentDo {
	return e.withDO(e.DO.Assign(attrs...))
}

func (e experimentDo) Joins(fields ...field.RelationField) *experimentDo {
	for _, _f := range fields {
		e = *e.withDO(e.DO.Joins(_f))
	}
	return &e
}

func (e experimentDo) Preload(fields ...field.RelationField) *experimentDo {
	for _, _f := range fields {
		e = *e.withDO(e.DO.Preload(_f))
	}
	return &e
}

func (e experimentDo) FirstOrInit() (*model.Experiment, error) {
	if result, err := e.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Experiment), nil
	}
}

func (e experimentDo) FirstOrCreate() (*model.Experiment, error) {
	if result, err := e.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Experiment), nil
	}
}

func (e experimentDo) FindByPage(offset int, limit int) (result []*model.Experiment, count int64, err error) {
	result, err = e.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = e.Offset(-1).Limit(-1).Count()
	return
}

func (e experimentDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = e.Count()
	if err != nil {
		return
	}

	err = e.Offset(offset).Limit(limit).Scan(result)
	return
}

func (e experimentDo) Scan(result interface{}) (err error) {
	return e.DO.Scan(result)
}

func (e experimentDo) Delete(models ...*model.Experiment) (result gen.ResultInfo, err error) {
	return e.DO.Delete(models)
}

func (e *experimentDo) withDO(do gen.Dao) *experimentDo {
	e.DO = *do.(*gen.DO)
	return e
}
