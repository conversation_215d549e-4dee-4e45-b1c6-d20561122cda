// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/mlops/pipeline/dao/model"
)

func newResourceReference(db *gorm.DB, opts ...gen.DOOption) resourceReference {
	_resourceReference := resourceReference{}

	_resourceReference.resourceReferenceDo.UseDB(db, opts...)
	_resourceReference.resourceReferenceDo.UseModel(&model.ResourceReference{})

	tableName := _resourceReference.resourceReferenceDo.TableName()
	_resourceReference.ALL = field.NewAsterisk(tableName)
	_resourceReference.ResourceUUID = field.NewString(tableName, "ResourceUUID")
	_resourceReference.ResourceType = field.NewString(tableName, "ResourceType")
	_resourceReference.ReferenceUUID = field.NewString(tableName, "ReferenceUUID")
	_resourceReference.ReferenceName = field.NewString(tableName, "ReferenceName")
	_resourceReference.ReferenceType = field.NewString(tableName, "ReferenceType")
	_resourceReference.Relationship = field.NewString(tableName, "Relationship")
	_resourceReference.Payload = field.NewString(tableName, "Payload")

	_resourceReference.fillFieldMap()

	return _resourceReference
}

type resourceReference struct {
	resourceReferenceDo

	ALL           field.Asterisk
	ResourceUUID  field.String
	ResourceType  field.String
	ReferenceUUID field.String
	ReferenceName field.String
	ReferenceType field.String
	Relationship  field.String
	Payload       field.String

	fieldMap map[string]field.Expr
}

func (r resourceReference) Table(newTableName string) *resourceReference {
	r.resourceReferenceDo.UseTable(newTableName)
	return r.updateTableName(newTableName)
}

func (r resourceReference) As(alias string) *resourceReference {
	r.resourceReferenceDo.DO = *(r.resourceReferenceDo.As(alias).(*gen.DO))
	return r.updateTableName(alias)
}

func (r *resourceReference) updateTableName(table string) *resourceReference {
	r.ALL = field.NewAsterisk(table)
	r.ResourceUUID = field.NewString(table, "ResourceUUID")
	r.ResourceType = field.NewString(table, "ResourceType")
	r.ReferenceUUID = field.NewString(table, "ReferenceUUID")
	r.ReferenceName = field.NewString(table, "ReferenceName")
	r.ReferenceType = field.NewString(table, "ReferenceType")
	r.Relationship = field.NewString(table, "Relationship")
	r.Payload = field.NewString(table, "Payload")

	r.fillFieldMap()

	return r
}

func (r *resourceReference) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := r.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (r *resourceReference) fillFieldMap() {
	r.fieldMap = make(map[string]field.Expr, 7)
	r.fieldMap["ResourceUUID"] = r.ResourceUUID
	r.fieldMap["ResourceType"] = r.ResourceType
	r.fieldMap["ReferenceUUID"] = r.ReferenceUUID
	r.fieldMap["ReferenceName"] = r.ReferenceName
	r.fieldMap["ReferenceType"] = r.ReferenceType
	r.fieldMap["Relationship"] = r.Relationship
	r.fieldMap["Payload"] = r.Payload
}

func (r resourceReference) clone(db *gorm.DB) resourceReference {
	r.resourceReferenceDo.ReplaceConnPool(db.Statement.ConnPool)
	return r
}

func (r resourceReference) replaceDB(db *gorm.DB) resourceReference {
	r.resourceReferenceDo.ReplaceDB(db)
	return r
}

type resourceReferenceDo struct{ gen.DO }

func (r resourceReferenceDo) Debug() *resourceReferenceDo {
	return r.withDO(r.DO.Debug())
}

func (r resourceReferenceDo) WithContext(ctx context.Context) *resourceReferenceDo {
	return r.withDO(r.DO.WithContext(ctx))
}

func (r resourceReferenceDo) ReadDB() *resourceReferenceDo {
	return r.Clauses(dbresolver.Read)
}

func (r resourceReferenceDo) WriteDB() *resourceReferenceDo {
	return r.Clauses(dbresolver.Write)
}

func (r resourceReferenceDo) Session(config *gorm.Session) *resourceReferenceDo {
	return r.withDO(r.DO.Session(config))
}

func (r resourceReferenceDo) Clauses(conds ...clause.Expression) *resourceReferenceDo {
	return r.withDO(r.DO.Clauses(conds...))
}

func (r resourceReferenceDo) Returning(value interface{}, columns ...string) *resourceReferenceDo {
	return r.withDO(r.DO.Returning(value, columns...))
}

func (r resourceReferenceDo) Not(conds ...gen.Condition) *resourceReferenceDo {
	return r.withDO(r.DO.Not(conds...))
}

func (r resourceReferenceDo) Or(conds ...gen.Condition) *resourceReferenceDo {
	return r.withDO(r.DO.Or(conds...))
}

func (r resourceReferenceDo) Select(conds ...field.Expr) *resourceReferenceDo {
	return r.withDO(r.DO.Select(conds...))
}

func (r resourceReferenceDo) Where(conds ...gen.Condition) *resourceReferenceDo {
	return r.withDO(r.DO.Where(conds...))
}

func (r resourceReferenceDo) Order(conds ...field.Expr) *resourceReferenceDo {
	return r.withDO(r.DO.Order(conds...))
}

func (r resourceReferenceDo) Distinct(cols ...field.Expr) *resourceReferenceDo {
	return r.withDO(r.DO.Distinct(cols...))
}

func (r resourceReferenceDo) Omit(cols ...field.Expr) *resourceReferenceDo {
	return r.withDO(r.DO.Omit(cols...))
}

func (r resourceReferenceDo) Join(table schema.Tabler, on ...field.Expr) *resourceReferenceDo {
	return r.withDO(r.DO.Join(table, on...))
}

func (r resourceReferenceDo) LeftJoin(table schema.Tabler, on ...field.Expr) *resourceReferenceDo {
	return r.withDO(r.DO.LeftJoin(table, on...))
}

func (r resourceReferenceDo) RightJoin(table schema.Tabler, on ...field.Expr) *resourceReferenceDo {
	return r.withDO(r.DO.RightJoin(table, on...))
}

func (r resourceReferenceDo) Group(cols ...field.Expr) *resourceReferenceDo {
	return r.withDO(r.DO.Group(cols...))
}

func (r resourceReferenceDo) Having(conds ...gen.Condition) *resourceReferenceDo {
	return r.withDO(r.DO.Having(conds...))
}

func (r resourceReferenceDo) Limit(limit int) *resourceReferenceDo {
	return r.withDO(r.DO.Limit(limit))
}

func (r resourceReferenceDo) Offset(offset int) *resourceReferenceDo {
	return r.withDO(r.DO.Offset(offset))
}

func (r resourceReferenceDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *resourceReferenceDo {
	return r.withDO(r.DO.Scopes(funcs...))
}

func (r resourceReferenceDo) Unscoped() *resourceReferenceDo {
	return r.withDO(r.DO.Unscoped())
}

func (r resourceReferenceDo) Create(values ...*model.ResourceReference) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Create(values)
}

func (r resourceReferenceDo) CreateInBatches(values []*model.ResourceReference, batchSize int) error {
	return r.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (r resourceReferenceDo) Save(values ...*model.ResourceReference) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Save(values)
}

func (r resourceReferenceDo) First() (*model.ResourceReference, error) {
	if result, err := r.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResourceReference), nil
	}
}

func (r resourceReferenceDo) Take() (*model.ResourceReference, error) {
	if result, err := r.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResourceReference), nil
	}
}

func (r resourceReferenceDo) Last() (*model.ResourceReference, error) {
	if result, err := r.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResourceReference), nil
	}
}

func (r resourceReferenceDo) Find() ([]*model.ResourceReference, error) {
	result, err := r.DO.Find()
	return result.([]*model.ResourceReference), err
}

func (r resourceReferenceDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ResourceReference, err error) {
	buf := make([]*model.ResourceReference, 0, batchSize)
	err = r.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (r resourceReferenceDo) FindInBatches(result *[]*model.ResourceReference, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return r.DO.FindInBatches(result, batchSize, fc)
}

func (r resourceReferenceDo) Attrs(attrs ...field.AssignExpr) *resourceReferenceDo {
	return r.withDO(r.DO.Attrs(attrs...))
}

func (r resourceReferenceDo) Assign(attrs ...field.AssignExpr) *resourceReferenceDo {
	return r.withDO(r.DO.Assign(attrs...))
}

func (r resourceReferenceDo) Joins(fields ...field.RelationField) *resourceReferenceDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Joins(_f))
	}
	return &r
}

func (r resourceReferenceDo) Preload(fields ...field.RelationField) *resourceReferenceDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Preload(_f))
	}
	return &r
}

func (r resourceReferenceDo) FirstOrInit() (*model.ResourceReference, error) {
	if result, err := r.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResourceReference), nil
	}
}

func (r resourceReferenceDo) FirstOrCreate() (*model.ResourceReference, error) {
	if result, err := r.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResourceReference), nil
	}
}

func (r resourceReferenceDo) FindByPage(offset int, limit int) (result []*model.ResourceReference, count int64, err error) {
	result, err = r.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = r.Offset(-1).Limit(-1).Count()
	return
}

func (r resourceReferenceDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = r.Count()
	if err != nil {
		return
	}

	err = r.Offset(offset).Limit(limit).Scan(result)
	return
}

func (r resourceReferenceDo) Scan(result interface{}) (err error) {
	return r.DO.Scan(result)
}

func (r resourceReferenceDo) Delete(models ...*model.ResourceReference) (result gen.ResultInfo, err error) {
	return r.DO.Delete(models)
}

func (r *resourceReferenceDo) withDO(do gen.Dao) *resourceReferenceDo {
	r.DO = *do.(*gen.DO)
	return r
}
