// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"transwarp.io/mlops/pipeline/dao/model"
)

func newJob(db *gorm.DB, opts ...gen.DOOption) job {
	_job := job{}

	_job.jobDo.UseDB(db, opts...)
	_job.jobDo.UseModel(&model.Job{})

	tableName := _job.jobDo.TableName()
	_job.ALL = field.NewAsterisk(tableName)
	_job.UUID = field.NewString(tableName, "UUID")
	_job.DisplayName = field.NewString(tableName, "DisplayName")
	_job.Name = field.NewString(tableName, "Name")
	_job.Namespace = field.NewString(tableName, "Namespace")
	_job.ServiceAccount = field.NewString(tableName, "ServiceAccount")
	_job.Description = field.NewString(tableName, "Description")
	_job.MaxConcurrency = field.NewInt64(tableName, "MaxConcurrency")
	_job.NoCatchup = field.NewBool(tableName, "NoCatchup")
	_job.CreatedAtInSec = field.NewInt64(tableName, "CreatedAtInSec")
	_job.UpdatedAtInSec = field.NewInt64(tableName, "UpdatedAtInSec")
	_job.Enabled = field.NewBool(tableName, "Enabled")
	_job.CronScheduleStartTimeInSec = field.NewInt64(tableName, "CronScheduleStartTimeInSec")
	_job.CronScheduleEndTimeInSec = field.NewInt64(tableName, "CronScheduleEndTimeInSec")
	_job.Schedule = field.NewString(tableName, "Schedule")
	_job.PeriodicScheduleStartTimeInSec = field.NewInt64(tableName, "PeriodicScheduleStartTimeInSec")
	_job.PeriodicScheduleEndTimeInSec = field.NewInt64(tableName, "PeriodicScheduleEndTimeInSec")
	_job.IntervalSecond = field.NewInt64(tableName, "IntervalSecond")
	_job.PipelineID = field.NewString(tableName, "PipelineId")
	_job.PipelineName = field.NewString(tableName, "PipelineName")
	_job.PipelineSpecManifest = field.NewString(tableName, "PipelineSpecManifest")
	_job.WorkflowSpecManifest = field.NewString(tableName, "WorkflowSpecManifest")
	_job.Parameters = field.NewString(tableName, "Parameters")
	_job.Conditions = field.NewString(tableName, "Conditions")

	_job.fillFieldMap()

	return _job
}

type job struct {
	jobDo

	ALL                            field.Asterisk
	UUID                           field.String
	DisplayName                    field.String
	Name                           field.String
	Namespace                      field.String
	ServiceAccount                 field.String
	Description                    field.String
	MaxConcurrency                 field.Int64
	NoCatchup                      field.Bool
	CreatedAtInSec                 field.Int64
	UpdatedAtInSec                 field.Int64
	Enabled                        field.Bool
	CronScheduleStartTimeInSec     field.Int64
	CronScheduleEndTimeInSec       field.Int64
	Schedule                       field.String
	PeriodicScheduleStartTimeInSec field.Int64
	PeriodicScheduleEndTimeInSec   field.Int64
	IntervalSecond                 field.Int64
	PipelineID                     field.String
	PipelineName                   field.String
	PipelineSpecManifest           field.String
	WorkflowSpecManifest           field.String
	Parameters                     field.String
	Conditions                     field.String

	fieldMap map[string]field.Expr
}

func (j job) Table(newTableName string) *job {
	j.jobDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j job) As(alias string) *job {
	j.jobDo.DO = *(j.jobDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *job) updateTableName(table string) *job {
	j.ALL = field.NewAsterisk(table)
	j.UUID = field.NewString(table, "UUID")
	j.DisplayName = field.NewString(table, "DisplayName")
	j.Name = field.NewString(table, "Name")
	j.Namespace = field.NewString(table, "Namespace")
	j.ServiceAccount = field.NewString(table, "ServiceAccount")
	j.Description = field.NewString(table, "Description")
	j.MaxConcurrency = field.NewInt64(table, "MaxConcurrency")
	j.NoCatchup = field.NewBool(table, "NoCatchup")
	j.CreatedAtInSec = field.NewInt64(table, "CreatedAtInSec")
	j.UpdatedAtInSec = field.NewInt64(table, "UpdatedAtInSec")
	j.Enabled = field.NewBool(table, "Enabled")
	j.CronScheduleStartTimeInSec = field.NewInt64(table, "CronScheduleStartTimeInSec")
	j.CronScheduleEndTimeInSec = field.NewInt64(table, "CronScheduleEndTimeInSec")
	j.Schedule = field.NewString(table, "Schedule")
	j.PeriodicScheduleStartTimeInSec = field.NewInt64(table, "PeriodicScheduleStartTimeInSec")
	j.PeriodicScheduleEndTimeInSec = field.NewInt64(table, "PeriodicScheduleEndTimeInSec")
	j.IntervalSecond = field.NewInt64(table, "IntervalSecond")
	j.PipelineID = field.NewString(table, "PipelineId")
	j.PipelineName = field.NewString(table, "PipelineName")
	j.PipelineSpecManifest = field.NewString(table, "PipelineSpecManifest")
	j.WorkflowSpecManifest = field.NewString(table, "WorkflowSpecManifest")
	j.Parameters = field.NewString(table, "Parameters")
	j.Conditions = field.NewString(table, "Conditions")

	j.fillFieldMap()

	return j
}

func (j *job) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *job) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 23)
	j.fieldMap["UUID"] = j.UUID
	j.fieldMap["DisplayName"] = j.DisplayName
	j.fieldMap["Name"] = j.Name
	j.fieldMap["Namespace"] = j.Namespace
	j.fieldMap["ServiceAccount"] = j.ServiceAccount
	j.fieldMap["Description"] = j.Description
	j.fieldMap["MaxConcurrency"] = j.MaxConcurrency
	j.fieldMap["NoCatchup"] = j.NoCatchup
	j.fieldMap["CreatedAtInSec"] = j.CreatedAtInSec
	j.fieldMap["UpdatedAtInSec"] = j.UpdatedAtInSec
	j.fieldMap["Enabled"] = j.Enabled
	j.fieldMap["CronScheduleStartTimeInSec"] = j.CronScheduleStartTimeInSec
	j.fieldMap["CronScheduleEndTimeInSec"] = j.CronScheduleEndTimeInSec
	j.fieldMap["Schedule"] = j.Schedule
	j.fieldMap["PeriodicScheduleStartTimeInSec"] = j.PeriodicScheduleStartTimeInSec
	j.fieldMap["PeriodicScheduleEndTimeInSec"] = j.PeriodicScheduleEndTimeInSec
	j.fieldMap["IntervalSecond"] = j.IntervalSecond
	j.fieldMap["PipelineId"] = j.PipelineID
	j.fieldMap["PipelineName"] = j.PipelineName
	j.fieldMap["PipelineSpecManifest"] = j.PipelineSpecManifest
	j.fieldMap["WorkflowSpecManifest"] = j.WorkflowSpecManifest
	j.fieldMap["Parameters"] = j.Parameters
	j.fieldMap["Conditions"] = j.Conditions
}

func (j job) clone(db *gorm.DB) job {
	j.jobDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j job) replaceDB(db *gorm.DB) job {
	j.jobDo.ReplaceDB(db)
	return j
}

type jobDo struct{ gen.DO }

func (j jobDo) Debug() *jobDo {
	return j.withDO(j.DO.Debug())
}

func (j jobDo) WithContext(ctx context.Context) *jobDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jobDo) ReadDB() *jobDo {
	return j.Clauses(dbresolver.Read)
}

func (j jobDo) WriteDB() *jobDo {
	return j.Clauses(dbresolver.Write)
}

func (j jobDo) Session(config *gorm.Session) *jobDo {
	return j.withDO(j.DO.Session(config))
}

func (j jobDo) Clauses(conds ...clause.Expression) *jobDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jobDo) Returning(value interface{}, columns ...string) *jobDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jobDo) Not(conds ...gen.Condition) *jobDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jobDo) Or(conds ...gen.Condition) *jobDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jobDo) Select(conds ...field.Expr) *jobDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jobDo) Where(conds ...gen.Condition) *jobDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jobDo) Order(conds ...field.Expr) *jobDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jobDo) Distinct(cols ...field.Expr) *jobDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jobDo) Omit(cols ...field.Expr) *jobDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jobDo) Join(table schema.Tabler, on ...field.Expr) *jobDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jobDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jobDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jobDo) RightJoin(table schema.Tabler, on ...field.Expr) *jobDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jobDo) Group(cols ...field.Expr) *jobDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jobDo) Having(conds ...gen.Condition) *jobDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jobDo) Limit(limit int) *jobDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jobDo) Offset(offset int) *jobDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jobDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jobDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jobDo) Unscoped() *jobDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jobDo) Create(values ...*model.Job) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jobDo) CreateInBatches(values []*model.Job, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jobDo) Save(values ...*model.Job) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jobDo) First() (*model.Job, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Job), nil
	}
}

func (j jobDo) Take() (*model.Job, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Job), nil
	}
}

func (j jobDo) Last() (*model.Job, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Job), nil
	}
}

func (j jobDo) Find() ([]*model.Job, error) {
	result, err := j.DO.Find()
	return result.([]*model.Job), err
}

func (j jobDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Job, err error) {
	buf := make([]*model.Job, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jobDo) FindInBatches(result *[]*model.Job, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jobDo) Attrs(attrs ...field.AssignExpr) *jobDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jobDo) Assign(attrs ...field.AssignExpr) *jobDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jobDo) Joins(fields ...field.RelationField) *jobDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jobDo) Preload(fields ...field.RelationField) *jobDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jobDo) FirstOrInit() (*model.Job, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Job), nil
	}
}

func (j jobDo) FirstOrCreate() (*model.Job, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Job), nil
	}
}

func (j jobDo) FindByPage(offset int, limit int) (result []*model.Job, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jobDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jobDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jobDo) Delete(models ...*model.Job) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jobDo) withDO(do gen.Dao) *jobDo {
	j.DO = *do.(*gen.DO)
	return j
}
