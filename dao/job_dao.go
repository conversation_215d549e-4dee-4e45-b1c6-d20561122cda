package dao

import (
	"context"

	"transwarp.io/mlops/pipeline/dao/model"
)

type JobDA<PERSON> struct {
	*CommonDAO
}

func NewJobDAO(dao *CommonDAO) *JobDAO {
	jobDAO := &JobDAO{
		dao,
	}
	return jobDAO
}

func (dao *JobDAO) ListByName(ctx context.Context, names ...string) ([]*model.Job, error) {
	q := dao.Job
	return q.Where(q.DisplayName.In(names...)).Find()
}

func (dao *JobDAO) ListByNameAndEnabled(ctx context.Context, id string, enabled bool) ([]*model.Job, error) {
	q := dao.Job
	return q.Where(q.DisplayName.Eq(id), q.Enabled.Is(enabled)).Find()
}

func (dao *JobDAO) ListByEnabled(ctx context.Context, enabled bool) ([]*model.Job, error) {
	q := dao.Job
	return q.Where(q.Enabled.Is(enabled)).Find()
}

func (dao *JobDAO) Delete(ctx context.Context, id string) error {
	q := dao.Job
	_, err := q.Where(q.UUID.Eq(id)).Delete(&model.Job{})
	return err
}

type JobRunningTaskCount struct {
	JobId            string
	RunningTaskCount int64
}

func (dao *JobDAO) CountRunningRun() (map[string]int64, error) {
	jobRunningTaskCount := &[]JobRunningTaskCount{}
	run := dao.Run
	rf := dao.ResourceReference
	err := rf.Select(rf.ReferenceUUID.As("job_id"), run.UUID.Count().As("running_task_count")).
		LeftJoin(run, run.UUID.EqCol(rf.ResourceUUID)).
		Where(rf.ReferenceType.Eq("Job"), rf.ResourceType.Eq("Run"), run.FinishedAtInSec.Eq(0)).
		Group(rf.ReferenceUUID).
		Scan(jobRunningTaskCount)
	if err != nil {
		return nil, err
	}
	result := map[string]int64{}
	for _, kv := range *jobRunningTaskCount {
		result[kv.JobId] = kv.RunningTaskCount
	}
	return result, err
}
