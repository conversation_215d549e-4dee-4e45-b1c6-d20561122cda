// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameMetric = "metrics"

// Metric mapped from table <metrics>
type Metric struct {
	ID         string    `gorm:"column:id;primaryKey;comment:ID" json:"id"`                                    // ID
	CreateTime time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"` // 创建时间
	UpdateTime time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"` // 更新时间
	RunID      string    `gorm:"column:run_id;not null;comment:run ID" json:"run_id"`                          // run ID
	NodeID     string    `gorm:"column:node_id;not null;comment:node ID" json:"node_id"`                       // node ID
	Key        string    `gorm:"column:key;not null;comment:key" json:"key"`                                   // key
	Value      string    `gorm:"column:value;not null;comment:value" json:"value"`                             // value
}

// TableName Metric's table name
func (*Metric) TableName() string {
	return TableNameMetric
}
