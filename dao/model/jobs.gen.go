// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameJob = "jobs"

// Job mapped from table <jobs>
type Job struct {
	UUID                           string `gorm:"column:UUID;primaryKey" json:"UUID"`
	DisplayName                    string `gorm:"column:DisplayName;not null" json:"DisplayName"`
	Name                           string `gorm:"column:Name;not null" json:"Name"`
	Namespace                      string `gorm:"column:Namespace;not null" json:"Namespace"`
	ServiceAccount                 string `gorm:"column:ServiceAccount;not null" json:"ServiceAccount"`
	Description                    string `gorm:"column:Description;not null" json:"Description"`
	MaxConcurrency                 int64  `gorm:"column:MaxConcurrency;not null" json:"MaxConcurrency"`
	NoCatchup                      bool   `gorm:"column:NoCatchup;not null" json:"NoCatchup"`
	CreatedAtInSec                 int64  `gorm:"column:CreatedAtInSec;not null" json:"CreatedAtInSec"`
	UpdatedAtInSec                 int64  `gorm:"column:UpdatedAtInSec;not null" json:"UpdatedAtInSec"`
	Enabled                        bool   `gorm:"column:Enabled;not null" json:"Enabled"`
	CronScheduleStartTimeInSec     int64  `gorm:"column:CronScheduleStartTimeInSec" json:"CronScheduleStartTimeInSec"`
	CronScheduleEndTimeInSec       int64  `gorm:"column:CronScheduleEndTimeInSec" json:"CronScheduleEndTimeInSec"`
	Schedule                       string `gorm:"column:Schedule" json:"Schedule"`
	PeriodicScheduleStartTimeInSec int64  `gorm:"column:PeriodicScheduleStartTimeInSec" json:"PeriodicScheduleStartTimeInSec"`
	PeriodicScheduleEndTimeInSec   int64  `gorm:"column:PeriodicScheduleEndTimeInSec" json:"PeriodicScheduleEndTimeInSec"`
	IntervalSecond                 int64  `gorm:"column:IntervalSecond" json:"IntervalSecond"`
	PipelineID                     string `gorm:"column:PipelineId;not null" json:"PipelineId"`
	PipelineName                   string `gorm:"column:PipelineName;not null" json:"PipelineName"`
	PipelineSpecManifest           string `gorm:"column:PipelineSpecManifest" json:"PipelineSpecManifest"`
	WorkflowSpecManifest           string `gorm:"column:WorkflowSpecManifest;not null" json:"WorkflowSpecManifest"`
	Parameters                     string `gorm:"column:Parameters" json:"Parameters"`
	Conditions                     string `gorm:"column:Conditions;not null" json:"Conditions"`
}

// TableName Job's table name
func (*Job) TableName() string {
	return TableNameJob
}
