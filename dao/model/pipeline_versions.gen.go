// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNamePipelineVersion = "pipeline_versions"

// PipelineVersion mapped from table <pipeline_versions>
type PipelineVersion struct {
	ID                       string    `gorm:"column:id;primaryKey;comment:ID" json:"id"`                                                             // ID
	CreateTime               time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`                          // 创建时间
	UpdateTime               time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`                          // 更新时间
	PipelineID               string    `gorm:"column:pipeline_id;not null;comment:pipeline ID" json:"pipeline_id"`                                    // pipeline ID
	Name                     string    `gorm:"column:name;not null;comment:名称" json:"name"`                                                           // 名称
	TaskFlow                 string    `gorm:"column:task_flow;comment:工作流程图json" json:"task_flow"`                                                   // 工作流程图json
	Desc                     string    `gorm:"column:desc;comment:描述" json:"desc"`                                                                    // 描述
	SchedulingStyle          int32     `gorm:"column:scheduling_style;not null;comment:调度方式，0手动调度，1定时调度" json:"scheduling_style"`                     // 调度方式，0手动调度，1定时调度
	TimingConfigCron         string    `gorm:"column:timing_config_cron;comment:调度周期cron表达式" json:"timing_config_cron"`                               // 调度周期cron表达式
	TimingConfigScheduleType int32     `gorm:"column:timing_config_schedule_type;not null;comment:定时调度设置，0基础 1高级" json:"timing_config_schedule_type"` // 定时调度设置，0基础 1高级
	WorkflowAdvancedConfig   string    `gorm:"column:workflow_advanced_config;comment:工作流高级配置json" json:"workflow_advanced_config"`                   // 工作流高级配置json
}

// TableName PipelineVersion's table name
func (*PipelineVersion) TableName() string {
	return TableNamePipelineVersion
}
