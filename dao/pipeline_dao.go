package dao

import (
	"github.com/google/uuid"
	"transwarp.io/mlops/pipeline/dao/model"
)

type PipelineDAO struct {
	*CommonDAO
}

func NewPipelineDAO(dao *CommonDAO) *PipelineDAO {
	pipelineDAO := &PipelineDAO{
		dao,
	}
	return pipelineDAO
}

func (dao *PipelineDAO) GetById(id string) (*model.Pipeline, error) {
	q := dao.Pipeline
	return q.Where(q.ID.Eq(id)).First()
}

func (dao *PipelineDAO) DeleteById(id string) error {
	q := dao.Pipeline
	_, err := q.Delete(&model.Pipeline{
		ID: id,
	})
	return err
}

func (dao *PipelineDAO) ListByProjectId(projectId string) ([]*model.Pipeline, error) {
	q := dao.Pipeline
	return q.Where(q.ProjectID.Eq(projectId)).Find()
}

func (dao *PipelineDAO) Create(pipeline *model.Pipeline) (string, error) {
	q := dao.Pipeline
	if pipeline.ID == "" {
		pipeline.ID = uuid.New().String()
	}
	err := q.Create(pipeline)
	return pipeline.ID, err
}

func (dao *PipelineDAO) Save(pipeline *model.Pipeline) (string, error) {
	q := dao.Pipeline
	err := q.Save(pipeline)
	return pipeline.ID, err
}

func (dao *PipelineDAO) CountByNameAndProjectId(name string, projectId string) (int64, error) {
	q := dao.Pipeline
	return q.Where(q.Name.Eq(name), q.ProjectID.Eq(projectId)).Count()
}

func (dao *PipelineDAO) DeleteByIds(Ids []string) error {
	q := dao.Pipeline
	_, err := q.Where(q.ID.In(Ids...)).Delete()
	return err
}