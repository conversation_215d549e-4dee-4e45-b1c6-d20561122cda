package dao

import (
	"context"
	"fmt"

	"gorm.io/gen"
	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	"transwarp.io/mlops/pipeline/conf"
	"transwarp.io/mlops/pipeline/dao/model"
)

type RunDAO struct {
	*CommonDAO
}

func NewRunDAO(dao *CommonDAO) *RunDAO {
	runDAO := &RunDAO{
		dao,
	}
	return runDAO
}

func (dao *RunDAO) Get(ctx context.Context, id string) (*model.Run, error) {
	q := dao.Run
	return q.Where(q.UUID.Eq(id)).First()
}

func (dao *RunDAO) Delete(ctx context.Context, id string) error {
	q := dao.Run
	_, err := q.Where(q.UUID.Eq(id)).Delete(&model.Run{})
	return err
}

func (dao *RunDAO) List(ctx context.Context, projectId string, filter *pb.ListRunsReq_Filter, pageConfig *pb.PageReq) ([]*model.Run, int64, error) {
	q := dao.Run
	conds := make([]gen.Condition, 0)
	if projectId != "" {
		conds = append(conds, q.ProjectID.Eq(projectId))
	}
	if filter.SourceType != pb.TaskSourceType_NONE {
		conds = append(conds, q.SourceType.Eq(filter.SourceType.String()))
	}
	if filter.From != 0 && filter.To != 0 {
		conds = append(conds, q.CreatedAtInSec.Between(filter.From, filter.To))
	}
	if filter.CreateUser != "" {
		conds = append(conds, q.Owner.Eq(filter.CreateUser))
	}
	if filter.SourceId != nil && len(filter.SourceId) > 0 {
		conds = append(conds, q.SourceID.In(filter.SourceId...))
	}
	if filter.SourceName != "" {
		conds = append(conds, q.SourceName.Like(fmt.Sprintf("%%%s%%", filter.SourceName)))
	}
	if filter.State != nil && len(filter.State) > 0 {
		states := make([]string, 0)
		for _, s := range filter.State {
			states = append(states, s.String())
		}
		conds = append(conds, q.Conditions.In(states...))
	}
	conds = append(conds, q.SophonID.Eq(conf.SophonServiceId))
	// 从代码来看直接调用 RPC 会出现空指针
	if pageConfig == nil {
		pageConfig = &pb.PageReq{}
	}
	if pageConfig.PageSize <= 0 {
		pageConfig.PageSize = -1
		pageConfig.Page = 2
	}
	if pageConfig.Page <= 0 {
		pageConfig.Page = 1
	}
	return q.Where(conds...).Order(q.CreatedAtInSec.Desc()).FindByPage((int(pageConfig.Page)-1)*int(pageConfig.PageSize), int(pageConfig.PageSize))
}
