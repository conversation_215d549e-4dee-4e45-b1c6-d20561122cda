package dao

import (
	"transwarp.io/mlops/pipeline/dao/model"
)

type ExperimentDAO struct {
	*CommonDAO
}

func NewExperimentDAO(dao *CommonDAO) *ExperimentDAO {
	experimentDAO := &ExperimentDAO{
		dao,
	}
	return experimentDAO
}

func (dao *ExperimentDAO) ListByName(name string) ([]*model.Experiment, error) {
	q := dao.Experiment
	return q.Where(q.Name.Eq(name)).Find()
}

func (dao *ExperimentDAO) Get(id string) (*model.Experiment, error) {
	q := dao.Experiment
	return q.Where(q.UUID.Eq(id)).First()
}

func (dao *ExperimentDAO) UpdateNamespace(id string, namespace string) error {
	q := dao.Experiment
	_, err := q.Where(q.UUID.Eq(id)).Update(q.Namespace, namespace)
	return err
}
func (dao *ExperimentDAO) Save(experiment *model.Experiment) error {
	q := dao.Experiment
	err := q.Save(experiment)
	return err
}