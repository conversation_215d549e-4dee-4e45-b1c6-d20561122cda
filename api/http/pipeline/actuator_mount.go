package pipeline

import (
	restfulspec "github.com/emicklei/go-restful-openapi/v2"
	restful "github.com/emicklei/go-restful/v3"
	pb "transwarp.io/aip/llmops-common/pb/common"
	"transwarp.io/mlops/pipeline/api/http/service"
)

type ActuatorService struct {
	ServiceName string
	rootPath    string
}

func init() {
	s := &ActuatorService{
		ServiceName: "Actuator",
		rootPath:    service.Actuator(),
	}
	service.RegisterService(s)
}

func (s *ActuatorService) Name() string {
	return s.ServiceName
}

func (s *ActuatorService) NewWebService() *restful.WebService {
	tags := []string{"Actuator"}
	metaK, metaV := restfulspec.KeyOpenAPITags, tags
	ws := new(restful.WebService)
	ws.Path(s.rootPath).
		Consumes(restful.MIME_JSON, restful.MIME_OCTET).
		Produces(restful.MIME_JSON, restful.MIME_OCTET)

	ws.Route(ws.GET("/health").
		To(s.<PERSON>eat<PERSON>).
		Doc("get all services").
		Metadata(restfulspec.KeyOpenAPITags, tags).
		Metadata(metaK, metaV).
		Reads(pb.CheckHealthReq{}).
		Returns(200, "OK", pb.HealthRsp{}))

	return ws
}
