package pipeline

import (
	"github.com/emicklei/go-restful/v3"
	"transwarp.io/aip/llmops-common/pb/common"
	"transwarp.io/mlops/mlops-std/util"
	api "transwarp.io/mlops/pipeline/api/grpc/pipeline"
	"transwarp.io/mlops/pipeline/api/http/helper"
)

func (this *ActuatorService) CheckHeath(request *restful.Request, response *restful.Response) {
	req := common.CheckHealthReq{}

	// call
	res, err := api.ActuatorServiceRPC.CheckHealth(helper.ContextWithToken(request), &req)

	if err != nil {
		util.ErrorResponse(response, err, util.GetLocale(request))
	} else {
		response.WriteEntity(res)
	}
}
