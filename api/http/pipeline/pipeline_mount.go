package pipeline

import (
	restfulspec "github.com/emicklei/go-restful-openapi/v2"
	restful "github.com/emicklei/go-restful/v3"
	"transwarp.io/aip/llmops-common/pb/common"
	pb "transwarp.io/aip/llmops-common/pb/pipeline"

	"transwarp.io/mlops/pipeline/api/http/service"
)

type PipelineService struct {
	ServiceName string
	rootPath    string
}

func init() {
	s := &PipelineService{
		ServiceName: "pipeline",
		rootPath:    service.V1Pipeline(),
	}
	service.RegisterService(s)
}

func (s *PipelineService) Name() string {
	return s.ServiceName
}

func (r *PipelineService) NewWebService() *restful.WebService {
	tags := []string{"Pipeline"}
	metaK, metaV := restfulspec.KeyOpenAPITags, tags
	ws := new(restful.WebService)
	ws.Path(r.rootPath).
		Consumes(restful.MIME_JSON, restful.MIME_OCTET).
		Produces(restful.MIME_JSON, restful.MIME_OCTET)

	apiTags := tags

	ws.Route(ws.GET("/{id}").
		To(r.GetPipeline).
		Doc("根据id获取工作流").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Metadata(metaK, metaV).
		Param(ws.PathParameter("id", "工作流id")).
		Returns(200, "OK", pb.Pipeline{}))

	ws.Route(ws.GET("/components").
		To(r.GetComponents).
		Doc("获取所有组件(流程)，组件即工作流节点的模板，节点的默认信息由组件信息填充").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Returns(200, "OK", pb.ComponentPage{}))

	ws.Route(ws.POST("").
		To(r.CreatePipeline).
		Doc("创建工作流").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.QueryParameter("project_id", "project id")).
		Reads(pb.CreatePipelineBody{}).
		Returns(200, "OK", pb.PipelineId{}))

	ws.Route(ws.GET("").
		To(r.GetAllPipelines).
		Doc("获取项目下所有工作流").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.QueryParameter("project_id", "project id")).
		Returns(200, "OK", pb.PipelinePage{}))

	ws.Route(ws.DELETE("/{id}").
		To(r.DeletePipeline).
		Doc("删除工作流").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.PathParameter("id", "工作流id")).
		Returns(200, "OK", common.DeleteRsp{}))

	ws.Route(ws.POST("/batch/delete").
		To(r.DeletePipelines).
		Doc("delete pipeline by ids").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Reads([]string{}).
		Returns(200, "OK", common.DeleteRsp{}))

	ws.Route(ws.PUT("/{id}").
		To(r.ModifyPipeline).
		Doc("修改工作流").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.PathParameter("id", "pipeline id")).
		Reads(pb.ModifyPipelineBody{}).
		Returns(200, "OK", pb.PipelineId{}))

	ws.Route(ws.POST("/version").
		To(r.CreatePipelineVersion).
		Doc("创建工作流版本").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Reads(pb.CreatePipelineVersionBody{}).
		Returns(200, "OK", pb.PipelineVersionId{}))

	ws.Route(ws.POST("/version/{version_id}/save").
		To(r.SavePipelineVersion).
		Doc("修改工作流版本的流程图和调度信息").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.PathParameter("version_id", "工作流版本id")).
		Reads(pb.SavePipelineVersionBody{}).
		Returns(200, "OK", pb.PipelineVersion{}))

	ws.Route(ws.GET("/{pipeline_id}/version").
		To(r.GetAllPipelineVersions).
		Doc("获取工作流下所有版本").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.PathParameter("pipeline_id", "工作流id")).
		Returns(200, "OK", pb.PipelineVersionPage{}))

	ws.Route(ws.GET("/version/{id}").
		To(r.GetPipelineVersion).
		Doc("根据id获取工作流版本").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.PathParameter("id", "工作流版本id")).
		Returns(200, "OK", pb.PipelineVersion{}))

	ws.Route(ws.DELETE("/version/{id}").
		To(r.DeletePipelineVersion).
		Doc("delete pipeline version by id").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.PathParameter("id", "工作流版本id")).
		Returns(200, "OK", common.DeleteRsp{}))

	ws.Route(ws.POST("/version/batch/delete").
		To(r.DeletePipelineVersions).
		Doc("delete pipeline versions by ids").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Reads([]string{}).
		Returns(200, "OK", common.DeleteRsp{}))

	ws.Route(ws.POST("/version/{id}/start").
		To(r.StartPipelineVersion).
		Doc("开启定时调度类型的工作流版本").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.PathParameter("id", "工作流版本id")).
		Returns(200, "OK", pb.PipelineVersionId{}))

	ws.Route(ws.POST("/version/{id}/stop").
		To(r.StopPipelineVersion).
		Doc("关闭定时调度类型的工作流版本").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.PathParameter("id", "工作流版本id")).
		Returns(200, "OK", pb.PipelineVersionId{}))

	ws.Route(ws.POST("/version/{id}/once").
		To(r.OncePipelineVersion).
		Doc("执行一次手动调度类型的工作流版本").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.PathParameter("id", "工作流版本id")).
		Returns(200, "OK", pb.OncePipelineVersionRsp{}))

	ws.Route(ws.GET("/version/{id}/export").
		To(r.ExportPipelineVersion).
		Doc("以yaml的形式导出工作流版本").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.PathParameter("id", "工作流版本id")).
		Returns(200, "OK", []byte{}))

	ws.Route(ws.POST("/version/from-yaml").
		To(r.CreatePipelineVersionByYaml).
		Doc("通过导入pipeline yaml创建工作流版本").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Reads(pb.CreatePipelineVersionByYamlBody{}).
		Returns(200, "OK", pb.PipelineVersionId{}))

	ws.Route(ws.POST("/unique").
		To(r.CheckPipelineNameUnique).
		Doc("check pipeline name is unique").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.QueryParameter("project_id", "project id")).
		Reads(pb.CheckPipelineBody{}).
		Returns(200, "OK", pb.CheckPipelineRsp{}))

	ws.Route(ws.POST("/version/unique").
		To(r.CheckPipelineNameVersionUnique).
		Doc("check pipeline version name is unique").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Reads(pb.CheckPipelineVersionBody{}).
		Returns(200, "OK", pb.CheckPipelineVersionRsp{}))

	return ws
}
