package pipeline

import (
	restfulspec "github.com/emicklei/go-restful-openapi/v2"
	restful "github.com/emicklei/go-restful/v3"
	pb "transwarp.io/aip/llmops-common/pb/pipeline"

	"transwarp.io/mlops/pipeline/api/http/service"
)

type PipelineServiceV2 struct {
	ServiceName string
	rootPath    string

	ps *PipelineService
}

func init() {
	s := &PipelineServiceV2{
		ServiceName: "pipelineV2",
		rootPath:    service.V2Pipeline(),
	}
	service.RegisterService(s)
}

func (s *PipelineServiceV2) Name() string {
	return s.ServiceName
}

func (r *PipelineServiceV2) NewWebService() *restful.WebService {
	tags := []string{"PipelineV2"}
	metaK, metaV := restfulspec.KeyOpenAPITags, tags
	ws := new(restful.WebService)
	ws.Path(r.rootPath).
		Consumes(restful.MIME_JSON, restful.MIME_OCTET).
		Produces(restful.MIME_JSON, restful.MIME_OCTET)

	apiTags := tags

	ws.Route(ws.POST("/version/{version_id}/save").
		To(r.ps.SavePipelineVersionV2).
		Doc("修改工作流版本的流程图和调度信息").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Metadata(metaK, metaV).
		Param(ws.PathParameter("version_id", "工作流版本id")).
		Reads(pb.SavePipelineVersionBody{}).
		Returns(200, "OK", pb.PipelineVersion{}))

	ws.Route(ws.GET("/version/{id}").
		To(r.ps.GetPipelineVersionV2).
		Doc("根据id获取工作流版本").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.PathParameter("id", "工作流版本id")).
		Returns(200, "OK", pb.PipelineVersion{}))

	return ws
}
