package pipeline

import (
	"bytes"

	"github.com/emicklei/go-restful/v3"
	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	"transwarp.io/mlops/mlops-std/stderr"
	"transwarp.io/mlops/mlops-std/util"
	grpc "transwarp.io/mlops/pipeline/api/grpc/pipeline"
	"transwarp.io/mlops/pipeline/api/http/helper"
)

func (this *PipelineService) GetPipeline(request *restful.Request, response *restful.Response) {
	req := pb.GetPipelineReq{
		Id: request.PathParameter("id"),
	}
	// call
	res, err := grpc.PipelineGRPC.GetPipeline(helper.ContextWithToken(request), &req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) GetComponents(request *restful.Request, response *restful.Response) {
	req := pb.GetComponentsReq{}
	// call
	res, err := grpc.PipelineGRPC.GetComponents(helper.ContextWithToken(request), &req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) CreatePipeline(request *restful.Request, response *restful.Response) {
	body := &pb.CreatePipelineBody{}
	request.ReadEntity(body)
	req := &pb.CreatePipelineReq{
		ProjectId:          request.QueryParameter("project_id"),
		CreatePipelineBody: body,
	}
	// call
	res, err := grpc.PipelineGRPC.CreatePipeline(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) GetAllPipelines(request *restful.Request, response *restful.Response) {
	req := &pb.ListPipelinesReq{
		ProjectId: request.QueryParameter("project_id"),
	}

	res, err := grpc.PipelineGRPC.ListPipelines(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) DeletePipeline(request *restful.Request, response *restful.Response) {
	req := pb.DeletePipelineReq{
		Id: request.PathParameter("id"),
	}
	// call
	res, err := grpc.PipelineGRPC.DeletePipeline(helper.ContextWithToken(request), &req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) DeletePipelines(request *restful.Request, response *restful.Response) {
	req := pb.DeletePipelinesReq{}
	request.ReadEntity(&req.Ids)

	// call
	res, err := grpc.PipelineGRPC.DeletePipelines(helper.ContextWithToken(request), &req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) ModifyPipeline(request *restful.Request, response *restful.Response) {
	body := &pb.ModifyPipelineBody{}
	request.ReadEntity(body)
	req := &pb.ModifyPipelineReq{
		Id:                 request.PathParameter("id"),
		ModifyPipelineBody: body,
	}
	request.ReadEntity(req)
	// call
	res, err := grpc.PipelineGRPC.ModifyPipeline(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) CreatePipelineVersion(request *restful.Request, response *restful.Response) {
	body := &pb.CreatePipelineVersionBody{}
	request.ReadEntity(body)
	req := &pb.CreatePipelineVersionReq{
		CreatePipelineVersionBody: body,
	}
	// call
	res, err := grpc.PipelineGRPC.CreatePipelineVersion(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) SavePipelineVersion(request *restful.Request, response *restful.Response) {
	body := &pb.SavePipelineVersionBody{}
	request.ReadEntity(body)

	req := &pb.SavePipelineVersionReq{
		PipelineVersionId:       request.PathParameter("version_id"),
		SavePipelineVersionBody: body,
	}
	// call
	res, err := grpc.PipelineGRPC.SavePipelineVersion(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) SavePipelineVersionV2(request *restful.Request, response *restful.Response) {
	body := &pb.SavePipelineVersionBodyV2{}
	err := request.ReadEntity(body)

	req := &pb.SavePipelineVersionReqV2{
		PipelineVersionId:       request.PathParameter("version_id"),
		SavePipelineVersionBody: body,
	}
	// call
	res, err := grpc.PipelineGRPC.SavePipelineVersionV2(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) GetAllPipelineVersions(request *restful.Request, response *restful.Response) {
	req := &pb.ListPipelineVersionsReq{
		PipelineId: request.PathParameter("pipeline_id"),
	}

	res, err := grpc.PipelineGRPC.ListPipelineVersions(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) GetPipelineVersion(request *restful.Request, response *restful.Response) {
	req := pb.GetPipelineVersionReq{
		Id: request.PathParameter("id"),
	}
	// call
	res, err := grpc.PipelineGRPC.GetPipelineVersion(helper.ContextWithToken(request), &req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) GetPipelineVersionV2(request *restful.Request, response *restful.Response) {
	req := pb.GetPipelineVersionReq{
		Id: request.PathParameter("id"),
	}
	// call
	res, err := grpc.PipelineGRPC.GetPipelineVersionV2(helper.ContextWithToken(request), &req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) DeletePipelineVersion(request *restful.Request, response *restful.Response) {
	req := pb.DeletePipelineVersionReq{
		Id: request.PathParameter("id"),
	}
	// call
	res, err := grpc.PipelineGRPC.DeletePipelineVersion(helper.ContextWithToken(request), &req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) DeletePipelineVersions(request *restful.Request, response *restful.Response) {
	req := pb.DeletePipelineVersionsReq{}
	request.ReadEntity(&req.Ids)

	// call
	res, err := grpc.PipelineGRPC.DeletePipelineVersions(helper.ContextWithToken(request), &req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) OncePipelineVersion(request *restful.Request, response *restful.Response) {
	req := &pb.OncePipelineVersionReq{
		Id: request.PathParameter("id"),
	}
	// call
	res, err := grpc.PipelineGRPC.OncePipelineVersion(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) StartPipelineVersion(request *restful.Request, response *restful.Response) {
	req := &pb.StartPipelineVersionReq{
		Id: request.PathParameter("id"),
	}
	// call
	res, err := grpc.PipelineGRPC.StartPipelineVersion(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) StopPipelineVersion(request *restful.Request, response *restful.Response) {
	req := &pb.StopPipelineVersionReq{
		Id: request.PathParameter("id"),
	}
	// call
	res, err := grpc.PipelineGRPC.StopPipelineVersion(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) ExportPipelineVersion(request *restful.Request, response *restful.Response) {
	req := &pb.ExportPipelineVersionReq{
		Id: request.PathParameter("id"),
	}
	// call
	res, err := grpc.PipelineGRPC.ExportPipelineVersion(helper.ContextWithToken(request), req)
	if err == nil {
		util.PostResponseWriter(request, response, bytes.NewReader(res.PipelineVersionFile), res.FileName, err)
	} else {
		util.ErrorResponse(response, err, util.GetLocale(request))
	}
}

func (this *PipelineService) CreatePipelineVersionByYaml(request *restful.Request, response *restful.Response) {
	body := &pb.CreatePipelineVersionByYamlBody{}
	request.ReadEntity(body)
	req := &pb.CreatePipelineVersionByYamlReq{
		CreatePipelineVersionByYamlBody: body,
	}
	// call
	res, err := grpc.PipelineGRPC.CreatePipelineVersionByYaml(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) CheckPipelineNameUnique(request *restful.Request, response *restful.Response) {
	body := &pb.CheckPipelineBody{}
	request.ReadEntity(body)
	req := &pb.CheckPipelineReq{
		Body:      body,
		ProjectId: request.QueryParameter("project_id"),
	}
	// call
	_, err := grpc.PipelineGRPC.CheckPipelineNameUnique(helper.ContextWithToken(request), req)
	if err != nil {
		if err.(*stderr.StdErr).Code == stderr.PipelineNameExist.Code {
			response.WriteEntity(false)
		} else {
			util.ErrorResponse(response, err, util.GetLocale(request))
		}
	} else {
		response.WriteEntity(true)
	}
}

func (this *PipelineService) CheckPipelineNameVersionUnique(request *restful.Request, response *restful.Response) {
	body := &pb.CheckPipelineVersionBody{}
	request.ReadEntity(body)
	req := &pb.CheckPipelineVersionReq{
		Body: body,
	}
	// call
	_, err := grpc.PipelineGRPC.CheckPipelineVersionNameUnique(helper.ContextWithToken(request), req)
	if err != nil {
		if err.(*stderr.StdErr).Code == stderr.PipelineVersionNameExist.Code {
			response.WriteEntity(false)
		} else {
			util.ErrorResponse(response, err, util.GetLocale(request))
		}
	} else {
		response.WriteEntity(true)
	}
}
