package pipeline

import (
	"bytes"

	"github.com/emicklei/go-restful/v3"
	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	"transwarp.io/mlops/mlops-std/stderr"
	"transwarp.io/mlops/mlops-std/util"
	"transwarp.io/mlops/pipeline/api/http/helper"
	provider "transwarp.io/mlops/pipeline/api/provider"
)

func (this *PipelineService) GetPipeline(request *restful.Request, response *restful.Response) {
	req := pb.GetPipelineReq{
		Id: request.PathParameter("id"),
	}
	// call
	res, err := provider.MustGetPipelineService().GetPipeline(helper.ContextWithToken(request), &req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) GetComponents(request *restful.Request, response *restful.Response) {
	req := pb.GetComponentsReq{}
	// call
	res, err := provider.MustGetPipelineService().GetComponents(helper.ContextWithToken(request), &req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) CreatePipeline(request *restful.Request, response *restful.Response) {
	body := &pb.CreatePipelineBody{}
	request.ReadEntity(body)
	req := &pb.CreatePipelineReq{
		ProjectId:          request.QueryParameter("project_id"),
		CreatePipelineBody: body,
	}
	// call
	res, err := provider.MustGetPipelineService().CreatePipeline(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) GetAllPipelines(request *restful.Request, response *restful.Response) {
	req := &pb.ListPipelinesReq{
		ProjectId: request.QueryParameter("project_id"),
	}

	res, err := provider.MustGetPipelineService().ListPipelines(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) DeletePipeline(request *restful.Request, response *restful.Response) {
	req := pb.DeletePipelineReq{
		Id: request.PathParameter("id"),
	}
	// call
	res, err := provider.MustGetPipelineService().DeletePipeline(helper.ContextWithToken(request), &req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) DeletePipelines(request *restful.Request, response *restful.Response) {
	req := pb.DeletePipelinesReq{}
	request.ReadEntity(&req.Ids)

	// call
	res, err := provider.MustGetPipelineService().DeletePipelines(helper.ContextWithToken(request), &req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) ModifyPipeline(request *restful.Request, response *restful.Response) {
	body := &pb.ModifyPipelineBody{}
	request.ReadEntity(body)
	req := &pb.ModifyPipelineReq{
		Id:                 request.PathParameter("id"),
		ModifyPipelineBody: body,
	}
	request.ReadEntity(req)
	// call
	res, err := provider.MustGetPipelineService().ModifyPipeline(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) CreatePipelineVersion(request *restful.Request, response *restful.Response) {
	body := &pb.CreatePipelineVersionBody{}
	request.ReadEntity(body)
	req := &pb.CreatePipelineVersionReq{
		CreatePipelineVersionBody: body,
	}
	// call
	res, err := provider.MustGetPipelineService().CreatePipelineVersion(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) SavePipelineVersion(request *restful.Request, response *restful.Response) {
	body := &pb.SavePipelineVersionBody{}
	request.ReadEntity(body)

	req := &pb.SavePipelineVersionReq{
		PipelineVersionId:       request.PathParameter("version_id"),
		SavePipelineVersionBody: body,
	}
	// call
	res, err := provider.MustGetPipelineService().SavePipelineVersion(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) SavePipelineVersionV2(request *restful.Request, response *restful.Response) {
	body := &pb.SavePipelineVersionBodyV2{}
	err := request.ReadEntity(body)

	req := &pb.SavePipelineVersionReqV2{
		PipelineVersionId:       request.PathParameter("version_id"),
		SavePipelineVersionBody: body,
	}
	// call
	res, err := provider.MustGetPipelineService().SavePipelineVersionV2(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) GetAllPipelineVersions(request *restful.Request, response *restful.Response) {
	req := &pb.ListPipelineVersionsReq{
		PipelineId: request.PathParameter("pipeline_id"),
	}

	res, err := provider.MustGetPipelineService().ListPipelineVersions(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) GetPipelineVersion(request *restful.Request, response *restful.Response) {
	req := pb.GetPipelineVersionReq{
		Id: request.PathParameter("id"),
	}
	// call
	res, err := provider.MustGetPipelineService().GetPipelineVersion(helper.ContextWithToken(request), &req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) GetPipelineVersionV2(request *restful.Request, response *restful.Response) {
	req := pb.GetPipelineVersionReq{
		Id: request.PathParameter("id"),
	}
	// call
	res, err := provider.MustGetPipelineService().GetPipelineVersionV2(helper.ContextWithToken(request), &req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) DeletePipelineVersion(request *restful.Request, response *restful.Response) {
	req := pb.DeletePipelineVersionReq{
		Id: request.PathParameter("id"),
	}
	// call
	res, err := provider.MustGetPipelineService().DeletePipelineVersion(helper.ContextWithToken(request), &req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) DeletePipelineVersions(request *restful.Request, response *restful.Response) {
	req := pb.DeletePipelineVersionsReq{}
	request.ReadEntity(&req.Ids)

	// call
	res, err := provider.MustGetPipelineService().DeletePipelineVersions(helper.ContextWithToken(request), &req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) OncePipelineVersion(request *restful.Request, response *restful.Response) {
	req := &pb.OncePipelineVersionReq{
		Id: request.PathParameter("id"),
	}
	// call
	res, err := provider.MustGetPipelineService().OncePipelineVersion(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) StartPipelineVersion(request *restful.Request, response *restful.Response) {
	req := &pb.StartPipelineVersionReq{
		Id: request.PathParameter("id"),
	}
	// call
	res, err := provider.MustGetPipelineService().StartPipelineVersion(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) StopPipelineVersion(request *restful.Request, response *restful.Response) {
	req := &pb.StopPipelineVersionReq{
		Id: request.PathParameter("id"),
	}
	// call
	res, err := provider.MustGetPipelineService().StopPipelineVersion(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) ExportPipelineVersion(request *restful.Request, response *restful.Response) {
	req := &pb.ExportPipelineVersionReq{
		Id: request.PathParameter("id"),
	}
	// call
	res, err := provider.MustGetPipelineService().ExportPipelineVersion(helper.ContextWithToken(request), req)
	if err == nil {
		util.PostResponseWriter(request, response, bytes.NewReader(res.PipelineVersionFile), res.FileName, err)
	} else {
		util.ErrorResponse(response, err, util.GetLocale(request))
	}
}

func (this *PipelineService) CreatePipelineVersionByYaml(request *restful.Request, response *restful.Response) {
	body := &pb.CreatePipelineVersionByYamlBody{}
	request.ReadEntity(body)
	req := &pb.CreatePipelineVersionByYamlReq{
		CreatePipelineVersionByYamlBody: body,
	}
	// call
	res, err := provider.MustGetPipelineService().CreatePipelineVersionByYaml(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}

func (this *PipelineService) CheckPipelineNameUnique(request *restful.Request, response *restful.Response) {
	body := &pb.CheckPipelineBody{}
	request.ReadEntity(body)
	req := &pb.CheckPipelineReq{
		Body:      body,
		ProjectId: request.QueryParameter("project_id"),
	}
	// call
	_, err := provider.MustGetPipelineService().CheckPipelineNameUnique(helper.ContextWithToken(request), req)
	if err != nil {
		if err.(*stderr.StdErr).Code == stderr.PipelineNameExist.Code {
			response.WriteEntity(false)
		} else {
			util.ErrorResponse(response, err, util.GetLocale(request))
		}
	} else {
		response.WriteEntity(true)
	}
}

func (this *PipelineService) CheckPipelineNameVersionUnique(request *restful.Request, response *restful.Response) {
	body := &pb.CheckPipelineVersionBody{}
	request.ReadEntity(body)
	req := &pb.CheckPipelineVersionReq{
		Body: body,
	}
	// call
	_, err := provider.MustGetPipelineService().CheckPipelineVersionNameUnique(helper.ContextWithToken(request), req)
	if err != nil {
		if err.(*stderr.StdErr).Code == stderr.PipelineVersionNameExist.Code {
			response.WriteEntity(false)
		} else {
			util.ErrorResponse(response, err, util.GetLocale(request))
		}
	} else {
		response.WriteEntity(true)
	}
}
