package service

import "fmt"

var (
	rootPath = "/api"
	v1       = "/v1"
	v2       = "/v2"
	v3       = "/v3"
	run      = "/run"
	pipeline = "/pipeline"
	workload = "/workload"
	actuator = "/actuator"
)

func V1() string {
	return fmt.Sprintf("%s%s", rootPath, v1)
}

func V2() string {
	return fmt.Sprintf("%s%s", rootPath, v2)
}

func V3() string {
	return fmt.Sprintf("%s%s", rootPath, v3)
}

func V1Actuator() string {
	return fmt.Sprintf("%s%s", V1(), actuator)
}

func V1Pipeline() string {
	return fmt.Sprintf("%s%s", V1(), pipeline)
}

func V2Pipeline() string {
	return fmt.Sprintf("%s%s", V2(), pipeline)
}

func V1Run() string {
	return fmt.Sprintf("%s%s", V1(), run)
}

func V2Run() string {
	return fmt.Sprintf("%s%s", V2(), run)
}

func V3Workload() string {
	return fmt.Sprintf("%s%s", V3(), workload)
}
