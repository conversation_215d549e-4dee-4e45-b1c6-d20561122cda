package service

import (
	"sync"

	"github.com/emicklei/go-restful/v3"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

type Service interface {
	Name() string
	NewWebService() *restful.WebService
}

type ServiceManager struct {
	services map[string]Service
	mu       sync.RWMutex
}

func NewServiceManager() *ServiceManager {
	return &ServiceManager{
		services: make(map[string]Service),
	}
}

var (
	DefaultServiceManager *ServiceManager
	once                  sync.Once
)

func GetServiceManager() *ServiceManager {
	once.Do(func() {
		DefaultServiceManager = NewServiceManager()
	})
	return DefaultServiceManager
}

func (m *ServiceManager) Register(s Service) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	name := s.Name()
	if _, exists := m.services[name]; exists {
		zlog.SugarWarnf("Service %s already registered, skipping", name)
		return nil
	}

	m.services[name] = s
	zlog.SugarInfof("Service %s registered successfully", name)
	return nil
}

func RegisterService(s Service) {
	GetServiceManager().Register(s)
}

func InitService(container *restful.Container) {
	GetServiceManager().Init(container)
}

func (m *ServiceManager) Init(container *restful.Container) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	for name, service := range m.services {
		zlog.SugarInfof("Initializing service: %s", name)
		ws := service.NewWebService()
		container.Add(ws)
	}
}

func (m *ServiceManager) GetService(name string) (Service, bool) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	service, exists := m.services[name]
	return service, exists
}

func (m *ServiceManager) GetAllServices() []Service {
	m.mu.RLock()
	defer m.mu.RUnlock()

	services := make([]Service, 0, len(m.services))
	for _, service := range m.services {
		services = append(services, service)
	}
	return services
}
