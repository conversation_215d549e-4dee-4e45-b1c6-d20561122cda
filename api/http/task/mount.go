package task

import (
	restfulspec "github.com/emicklei/go-restful-openapi/v2"
	restful "github.com/emicklei/go-restful/v3"

	"transwarp.io/mlops/pipeline/api/http/service"
)

type WorkloadService struct {
	ServiceName string
	rootPath    string
}

func init() {
	s := &WorkloadService{
		ServiceName: "Task",
		rootPath:    service.V3Workload(),
	}
	service.RegisterService(s)
}

func (s *WorkloadService) Name() string {
	return s.ServiceName
}

func (r *WorkloadService) NewWebService() *restful.WebService {
	tags := []string{"Task"}
	metaK, metaV := restfulspec.KeyOpenAPITags, tags
	ws := new(restful.WebService)
	ws.Path(r.rootPath).
		Consumes(restful.MIME_JSON, restful.MIME_OCTET).
		Produces(restful.MIME_JSON, restful.MIME_OCTET)

	apiTags := tags

	// 保留原有的路由
	ws.Route(ws.GET("").
		To(r.ListTasks).
		Doc("任务列表").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Metadata(metaK, metaV).
		Reads(ListWorkloadRequest{}).
		Returns(200, "OK", ListWorkloadResponse{}))

	// 添加新的Kueue集成路由
	ws.Route(ws.GET("/kueue").
		To(r.ListWorkloadWithKueue).
		Doc("基于Kueue的工作负载列表").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Metadata(metaK, metaV).
		Param(ws.QueryParameter("page", "页码").DataType("integer").DefaultValue("1")).
		Param(ws.QueryParameter("page_size", "每页大小").DataType("integer").DefaultValue("20")).
		Param(ws.QueryParameter("project", "项目").DataType("string")).
		Param(ws.QueryParameter("status", "状态").DataType("string")).
		Param(ws.QueryParameter("type", "类型").DataType("string")).
		Param(ws.QueryParameter("priority", "优先级").DataType("integer")).
		Param(ws.QueryParameter("creator", "创建者").DataType("string")).
		Param(ws.QueryParameter("queue_name", "队列名称").DataType("string")).
		Param(ws.QueryParameter("search_keyword", "搜索关键词").DataType("string")).
		Param(ws.QueryParameter("sort_by", "排序字段").DataType("string").DefaultValue("create_at")).
		Param(ws.QueryParameter("sort_order", "排序方向").DataType("string").DefaultValue("desc")).
		Returns(200, "OK", map[string]interface{}{}))

	// 添加工作负载统计路由
	ws.Route(ws.GET("/stats").
		To(r.GetWorkloadStats).
		Doc("工作负载统计信息").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Metadata(metaK, metaV).
		Param(ws.QueryParameter("namespace", "命名空间").DataType("string")).
		Returns(200, "OK", map[string]interface{}{}))

	return ws
}
