package task

import (
	restfulspec "github.com/emicklei/go-restful-openapi/v2"
	restful "github.com/emicklei/go-restful/v3"

	"transwarp.io/mlops/pipeline/api/http/service"
	"transwarp.io/mlops/pipeline/internal/service/task"
)

type TaskService struct {
	ServiceName string
	rootPath    string
	ts          *task.TaskService
}

func init() {
	s := &TaskService{
		ServiceName: "TaskService",
		rootPath:    service.V3Task(),

		ts: task.NewTaskService(),
	}
	service.RegisterService(s)
}

func (r *TaskService) Name() string {
	return r.ServiceName
}

func (r *TaskService) NewWebService() *restful.WebService {
	tags := []string{"Task"}
	metaK, metaV := restfulspec.KeyOpenAPITags, tags
	ws := new(restful.WebService)
	ws.Path(r.rootPath).
		Consumes(restful.MIME_JSON, restful.MIME_OCTET).
		Produces(restful.MIME_JSON, restful.MIME_OCTET)

	ws.Route(ws.GET("").
		To(r.ListTaskWithQuery).
		Doc("工作负载列表（支持复杂查询）").
		Metadata(metaK, metaV).
		Param(ws.QueryParameter("page", "页码").DataType("integer").DefaultValue("1")).
		Param(ws.QueryParameter("page_size", "每页大小").DataType("integer").DefaultValue("20")).
		Param(ws.QueryParameter("namespace", "命名空间").DataType("string")).
		Param(ws.QueryParameter("project", "项目").DataType("string")).
		Param(ws.QueryParameter("status", "状态").DataType("string")).
		Param(ws.QueryParameter("type", "类型").DataType("string")).
		Param(ws.QueryParameter("priority", "优先级").DataType("integer")).
		Param(ws.QueryParameter("creator", "创建者").DataType("string")).
		Param(ws.QueryParameter("queue_name", "队列名称").DataType("string")).
		Param(ws.QueryParameter("search_keyword", "搜索关键词").DataType("string")).
		Param(ws.QueryParameter("sort_by", "排序字段").DataType("string").DefaultValue("create_at")).
		Param(ws.QueryParameter("sort_order", "排序方向").DataType("string").DefaultValue("desc")).
		Param(ws.QueryParameter("create_time_start", "创建时间开始").DataType("integer")).
		Param(ws.QueryParameter("create_time_end", "创建时间结束").DataType("integer")).
		Returns(200, "OK", TaskListResp{}))

	ws.Route(ws.GET("/{id}").
		To(r.GetTask).
		Doc("获取工作负载详情").
		Metadata(metaK, metaV).
		Param(ws.PathParameter("id", "任务id").DataType("string")).
		Returns(200, "OK", TaskResp{}))

	return ws
}
