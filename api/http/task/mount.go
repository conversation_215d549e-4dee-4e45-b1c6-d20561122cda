package task

import (
	restfulspec "github.com/emicklei/go-restful-openapi/v2"
	restful "github.com/emicklei/go-restful/v3"

	"transwarp.io/mlops/pipeline/api/http/service"
	"transwarp.io/mlops/pipeline/internal/core/manager/task"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
)

type TaskService struct {
	ServiceName string
	rootPath    string
	tm          *task.TaskManager
}

func init() {
	s := &TaskService{
		ServiceName: "TaskService",
		rootPath:    service.V3Task(),

		tm: task.NewTaskManager(),
	}
	service.RegisterService(s)
}

func (r *TaskService) Name() string {
	return r.ServiceName
}

func (r *TaskService) NewWebService() *restful.WebService {
	tags := []string{"Task"}
	metaK, metaV := restfulspec.KeyOpenAPITags, tags
	ws := new(restful.WebService)
	ws.Path(r.rootPath).
		Consumes(restful.MIME_JSON, restful.MIME_OCTET).
		Produces(restful.MIME_JSON, restful.MIME_OCTET)

	ws.Route(ws.POST("").
		To(r.CreateTask).
		Doc("create task from YAML").
		Metadata(metaK, metaV).
		Consumes("application/x-yaml", "text/yaml").
		Returns(200, "OK", model.Task{}))
	ws.Route(ws.DELETE("/{id}").
		To(r.DeleteTask).
		Doc("delete task by id").
		Metadata(metaK, metaV).
		Param(ws.PathParameter("id", "task id").DataType("string")).
		Returns(200, "OK", model.Task{}))
	ws.Route(ws.GET("").
		To(r.ListTasks).
		Doc("list task").
		Metadata(metaK, metaV).
		Param(ws.QueryParameter("page", "page").DataType("integer").DefaultValue("1")).
		Param(ws.QueryParameter("page_size", "page size").DataType("integer").DefaultValue("20")).
		Param(ws.QueryParameter("project", "project").DataType("string")).
		Param(ws.QueryParameter("status", "status").DataType("string")).
		Param(ws.QueryParameter("type", "type").DataType("string")).
		Param(ws.QueryParameter("priority", "priority").DataType("integer")).
		Param(ws.QueryParameter("creator", "creator").DataType("string")).
		Param(ws.QueryParameter("queue_name", "queue name").DataType("string")).
		Param(ws.QueryParameter("search_keyword", "search keyword").DataType("string")).
		Param(ws.QueryParameter("sort_by", "sort by filed").DataType("string").DefaultValue("create_at")).
		Param(ws.QueryParameter("sort_order", "sort order").DataType("string").DefaultValue("desc")).
		Param(ws.QueryParameter("create_at_start_time", "create at start time").DataType("integer")).
		Param(ws.QueryParameter("create_at_end_time", "create at end time").DataType("integer")).
		Returns(200, "OK", model.ListTasksResp{}))
	ws.Route(ws.GET("/{id}").
		To(r.GetTask).
		Doc("get task by id").
		Metadata(metaK, metaV).
		Param(ws.PathParameter("id", "task id").DataType("string")).
		Returns(200, "OK", model.Task{}))
	ws.Route(ws.POST("/{id}/rerun").
		To(r.RerunTask).
		Doc("rerun task by id").
		Metadata(metaK, metaV).
		Param(ws.PathParameter("id", "task id").DataType("string")).
		Returns(200, "OK", model.Task{}))
	ws.Route(ws.POST("/{id}/suspend").
		To(r.SuspendTask).
		Doc("suspend task by id").
		Metadata(metaK, metaV).
		Param(ws.PathParameter("id", "task id").DataType("string")).
		Returns(200, "OK", model.Task{}))

	// task priority
	ws.Route(ws.GET("/priorities").
		To(r.ListTaskPriorities).
		Doc("list all task priorities").
		Metadata(metaK, metaV).
		Returns(200, "OK", ListPrioritiesResp{}))
	// task type
	ws.Route(ws.GET("/types").
		To(r.ListTaskTypes).
		Doc("list all task types").
		Metadata(metaK, metaV).
		Returns(200, "OK", ListTypesResp{}))
	ws.Route(ws.GET("/statuses").
		To(r.ListTaskStatuses).
		Doc("list all task status").
		Metadata(metaK, metaV).
		Returns(200, "OK", ListStatusesResp{}))
	ws.Route(ws.GET("/queues").
		To(r.ListQueues).
		Doc("list all queues").
		Metadata(metaK, metaV).
		Returns(200, "OK", ListStatusesResp{}))

	return ws
}
