package task

import (
	restfulspec "github.com/emicklei/go-restful-openapi/v2"
	restful "github.com/emicklei/go-restful/v3"

	"transwarp.io/mlops/pipeline/api/http/service"
	"transwarp.io/mlops/pipeline/internal/service/task"
)

type TaskService struct {
	ServiceName string
	rootPath    string
	ts          *task.TaskService
}

func init() {
	s := &TaskService{
		ServiceName: "TaskService",
		rootPath:    service.V3Task(),

		ts: task.NewTaskService(),
	}
	service.RegisterService(s)
}

func (r *TaskService) Name() string {
	return r.ServiceName
}

func (r *TaskService) NewWebService() *restful.WebService {
	tags := []string{"Task"}
	metaK, metaV := restfulspec.KeyOpenAPITags, tags
	ws := new(restful.WebService)
	ws.Path(r.rootPath).
		Consumes(restful.MIME_JSON, restful.MIME_OCTET).
		Produces(restful.MIME_JSON, restful.MIME_OCTET)

	// 工作负载列表（使用新的查询框架）
	ws.Route(ws.GET("").
		To(r.ListTaskWithQuery).
		Doc("工作负载列表（支持复杂查询）").
		Metadata(metaK, metaV).
		Param(ws.QueryParameter("page", "页码").DataType("integer").DefaultValue("1")).
		Param(ws.QueryParameter("page_size", "每页大小").DataType("integer").DefaultValue("20")).
		Param(ws.QueryParameter("namespace", "命名空间").DataType("string")).
		Param(ws.QueryParameter("project", "项目").DataType("string")).
		Param(ws.QueryParameter("status", "状态").DataType("string")).
		Param(ws.QueryParameter("type", "类型").DataType("string")).
		Param(ws.QueryParameter("priority", "优先级").DataType("integer")).
		Param(ws.QueryParameter("creator", "创建者").DataType("string")).
		Param(ws.QueryParameter("queue_name", "队列名称").DataType("string")).
		Param(ws.QueryParameter("search_keyword", "搜索关键词").DataType("string")).
		Param(ws.QueryParameter("sort_by", "排序字段").DataType("string").DefaultValue("create_at")).
		Param(ws.QueryParameter("sort_order", "排序方向").DataType("string").DefaultValue("desc")).
		Param(ws.QueryParameter("create_time_start", "创建时间开始").DataType("integer")).
		Param(ws.QueryParameter("create_time_end", "创建时间结束").DataType("integer")).
		Returns(200, "OK", TaskListResp{}))

	// 工作负载详情
	ws.Route(ws.GET("/{namespace}/{name}").
		To(r.GetTask).
		Doc("获取工作负载详情").
		Metadata(metaK, metaV).
		Param(ws.PathParameter("namespace", "命名空间").DataType("string")).
		Param(ws.PathParameter("name", "工作负载名称").DataType("string")).
		Returns(200, "OK", TaskResp{}))

	// 工作负载统计
	// 	ws.Route(ws.GET("/stats").
	// 		To(r.GetTaskStats).
	// 		Doc("工作负载统计信息").
	// 		Metadata(metaK, metaV).
	// 		Param(ws.QueryParameter("namespace", "命名空间").DataType("string")).
	// 		Returns(200, "OK", TaskStatsResp{}))
	//
	// 	// 预定义查询
	// 	ws.Route(ws.GET("/running").
	// 		To(r.GetRunningTasks).
	// 		Doc("运行中的工作负载").
	// 		Metadata(metaK, metaV).
	// 		Param(ws.QueryParameter("page", "页码").DataType("integer").DefaultValue("1")).
	// 		Param(ws.QueryParameter("page_size", "每页大小").DataType("integer").DefaultValue("20")).
	// 		Returns(200, "OK", TaskListResp{}))
	//
	// 	ws.Route(ws.GET("/pending").
	// 		To(r.GetPendingTasks).
	// 		Doc("等待中的工作负载").
	// 		Metadata(metaK, metaV).
	// 		Param(ws.QueryParameter("page", "页码").DataType("integer").DefaultValue("1")).
	// 		Param(ws.QueryParameter("page_size", "每页大小").DataType("integer").DefaultValue("20")).
	// 		Returns(200, "OK", TaskListResp{}))
	//
	// 	ws.Route(ws.GET("/completed").
	// 		To(r.GetCompletedTasks).
	// 		Doc("已完成的工作负载").
	// 		Metadata(metaK, metaV).
	// 		Param(ws.QueryParameter("page", "页码").DataType("integer").DefaultValue("1")).
	// 		Param(ws.QueryParameter("page_size", "每页大小").DataType("integer").DefaultValue("20")).
	// 		Returns(200, "OK", TaskListResp{}))
	//
	// 	ws.Route(ws.GET("/gpu").
	// 		To(r.GetGPUTasks).
	// 		Doc("GPU工作负载").
	// 		Metadata(metaK, metaV).
	// 		Param(ws.QueryParameter("page", "页码").DataType("integer").DefaultValue("1")).
	// 		Param(ws.QueryParameter("page_size", "每页大小").DataType("integer").DefaultValue("20")).
	// 		Returns(200, "OK", TaskListResp{}))

	// 高级查询
	ws.Route(ws.POST("/query").
		To(r.QueryTasks).
		Doc("高级查询工作负载").
		Metadata(metaK, metaV).
		Reads(QueryReq{}).
		Returns(200, "OK", TaskListResp{}))

	// 查询指标
	ws.Route(ws.GET("/metrics").
		To(r.GetQueryMetrics).
		Doc("查询性能指标").
		Metadata(metaK, metaV).
		Returns(200, "OK", QueryMetricsResp{}))

	// 缓存管理
	ws.Route(ws.DELETE("/cache").
		To(r.ClearCache).
		Doc("清空查询缓存").
		Metadata(metaK, metaV).
		Returns(200, "OK", Resp{}))

	return ws
}
