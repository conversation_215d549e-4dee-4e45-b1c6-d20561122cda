package task

import (
	"strconv"

	"github.com/emicklei/go-restful/v3"
	"go.uber.org/zap"

	"transwarp.io/mlops/mlops-std/stdlog"
	"transwarp.io/mlops/pipeline/internal/core/task/model"
	"transwarp.io/mlops/pipeline/internal/core/task/query"
)

type TaskResp struct {
	*model.Task
	Duration int64 `json:"duration,omitempty"`
}

type TaskListResp struct {
	Items      []*TaskResp `json:"items"`
	Total      int32       `json:"total"`
	Page       int32       `json:"page"`
	PageSize   int32       `json:"page_size"`
	TotalPages int32       `json:"total_pages"`
	QueryTime  int64       `json:"query_time"`
}

// QueryRequest 查询请求
type QueryReq struct {
	Filters       []FilterSpec    `json:"filters"`
	Sorters       []SortSpec      `json:"sorters"`
	Pagination    *PaginationSpec `json:"pagination"`
	LabelSelector string          `json:"label_selector"`
}

// FilterSpec 过滤器规格
type FilterSpec struct {
	Field    string      `json:"field"`
	Operator string      `json:"operator"`
	Value    interface{} `json:"value"`
}

// SortSpec 排序规格
type SortSpec struct {
	Field     string `json:"field"`
	Direction string `json:"direction"`
}

// PaginationSpec 分页规格
type PaginationSpec struct {
	Page     int32 `json:"page"`
	PageSize int32 `json:"page_size"`
}

type Resp struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

func (this *TaskService) ListTaskWithQuery(request *restful.Request, response *restful.Response) {
	req := this.parseListRequest(request)

	result, err := this.ts.ListTasks(request.Request.Context(), req)
	if err != nil {
		stdlog.Error("Failed to list tasks", zap.Error(err))
		response.WriteErrorString(500, "Failed to list tasks: "+err.Error())
		return
	}

	resp := this.convertToListResp(result)
	response.WriteEntity(resp)
}

func (this *TaskService) GetTask(request *restful.Request, response *restful.Response) {
	namespace := request.PathParameter("namespace")
	name := request.PathParameter("name")

	if namespace == "" || name == "" {
		response.WriteErrorString(400, "namespace and name are required")
		return
	}

	workload, err := this.ts.GetTask(request.Request.Context(), namespace, name)
	if err != nil {
		stdlog.Error("Failed to get task",
			zap.String("namespace", namespace),
			zap.String("name", name),
			zap.Error(err))
		response.WriteErrorString(404, "Task not found")
		return
	}

	resp := this.convertToTaskResp(workload)
	response.WriteEntity(resp)
}

func (this *TaskService) parseListRequest(request *restful.Request) *model.ListTasksReq {
	req := &model.ListTasksReq{
		Page:      1,
		PageSize:  20,
		SortBy:    "create_at",
		SortOrder: "desc",
	}

	if page := request.QueryParameter("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			req.Page = int32(p)
		}
	}

	if pageSize := request.QueryParameter("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 {
			req.PageSize = int32(ps)
		}
	}

	req.Namespace = request.QueryParameter("namespace")
	req.ProjectName = request.QueryParameter("project")
	req.Creator = request.QueryParameter("creator")
	req.QueueName = request.QueryParameter("queue_name")
	req.SearchKeyword = request.QueryParameter("search_keyword")
	req.SortBy = request.QueryParameter("sort_by")
	req.SortOrder = request.QueryParameter("sort_order")

	if status := request.QueryParameters("status"); len(status) > 0 {
		req.Status = status
	}

	if types := request.QueryParameters("type"); len(types) > 0 {
		req.Type = types
	}

	if priorities := request.QueryParameters("priority"); len(priorities) > 0 {
		for _, p := range priorities {
			if priority, err := strconv.Atoi(p); err == nil {
				req.Priority = append(req.Priority, int32(priority))
			}
		}
	}

	if createTimeStart := request.QueryParameter("create_time_start"); createTimeStart != "" {
		if ts, err := strconv.ParseInt(createTimeStart, 10, 64); err == nil {
			req.CreateAtStart = &ts
		}
	}

	if createTimeEnd := request.QueryParameter("create_time_end"); createTimeEnd != "" {
		if ts, err := strconv.ParseInt(createTimeEnd, 10, 64); err == nil {
			req.CreateAtEnd = &ts
		}
	}

	return req
}

func (this *TaskService) convertToListResp(result *model.ListTasksResp) *TaskListResp {
	items := make([]*TaskResp, len(result.Items))
	for i, item := range result.Items {
		items[i] = this.convertToTaskResp(item)
	}

	return &TaskListResp{
		Items:      items,
		Total:      result.Total,
		Page:       result.Page,
		PageSize:   result.PageSize,
		TotalPages: result.TotalPages,
	}
}

func (this *TaskService) convertQueryResultToListResp(result *query.QueryResult[*model.Task]) *TaskListResp {
	items := make([]*TaskResp, len(result.Items))
	for i, item := range result.Items {
		items[i] = this.convertToTaskResp(item)
	}

	return &TaskListResp{
		Items:      items,
		Total:      result.Total,
		Page:       result.Page,
		PageSize:   result.PageSize,
		TotalPages: result.TotalPages,
		QueryTime:  result.QueryTime,
	}
}

func (this *TaskService) convertToTaskResp(wl *model.Task) *TaskResp {
	resp := &TaskResp{
		Task: wl,
	}

	// 计算运行时长
	if wl.StartAt != nil {
	}

	return resp
}
