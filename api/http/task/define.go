package task

import (
	"context"
	"net/http"
	"strconv"

	"github.com/emicklei/go-restful/v3"

	"transwarp.io/mlops/pipeline/api/http/helper"
	model "transwarp.io/mlops/pipeline/internal/core/model/task"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

func (this *TaskService) CreateTask(request *restful.Request, response *restful.Response) {
}

func (this *TaskService) DeleteTask(request *restful.Request, response *restful.Response) {
	id := request.PathParameter("id")
	ctx := context.Background()
	if err := this.tm.DeleteTask(ctx, id); err != nil {
		response.WriteErrorString(http.StatusInternalServerError, "Failed to list tasks: "+err.Error())
	}
	response.Flush()
}

func (this *TaskService) ListTasks(request *restful.Request, response *restful.Response) {
	req := this.parseListRequest(request)

	result, err := this.tm.ListTasks(request.Request.Context(), req)
	if err != nil {
		zlog.SugarErrorf("Failed to list tasks: %w", err)
		response.WriteErrorString(http.StatusInternalServerError, "Failed to list tasks: "+err.Error())
		return
	}

	response.WriteEntity(result)
}

func (this *TaskService) GetTask(request *restful.Request, response *restful.Response) {
	id := request.PathParameter("id")

	if id == "" {
		response.WriteErrorString(http.StatusBadRequest, "task id are required")
		return
	}

	task, err := this.tm.GetTask(request.Request.Context(), id)
	if err != nil {
		zlog.SugarErrorf("Failed to get task %s, error: %w", id, err)
		response.WriteErrorString(http.StatusInternalServerError, "Failed to get task: "+err.Error())
		return
	}

	response.WriteEntity(task)
}

func (this *TaskService) ReRun(request *restful.Request, response *restful.Response) {
	id := request.PathParameter("id")

	if id == "" {
		response.WriteErrorString(http.StatusBadRequest, "task id are required")
		return
	}
	// TODO rerun task
}

func (this *TaskService) parseListRequest(request *restful.Request) *model.ListTasksReq {
	req := &model.ListTasksReq{
		Page:      1,
		PageSize:  20,
		SortBy:    "create_at",
		SortOrder: "desc",
	}

	if page := request.QueryParameter("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			req.Page = int32(p)
		}
	}

	if pageSize := request.QueryParameter("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 {
			req.PageSize = int32(ps)
		}
	}

	req.SearchKeyword = request.QueryParameter("search_keyword")
	req.SortBy = request.QueryParameter("sort_by")
	req.SortOrder = request.QueryParameter("sort_order")

	if status := request.QueryParameters("status"); len(status) > 0 {
		req.Status = status
	}

	if types := request.QueryParameters("type"); len(types) > 0 {
		req.Types = types
	}

	if priorities := request.QueryParameters("priority"); len(priorities) > 0 {
		for _, p := range priorities {
			if priority, err := strconv.Atoi(p); err == nil {
				req.Priorities = append(req.Priorities, int32(priority))
			}
		}
	}

	if createAtStart := request.QueryParameter("create_at_start"); createAtStart != "" {
		if ts, err := strconv.ParseInt(createAtStart, 10, 64); err == nil {
			req.CreateAtStart = &ts
		}
	}

	if createAtEnd := request.QueryParameter("create_at_end"); createAtEnd != "" {
		if ts, err := strconv.ParseInt(createAtEnd, 10, 64); err == nil {
			req.CreateAtEnd = &ts
		}
	}

	return req
}

func (this *TaskService) ListTaskTypes(request *restful.Request, response *restful.Response) {
	lang := helper.GetLanguageFromRequest(request.Request)

	resp := &ListTypesResp{
		Items: getEnumItems(lang, typeData),
	}

	if err := response.WriteEntity(resp); err != nil {
		zlog.SugarErrorf("Error writing response for ListTaskTypes: %v", err)
		response.WriteErrorString(http.StatusInternalServerError, "Failed to write response for task types")
	}
}

func (this *TaskService) ListTaskPriorities(request *restful.Request, response *restful.Response) {
	lang := helper.GetLanguageFromRequest(request.Request)

	resp := &ListPrioritiesResp{
		Items: getEnumItems(lang, priorityData),
	}

	if err := response.WriteEntity(resp); err != nil {
		zlog.SugarErrorf("Error writing response for ListTaskPriorities: %v", err)
		response.WriteErrorString(http.StatusInternalServerError, "Failed to write response for task priorities")
	}
}

func (this *TaskService) ListTaskStatuses(request *restful.Request, response *restful.Response) {
	lang := helper.GetLanguageFromRequest(request.Request)

	resp := &ListPrioritiesResp{
		Items: getEnumItems(lang, statusesData),
	}

	if err := response.WriteEntity(resp); err != nil {
		zlog.SugarErrorf("Error writing response for ListTaskStatuses: %v", err)
		response.WriteErrorString(http.StatusInternalServerError, "Failed to write response for task priorities")
	}
}

type ListTypesResp struct {
	Items []EnumItem `json:"items"`
}

type ListPrioritiesResp struct {
	Items []EnumItem `json:"items"`
}
type ListStatusesResp struct {
	Items []EnumItem `json:"items"`
}

type EnumItem struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
}

var typeData = map[string][]EnumItem{
	helper.LANG_ZH: {
		{ID: string(model.TaskTypePipeline), Name: "工作流", Description: "工作流任务"},
		{ID: string(model.TaskTypeTraining), Name: "训练", Description: "训练任务"},
		{ID: string(model.TaskTypeInference), Name: "推理", Description: "推理任务"},
		{ID: string(model.TaskTypeEvaluation), Name: "评估", Description: "评估任务"},
		{ID: string(model.TaskTypeDataProcess), Name: "数据处理", Description: "数据处理任务"},
		{ID: string(model.TaskTypeCustom), Name: "自定义", Description: "自定义任务"},
	},
	helper.LANG_EN: {
		{ID: string(model.TaskTypePipeline), Name: "pipeline", Description: "Pipeline task"},
		{ID: string(model.TaskTypeTraining), Name: "train", Description: "Training task"},
		{ID: string(model.TaskTypeInference), Name: "inference", Description: "Inference task"},
		{ID: string(model.TaskTypeEvaluation), Name: "evaluation", Description: "Evaluation task"},
		{ID: string(model.TaskTypeDataProcess), Name: "data_process", Description: "Data processing task"},
		{ID: string(model.TaskTypeCustom), Name: "custom", Description: "Custom task"},
	},
}

var priorityData = map[string][]EnumItem{
	helper.LANG_ZH: {
		{ID: string(model.PriorityLow), Name: "低", Description: "低优先级任务"},
		{ID: string(model.PriorityMedium), Name: "中", Description: "中优先级任务"},
		{ID: string(model.PriorityHigh), Name: "高", Description: "高优先级任务"},
	},
	helper.LANG_EN: {
		{ID: string(model.PriorityLow), Name: "low", Description: "Low priority task"},
		{ID: string(model.PriorityMedium), Name: "medium", Description: "Medium priority task"},
		{ID: string(model.PriorityHigh), Name: "high", Description: "High priority task"},
	},
}

var statusesData = map[string][]EnumItem{
	helper.LANG_ZH: {
		{ID: string(model.StatusPending), Name: "排队中", Description: "排队中"},
		// {ID: string(model.StatusAdmitted), Name: "已提交", Description: "已提交"},
		{ID: string(model.StatusRunning), Name: "运行中", Description: "运行中"},
		{ID: string(model.StatusSucceeded), Name: "完成", Description: "完成"},
		{ID: string(model.StatusFailed), Name: "失败", Description: "失败"},
		{ID: string(model.StatusCancelled), Name: "已取消", Description: "已取消"},
		// {ID: string(model.StatusSuspended), Name: "挂起", Description: "挂起"},
	},
	helper.LANG_EN: {
		{ID: string(model.StatusPending), Name: "pending", Description: "pending"},
		// {ID: string(model.StatusAdmitted), Name: "Admitted", Description: "Admitted"},
		{ID: string(model.StatusRunning), Name: "running", Description: "running"},
		{ID: string(model.StatusSucceeded), Name: "succeeded", Description: "succeeded"},
		{ID: string(model.StatusFailed), Name: "failed", Description: "failed"},
		{ID: string(model.StatusCancelled), Name: "cancelled", Description: "cancelled"},
		// {ID: string(model.StatusSuspended), Name: "Suspended", Description: "Suspended"},
	},
}

func getEnumItems(lang string, data map[string][]EnumItem) []EnumItem {
	items, ok := data[lang]
	if !ok {
		zlog.SugarWarnf("Warning: Language '%s' not fully supported, falling back to '%s'", lang, helper.LANG_ZH)
		return data[helper.LANG_ZH]
	}
	return items
}
