package task

import (
	"strconv"
	"time"

	"github.com/emicklei/go-restful/v3"
	"go.uber.org/zap"

	"transwarp.io/mlops/mlops-std/stdlog"
	"transwarp.io/mlops/pipeline/internal/core/task/model"
	"transwarp.io/mlops/pipeline/internal/core/task/query"
)

// TaskResp 工作负载响应
type TaskResp struct {
	*model.Task
	Duration int64 `json:"duration,omitempty"` // 运行时长(秒)
}

// TaskListResp 工作负载列表响应
type TaskListResp struct {
	Items      []*TaskResp `json:"items"`
	Total      int32       `json:"total"`
	Page       int32       `json:"page"`
	PageSize   int32       `json:"page_size"`
	TotalPages int32       `json:"total_pages"`
	QueryTime  int64       `json:"query_time"`
}

// TaskStatsResp 工作负载统计响应
type TaskStatsResp struct {
	Total      int32              `json:"total"`
	ByStatus   map[string]int     `json:"by_status"`
	ByPriority map[int32]int      `json:"by_priority"`
	ByType     map[string]int     `json:"by_type"`
	Resources  *ResourceStatsResp `json:"resources"`
}

// ResourceStatsResp 资源统计响应
type ResourceStatsResp struct {
	CPU       ResourceMetrics `json:"cpu"`
	Memory    ResourceMetrics `json:"memory"`
	GPU       ResourceMetrics `json:"gpu"`
	GPUMemory ResourceMetrics `json:"gpu_memory"`
}

// ResourceMetrics 资源指标
type ResourceMetrics struct {
	Requested int64 `json:"requested"`
	Used      int64 `json:"used"`
	Limit     int64 `json:"limit"`
}

// QueryRequest 查询请求
type QueryReq struct {
	Filters       []FilterSpec    `json:"filters"`
	Sorters       []SortSpec      `json:"sorters"`
	Pagination    *PaginationSpec `json:"pagination"`
	LabelSelector string          `json:"label_selector"`
}

// FilterSpec 过滤器规格
type FilterSpec struct {
	Field    string      `json:"field"`
	Operator string      `json:"operator"`
	Value    interface{} `json:"value"`
}

// SortSpec 排序规格
type SortSpec struct {
	Field     string `json:"field"`
	Direction string `json:"direction"`
}

// PaginationSpec 分页规格
type PaginationSpec struct {
	Page     int32 `json:"page"`
	PageSize int32 `json:"page_size"`
}

// QueryMetricsResp 查询指标响应
type QueryMetricsResp struct {
	TotalQueries  int64         `json:"total_queries"`
	CacheHits     int64         `json:"cache_hits"`
	CacheMisses   int64         `json:"cache_misses"`
	CacheHitRate  float64       `json:"cache_hit_rate"`
	AverageTime   time.Duration `json:"average_time"`
	SlowQueries   int64         `json:"slow_queries"`
	ErrorQueries  int64         `json:"error_queries"`
	LastQueryTime time.Time     `json:"last_query_time"`
}

// Resp 通用响应
type Resp struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// ListTaskWithQuery 使用查询框架的工作负载列表
func (this *TaskService) ListTaskWithQuery(request *restful.Request, response *restful.Response) {
	// 解析查询参数
	req := this.parseListRequest(request)

	// 调用服务
	result, err := this.ts.ListTasks(request.Request.Context(), req)
	if err != nil {
		stdlog.Error("Failed to list tasks", zap.Error(err))
		response.WriteErrorString(500, "Failed to list tasks: "+err.Error())
		return
	}

	// 转换响应
	resp := this.convertToListResp(result)
	response.WriteEntity(resp)
}

// GetTask 获取工作负载详情
func (this *TaskService) GetTask(request *restful.Request, response *restful.Response) {
	namespace := request.PathParameter("namespace")
	name := request.PathParameter("name")

	if namespace == "" || name == "" {
		response.WriteErrorString(400, "namespace and name are required")
		return
	}

	workload, err := this.ts.GetTask(request.Request.Context(), namespace, name)
	if err != nil {
		stdlog.Error("Failed to get task",
			zap.String("namespace", namespace),
			zap.String("name", name),
			zap.Error(err))
		response.WriteErrorString(404, "Task not found")
		return
	}

	resp := this.convertToTaskResp(workload)
	response.WriteEntity(resp)
}

// GetTaskStats 获取工作负载统计
// func (this *TaskService) GetTaskStats(request *restful.Request, response *restful.Response) {
// 	namespace := request.QueryParameter("namespace")
//
// 	stats, err := this.ts.GetTaskStats(request.Request.Context(), namespace)
// 	if err != nil {
// 		stdlog.Error("Failed to get workload stats", zap.Error(err))
// 		response.WriteErrorString(500, "Failed to get workload stats: "+err.Error())
// 		return
// 	}
//
// 	response.WriteEntity(stats)
// }

// GetRunningTasks 获取运行中的工作负载
// func (this *TaskService) GetRunningTasks(request *restful.Request, response *restful.Response) {
// 	this.executePreDefinedQuery(request, response, query.RunningTasks())
// }
//
// // GetPendingTasks 获取等待中的工作负载
// func (this *TaskService) GetPendingTasks(request *restful.Request, response *restful.Response) {
// 	this.executePreDefinedQuery(request, response, query.PendingTasks())
// }
//
// // GetCompletedTasks 获取已完成的工作负载
// func (this *TaskService) GetCompletedTasks(request *restful.Request, response *restful.Response) {
// 	this.executePreDefinedQuery(request, response, query.CompletedTasks())
// }
//
// // GetGPUTasks 获取GPU工作负载
// func (this *TaskService) GetGPUTasks(request *restful.Request, response *restful.Response) {
// 	this.executePreDefinedQuery(request, response, query.GPUTasks())
// }

// QueryTasks 高级查询工作负载
func (this *TaskService) QueryTasks(request *restful.Request, response *restful.Response) {
	var queryReq QueryReq
	if err := request.ReadEntity(&queryReq); err != nil {
		response.WriteErrorString(400, "Invalid request body: "+err.Error())
		return
	}

	// 构建查询
	queryBuilder := this.buildQueryFromRequest(&queryReq)

	// 执行查询
	result, err := this.ts.QueryTasks(request.Request.Context(), queryBuilder)
	if err != nil {
		stdlog.Error("Failed to execute query", zap.Error(err))
		response.WriteErrorString(500, "Failed to execute query: "+err.Error())
		return
	}

	// 转换响应
	resp := this.convertQueryResultToListResp(result)
	response.WriteEntity(resp)
}

// GetQueryMetrics 获取查询指标
func (this *TaskService) GetQueryMetrics(request *restful.Request, response *restful.Response) {
	metrics := this.ts.GetQueryMetrics()
	cacheStats := this.ts.GetCacheStats()

	total := cacheStats.Hits + cacheStats.Misses
	hitRate := float64(0)
	if total > 0 {
		hitRate = float64(cacheStats.Hits) / float64(total)
	}

	resp := &QueryMetricsResp{
		TotalQueries:  metrics.TotalQueries,
		CacheHits:     cacheStats.Hits,
		CacheMisses:   cacheStats.Misses,
		CacheHitRate:  hitRate,
		AverageTime:   metrics.AverageTime,
		SlowQueries:   metrics.SlowQueries,
		ErrorQueries:  metrics.ErrorQueries,
		LastQueryTime: metrics.LastQueryTime,
	}

	response.WriteEntity(resp)
}

// ClearCache 清空查询缓存
func (this *TaskService) ClearCache(request *restful.Request, response *restful.Response) {
	this.ts.ClearQueryCache()

	resp := Resp{
		Code:    200,
		Message: "Cache cleared successfully",
	}
	response.WriteEntity(resp)
}

// parseListRequest 解析列表请求参数
func (this *TaskService) parseListRequest(request *restful.Request) *model.ListTasksReq {
	req := &model.ListTasksReq{
		Page:      1,
		PageSize:  20,
		SortBy:    "create_at",
		SortOrder: "desc",
	}

	// 解析分页参数
	if page := request.QueryParameter("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			req.Page = int32(p)
		}
	}

	if pageSize := request.QueryParameter("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 {
			req.PageSize = int32(ps)
		}
	}

	// 解析过滤参数
	req.Namespace = request.QueryParameter("namespace")
	req.ProjectName = request.QueryParameter("project")
	req.Creator = request.QueryParameter("creator")
	req.QueueName = request.QueryParameter("queue_name")
	req.SearchKeyword = request.QueryParameter("search_keyword")
	req.SortBy = request.QueryParameter("sort_by")
	req.SortOrder = request.QueryParameter("sort_order")

	// 解析数组参数
	if status := request.QueryParameters("status"); len(status) > 0 {
		req.Status = status
	}

	if types := request.QueryParameters("type"); len(types) > 0 {
		req.Type = types
	}

	if priorities := request.QueryParameters("priority"); len(priorities) > 0 {
		for _, p := range priorities {
			if priority, err := strconv.Atoi(p); err == nil {
				req.Priority = append(req.Priority, int32(priority))
			}
		}
	}

	// 解析时间范围
	if createTimeStart := request.QueryParameter("create_time_start"); createTimeStart != "" {
		if ts, err := strconv.ParseInt(createTimeStart, 10, 64); err == nil {
			req.CreateTimeStart = &ts
		}
	}

	if createTimeEnd := request.QueryParameter("create_time_end"); createTimeEnd != "" {
		if ts, err := strconv.ParseInt(createTimeEnd, 10, 64); err == nil {
			req.CreateTimeEnd = &ts
		}
	}

	return req
}

// executePreDefinedQuery 执行预定义查询
func (this *TaskService) executePreDefinedQuery(request *restful.Request, response *restful.Response, queryBuilder *query.TaskQuery) {
	// 解析分页参数
	page := int32(1)
	pageSize := int32(20)

	if p := request.QueryParameter("page"); p != "" {
		if parsed, err := strconv.Atoi(p); err == nil && parsed > 0 {
			page = int32(parsed)
		}
	}

	if ps := request.QueryParameter("page_size"); ps != "" {
		if parsed, err := strconv.Atoi(ps); err == nil && parsed > 0 {
			pageSize = int32(parsed)
		}
	}

	// 应用分页
	queryBuilder.Limit(page, pageSize)

	// 执行查询
	result, err := this.ts.QueryTasks(request.Request.Context(), queryBuilder)
	if err != nil {
		stdlog.Error("Failed to execute predefined query", zap.Error(err))
		response.WriteErrorString(500, "Failed to execute query: "+err.Error())
		return
	}

	// 转换响应
	resp := this.convertQueryResultToListResp(result)
	response.WriteEntity(resp)
}

// buildQueryFromRequest 从请求构建查询
func (this *TaskService) buildQueryFromRequest(req *QueryReq) *query.TaskQuery {
	queryBuilder := query.NewTaskQuery()

	// 应用过滤器
	for _, filter := range req.Filters {
		operator := this.convertOperator(filter.Operator)
		queryBuilder.WhereField(filter.Field, operator, filter.Value)
	}

	// 应用排序
	for _, sorter := range req.Sorters {
		direction := query.SortAsc
		if sorter.Direction == "desc" {
			direction = query.SortDesc
		}
		queryBuilder.OrderBy(sorter.Field, direction)
	}

	// 应用分页
	if req.Pagination != nil {
		queryBuilder.Limit(req.Pagination.Page, req.Pagination.PageSize)
	}

	// 应用标签选择器
	if req.LabelSelector != "" {
		queryBuilder.WhereLabelMatches(req.LabelSelector)
	}

	return queryBuilder
}

// convertOperator 转换操作符
func (this *TaskService) convertOperator(op string) query.Operator {
	switch op {
	case "eq", "equal":
		return query.OpEqual
	case "ne", "not_equal":
		return query.OpNotEqual
	case "gt", "greater_than":
		return query.OpGreaterThan
	case "gte", "greater_than_or_equal":
		return query.OpGreaterThanOrEqual
	case "lt", "less_than":
		return query.OpLessThan
	case "lte", "less_than_or_equal":
		return query.OpLessThanOrEqual
	case "in":
		return query.OpIn
	case "nin", "not_in":
		return query.OpNotIn
	case "contains":
		return query.OpContains
	case "starts", "starts_with":
		return query.OpStartsWith
	case "ends", "ends_with":
		return query.OpEndsWith
	case "regex":
		return query.OpRegex
	case "null", "is_null":
		return query.OpIsNull
	case "notnull", "is_not_null":
		return query.OpIsNotNull
	case "between":
		return query.OpBetween
	default:
		return query.OpEqual
	}
}

// convertToListResp 转换为列表响应
func (this *TaskService) convertToListResp(result *model.ListTasksResp) *TaskListResp {
	items := make([]*TaskResp, len(result.Items))
	for i, item := range result.Items {
		items[i] = this.convertToTaskResp(item)
	}

	return &TaskListResp{
		Items:      items,
		Total:      result.Total,
		Page:       result.Page,
		PageSize:   result.PageSize,
		TotalPages: result.TotalPages,
	}
}

// convertQueryResultToListResp 转换查询结果为列表响应
func (this *TaskService) convertQueryResultToListResp(result *query.QueryResult[*model.Task]) *TaskListResp {
	items := make([]*TaskResp, len(result.Items))
	for i, item := range result.Items {
		items[i] = this.convertToTaskResp(item)
	}

	return &TaskListResp{
		Items:      items,
		Total:      result.Total,
		Page:       result.Page,
		PageSize:   result.PageSize,
		TotalPages: result.TotalPages,
		QueryTime:  result.QueryTime,
	}
}

// convertToTaskResp 转换为工作负载响应
func (this *TaskService) convertToTaskResp(wl *model.Task) *TaskResp {
	resp := &TaskResp{
		Task: wl,
	}

	// 计算运行时长
	if wl.StartAt != nil {
		duration := wl.CalculateDuration()
		resp.Duration = int64(duration.Seconds())
	}

	return resp
}
