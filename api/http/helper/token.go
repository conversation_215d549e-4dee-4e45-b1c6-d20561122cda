package helper

import (
	"context"

	"github.com/emicklei/go-restful/v3"
	"google.golang.org/grpc/metadata"
	token "transwarp.io/mlops/mlops-std/token"
)

//func ContextWithToken(request *restful.Request) context.Context {
//	t := request.Request.Header.Get(token.AUTH_HEADER)
//	if t != "" {
//		ctx := metadata.NewOutgoingContext(context.Background(), metadata.Pairs(token.AUTH_HEADER, t))
//		return ctx
//	} else {
//		return context.Background()
//	}
//}

func ContextWithToken(request *restful.Request) context.Context {
	//TODO Still send cookies parameters if token disappear while cookies is exist.

	//SET COOKIE
	MD := make(map[string]string)
	cookies := request.Request.Cookies()
	for _, cookie := range cookies {
		MD[cookie.Name] = cookie.Value
	}

	//SET auth
	t := request.Request.Header.Get(token.AUTH_HEADER)
	MD[token.AUTH_HEADER] = t
	var ctx context.Context
	if len(MD) != 0 {
		ctx = metadata.NewIncomingContext(context.Background(), metadata.New(MD))
	} else {
		ctx = context.Background()
	}
	return ctx
}
