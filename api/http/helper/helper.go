package helper

import (
	"net/http"
	"strings"
)

const (
	langKey = "lang"

	LANG_ZH = "zh"
	LANG_EN = "en"
)

func GetLanguageFromRequest(r *http.Request) string {
	// 1. Try to get 'lang' from query parameter
	lang := r.URL.Query().Get(langKey)
	if lang != "" {
		return strings.ToLower(lang)
	}

	// 2. If not in query, try to get 'lang' from cookie
	cookie, err := r.<PERSON>(lang<PERSON><PERSON>)
	if err == nil && cookie.Value != "" {
		return strings.ToLower(cookie.Value)
	}

	// 3. Default to Chinese if neither is found
	return LANG_ZH
}
