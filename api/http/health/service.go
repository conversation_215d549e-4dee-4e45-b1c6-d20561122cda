package health

import (
	"fmt"
	"net/http"

	restfulspec "github.com/emicklei/go-restful-openapi"
	"github.com/emicklei/go-restful/v3"

	service "transwarp.io/mlops/pipeline/api/http/service"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

type HealthService struct {
	ServiceName string
	rootPath    string
}

func init() {
	s := &HealthService{
		ServiceName: "HealthService",
		rootPath:    fmt.Sprintf("%s%s", service.V1(), "/health"),
	}
	service.RegisterService(s)
}

func (s *HealthService) Name() string {
	return s.ServiceName
}

func (s *HealthService) NewWebService() *restful.WebService {
	tags := []string{"Health Check"}
	metaK, metaV := restfulspec.KeyOpenAPITags, tags
	ws := new(restful.WebService)
	ws.Path(s.rootPath).
		Consumes(restful.MIME_JSON, restful.MIME_OCTET).
		Produces(restful.MIME_JSON, restful.MIME_OCTET)

	ws.Route(ws.GET("/healthz").To(s.Healthz).
		Doc("liveness probe").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), HealthResp{}))
	ws.Route(ws.GET("/readyz").To(s.Healthz).
		Doc("readiness probe").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), HealthResp{}))
	ws.Route(ws.GET("/status").To(s.Healthz).
		Doc("server status").Metadata(restfulspec.KeyOpenAPITags, tags).Metadata(metaK, metaV).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), HealthResp{}))

	return ws
}

func (s *HealthService) Healthz(req *restful.Request, resp *restful.Response) {
	successResponse(resp, HealthOK)
}

func (s *HealthService) Readyz(req *restful.Request, resp *restful.Response) {
	successResponse(resp, HealthOK)
}

// TODO server detail status
func (s *HealthService) Status(req *restful.Request, resp *restful.Response) {
	successResponse(resp, HealthOK)
}

func successResponse(response *restful.Response, value interface{}) {
	err := response.WriteHeaderAndEntity(http.StatusOK, value)
	if err != nil {
		zlog.SugarErrorf("restful: error while writing header and entity")
	}
}
