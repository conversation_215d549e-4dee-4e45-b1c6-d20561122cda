package health

import "time"

const (
	StatusUp   = "UP"
	StatusDown = "DOWN"
)

var (
	HealthOK = HealthResp{
		Status:    StatusUp,
		Timestamp: time.Now().Unix(),
		Details:   make(map[string]any),
	}
)

type HealthResp struct {
	Status    string         `json:"status"`
	Timestamp int64          `json:"timestamp"`
	Details   map[string]any `json:"details"`
	Error     string         `json:"error,omitempty"`
}

type HealthDetails struct {
	Database   ServiceStatus `json:"database"`
	Cache      ServiceStatus `json:"cache"`
	ThirdParty ServiceStatus `json:"third_party"`
}

type ServiceStatus struct {
	Status  string `json:"status"`
	Latency int64  `json:"latency"`
	Message string `json:"message"`
}
