package swagger

import (
	"embed"
	"fmt"
	"net/http"
	"os"
	"path"
	"sort"
	"strconv"
	"strings"
	"unicode"

	restfulspec "github.com/emicklei/go-restful-openapi/v2"
	restful "github.com/emicklei/go-restful/v3"
	"github.com/go-openapi/spec"
	"github.com/spf13/viper"
	"transwarp.io/mlops/pipeline/api/http/service"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

var (
	//go:embed swagger-ui/*
	swaggerFiles embed.FS

	swaggerUIRoot = "swagger-ui"
)

type SwaggerUIService struct {
	ServiceName string
	rootPath    string
}

func init() {
	s := &SwaggerUIService{
		ServiceName: "SwaggerUI",
		rootPath:    "/apidocs",
	}
	isSwaggerEnabled := func() bool {
		enabledStr := os.Getenv("SWAGGER_ENABLED")
		if enabledStr == "" {
			return true
		}
		enabled, err := strconv.ParseBool(enabledStr)
		if err != nil {
			return false
		}
		return enabled
	}()
	if !isSwaggerEnabled {
		return
	}
	service.RegisterService(s)
}

func (s *SwaggerUIService) Name() string {
	return s.ServiceName
}

func (s *SwaggerUIService) NewWebService() *restful.WebService {
	ws := new(restful.WebService)
	ws.Path(s.rootPath).
		Consumes(restful.MIME_JSON, restful.MIME_OCTET)

	ws.Route(ws.GET("/version.json").To(s.getVersion))
	ws.Route(ws.GET("/environments").To(s.listConfigEnvKeys).Doc("env variables").Writes([]string{}))
	ws.Route(ws.GET("/swagger.json").To(s.getSwagger))
	ws.Route(ws.GET("/").To(s.getSwaggerUI))
	ws.Route(ws.GET("{subpath}").Param(restful.PathParameter("subpath", "sub path")).To(s.getSwaggerUIResources))

	return ws
}

func (s *SwaggerUIService) getSwagger(request *restful.Request, response *restful.Response) {
	config := restfulspec.Config{
		WebServices: restful.RegisteredWebServices(),
		SchemaFormatHandler: func(typeName string) string {
			return typeName
		},
		DefinitionNameHandler: func(s string) string {
			if len(s) > 0 && unicode.IsUpper(rune(s[0])) {
				return s
			}
			return ""
		},
	}
	swagger := restfulspec.BuildSwagger(config)
	swagger.Info = &spec.Info{
		InfoProps: spec.InfoProps{
			Description: "API Docs for SOPHON MLOPS Pipeline",
			Title:       "Vision API DOCS",
			Version:     "1.0.0",
		},
	}

	swagger.Schemes = []string{"https", "http"}
	if request.Request.Header.Get("X-Forwarded-Proto") == "" {
		swagger.Schemes = []string{"http", "https"}
	}

	if err := response.WriteHeaderAndEntity(http.StatusOK, swagger); err != nil {
		zlog.SugarErrorf("write response")
	}
}

func (s *SwaggerUIService) listConfigEnvKeys(request *restful.Request, response *restful.Response) {
	keys := viper.AllKeys()
	sort.Strings(keys)
	for i, key := range keys {
		keys[i] = "MLOPS_PIPELINE" + "_" + strings.ToUpper(key)
	}
	response.WriteHeaderAndEntity(http.StatusOK, keys)
}

func (s *SwaggerUIService) getVersion(request *restful.Request, response *restful.Response) {
	response.WriteHeaderAndEntity(http.StatusOK, getVersion())
}

type VersionInfo struct {
	BuildName    string
	BuildVersion string
	BuildTime    string
	CommitId     string
}

func getVersion() map[string]*VersionInfo {
	appVersion := make(map[string]*VersionInfo)
	appVersion["pipeline"] = &VersionInfo{
		BuildName:    "",
		BuildVersion: "",
		BuildTime:    "",
		CommitId:     "",
	}
	return appVersion
}

func (s *SwaggerUIService) getSwaggerUI(request *restful.Request, response *restful.Response) {
	content, err := swaggerFiles.ReadFile(fmt.Sprintf("%s/%s", swaggerUIRoot, "index.html"))
	if err != nil {
		response.WriteError(http.StatusInternalServerError, err)
		return
	}
	response.Header().Set("Content-Type", "text/html; charset=utf-8")
	response.Write(content)
}

func (s *SwaggerUIService) getSwaggerUIResources(req *restful.Request, resp *restful.Response) {
	param := req.PathParameter("subpath")
	subpath := path.Join(swaggerUIRoot, param)
	if param == "" {
		subpath = path.Join(swaggerUIRoot, "index.html")
	}
	content, err := swaggerFiles.ReadFile(subpath)
	if err != nil {
		resp.WriteError(http.StatusNotFound, err)
		return
	}
	contentType := ""
	ext := strings.ToLower(path.Ext(param))
	switch ext {
	case ".css":
		contentType = "text/css"
	case ".js":
		contentType = "application/javascript"
	case ".png":
		contentType = "image/png"
	case ".jpg", ".jpeg":
		contentType = "image/jpeg"
	case ".gif":
		contentType = "image/gif"
	case ".svg":
		contentType = "image/svg+xml"
	case ".json":
		contentType = "application/json"
	case ".html":
		contentType = "text/html"
	default:
		contentType = http.DetectContentType(content)
	}

	if contentType != "" {
		resp.Header().Set("Content-Type", contentType)
	}

	resp.Write(content)
}
