package swagger

import (
	"embed"
	"fmt"
	"net/http"
	"path"

	restfulspec "github.com/emicklei/go-restful-openapi/v2"
	restful "github.com/emicklei/go-restful/v3"
	"github.com/go-openapi/spec"
	"transwarp.io/mlops/pipeline/api/http/service"
	config "transwarp.io/mlops/pipeline/conf"
)

var (
	//go:embed swagger-ui/*
	swaggerFiles embed.FS

	swaggerUIRoot = "swagger-ui"
)

type SwaggerUIService struct {
	ServiceName string
	rootPath    string
}

func init() {
	s := &SwaggerUIService{
		ServiceName: "SwaggerUI",
		rootPath:    "/apidocs",
	}
	service.RegisterService(s)
}

func (s *SwaggerUIService) Name() string {
	return s.ServiceName
}

func (s *SwaggerUIService) NewWebService() *restful.WebService {
	ws := new(restful.WebService)
	ws.Path(s.rootPath).
		Consumes(restful.MIME_JSON, restful.MIME_OCTET).
		Produces(restful.MIME_JSON, restful.MIME_OCTET)

	ws.Route(ws.GET("/").To(s.GetSwaggerUI))
	ws.Route(ws.GET("/swagger.json").To(s.GetSwagger))
	ws.Route(ws.GET("/{subpath:*}").To(s.GetSwaggerUIResources))

	return ws
}

func (s *SwaggerUIService) GetSwagger(request *restful.Request, response *restful.Response) {
	config := restfulspec.Config{
		WebServices:                   restful.RegisteredWebServices(),
		PostBuildSwaggerObjectHandler: s.enrichSwaggerObject,
	}
	swagger := restfulspec.BuildSwagger(config)
	for index, d := range swagger.Definitions {
		i := 0
		for _, r := range d.SchemaProps.Required {
			if r != "unknownFields" && r != "sizeCache" && r != "state" { // 满足条件的元素向前移动
				swagger.Definitions[index].Required[i] = r
				i++
			}
		}
		definition := swagger.Definitions[index]
		definition.Required = swagger.Definitions[index].Required[:i]
		swagger.Definitions[index] = swagger.Definitions[index]

		for k, _ := range d.SchemaProps.Properties {
			if k == "unknownFields" || k == "sizeCache" || k == "state" { // 满足条件的元素向前移动
				delete(d.SchemaProps.Properties, k)
			}
		}
	}
	response.WriteEntity(swagger)
}

func (s *SwaggerUIService) GetSwaggerUI(request *restful.Request, response *restful.Response) {
	content, err := swaggerFiles.ReadFile(fmt.Sprintf("%s/%s", swaggerUIRoot, "index.html"))
	if err != nil {
		response.WriteError(http.StatusInternalServerError, err)
		return
	}
	response.Write(content)
}

func (s *SwaggerUIService) GetSwaggerUIResources(req *restful.Request, resp *restful.Response) {
	param := req.PathParameter("subpath")
	subpath := path.Join(swaggerUIRoot, param)
	if param == "" {
		subpath = path.Join(swaggerUIRoot, "index.html")
	}
	content, err := swaggerFiles.ReadFile(subpath)
	if err != nil {
		resp.WriteError(http.StatusNotFound, err)
		return
	}
	resp.Write(content)
}

func (s *SwaggerUIService) enrichSwaggerObject(swo *spec.Swagger) {
	swo.Schemes = []string{"https"}
	basePrefix := config.PipeineConfig.MLOPS.HttpServer.ServicePrefix
	if len(basePrefix) == 0 || basePrefix == "" {
		basePrefix = "/gateway/pipeline"
	}
	swo.BasePath = basePrefix

	swo.Info = &spec.Info{
		InfoProps: spec.InfoProps{
			Title:       "Mlops-Pipeline API",
			Description: "API Document For Service(Mlops-Pipeline) of Sophon",

			Contact: &spec.ContactInfo{
				ContactInfoProps: spec.ContactInfoProps{
					Name:  "Sophon MLOps-Pipeline of Tranwarp backend document",
					Email: "<EMAIL>",
					URL:   "https://www.transwarp.cn",
				},
			},
			License: &spec.License{
				LicenseProps: spec.LicenseProps{
					Name: "Private Licence",
					URL:  "https://www.transwarp.cn",
				},
			},
			Version: "dev",
		},
	}

	swo.SecurityDefinitions = spec.SecurityDefinitions{
		"Authorization": spec.APIKeyAuth("Authorization", "header"),
		//TODO 修改提供登录和鉴权的地址
		"oauth2": spec.OAuth2AccessToken("https://tw-node128:8393/cas/login", "https://tw-node128:8393/cas/login"),
	}

}
