package workload

import (
	"time"

	"github.com/emicklei/go-restful/v3"
)

// WorkloadType 工作负载类型枚举
type WorkloadType string

const (
	WorkloadTypePipeline    WorkloadType = "PIPELINE"     // 流水线
	WorkloadTypeTraining    WorkloadType = "TRAINING"     // 模型训练
	WorkloadTypeInference   WorkloadType = "INFERENCE"    // 模型推理
	WorkloadTypeEvaluation  WorkloadType = "EVALUATION"   // 模型评估
	WorkloadTypeDataProcess WorkloadType = "DATA_PROCESS" // 数据处理
	WorkloadTypeCustom      WorkloadType = "CUSTOM"       // 自定义
)

// WorkloadPriority 工作负载优先级枚举
type WorkloadPriority int32

const (
	PriorityLow    WorkloadPriority = 0 // 低优先级
	PriorityNormal WorkloadPriority = 1 // 普通优先级
	PriorityHigh   WorkloadPriority = 2 // 高优先级
	PriorityUrgent WorkloadPriority = 3 // 紧急优先级
)

// WorkloadStatus 工作负载状态枚举
type WorkloadStatus string

const (
	StatusPending   WorkloadStatus = "Pending"   // 等待中
	StatusAdmitted  WorkloadStatus = "Admitted"  // 已接受
	StatusRunning   WorkloadStatus = "Running"   // 运行中
	StatusSucceeded WorkloadStatus = "Succeeded" // 成功
	StatusFailed    WorkloadStatus = "Failed"    // 失败
	StatusCancelled WorkloadStatus = "Cancelled" // 已取消
	StatusSuspended WorkloadStatus = "Suspended" // 已暂停
)

// ResourceUsage 资源使用情况值对象
type ResourceUsage struct {
	Used      int64 `json:"used"`      // 已使用
	Allocated int64 `json:"allocated"` // 已分配
	Limit     int64 `json:"limit"`     // 限额
}

// Workload 工作负载聚合根
type Workload struct {
	// 基本信息
	ID        string           `json:"id"`        // 工作负载ID
	Name      string           `json:"name"`      // 任务名称
	Namespace string           `json:"namespace"` // 命名空间
	Project   string           `json:"project"`   // 所属空间/项目
	Type      WorkloadType     `json:"type"`      // 类型
	Priority  WorkloadPriority `json:"priority"`  // 优先级
	Status    WorkloadStatus   `json:"status"`    // 状态

	// 调度信息
	QueueName        string `json:"queue_name"`         // 队列名称
	ClusterQueueName string `json:"cluster_queue_name"` // 集群队列名称

	// 时间信息
	CreateAt time.Time  `json:"create_at"` // 创建时间
	StartAt  *time.Time `json:"start_at"`  // 开始时间
	EndAt    *time.Time `json:"end_at"`    // 结束时间

	// 用户信息
	Creator string `json:"creator"` // 创建人

	// 描述信息
	Description string            `json:"description"` // 描述
	Labels      map[string]string `json:"labels"`      // 标签
	Annotations map[string]string `json:"annotations"` // 注解

	// 资源信息
	// Resources *WorkloadResources `json:"resources"` // 资源信息

	// Kubernetes原始对象
	// RawObject *unstructured.Unstructured `json:"-"` // 原始K8s对象
}

// CreateWorkloadRequest 创建工作负载请求
type CreateWorkloadRequest struct {
	Name        string                    `json:"name" validate:"required,max=253"`
	Namespace   string                    `json:"namespace" validate:"required,max=63"`
	Project     string                    `json:"project" validate:"required,max=253"`
	Type        string                    `json:"type" validate:"required,oneof=PIPELINE TRAINING INFERENCE EVALUATION DATA_PROCESS CUSTOM"`
	Priority    int32                     `json:"priority" validate:"min=0,max=3"`
	QueueName   string                    `json:"queue_name,omitempty" validate:"max=253"`
	Description string                    `json:"description,omitempty" validate:"max=1000"`
	Labels      map[string]string         `json:"labels,omitempty"`
	Annotations map[string]string         `json:"annotations,omitempty"`
	Resources   *WorkloadResourcesRequest `json:"resources,omitempty"`
}

// WorkloadResourcesRequest 工作负载资源请求
type WorkloadResourcesRequest struct {
	Memory    *ResourceUsageRequest `json:"memory,omitempty"`
	CPU       *ResourceUsageRequest `json:"cpu,omitempty"`
	GpuMemory *ResourceUsageRequest `json:"gpu_memory,omitempty"`
	GpuCore   *ResourceUsageRequest `json:"gpu_core,omitempty"`
	PodCount  int32                 `json:"pod_count,omitempty" validate:"min=0"`
}

// ResourceUsageRequest 资源使用请求
type ResourceUsageRequest struct {
	Allocated int64 `json:"allocated" validate:"min=0"`
	Limit     int64 `json:"limit" validate:"min=0"`
}

// UpdateWorkloadRequest 更新工作负载请求
type UpdateWorkloadRequest struct {
	Description string            `json:"description,omitempty" validate:"max=1000"`
	Labels      map[string]string `json:"labels,omitempty"`
	Annotations map[string]string `json:"annotations,omitempty"`
	Priority    *int32            `json:"priority,omitempty" validate:"omitempty,min=0,max=3"`
	QueueName   *string           `json:"queue_name,omitempty" validate:"omitempty,max=253"`
}

// ListWorkloadRequest 列出工作负载请求
type ListWorkloadRequest struct {
	Page     int32 `json:"page" validate:"min=1"`
	PageSize int32 `json:"page_size" validate:"min=1,max=1000"`

	// 过滤条件
	Namespace string   `json:"namespace,omitempty"`
	Project   []string `json:"project,omitempty"`
	Status    []string `json:"status,omitempty"`
	Type      []string `json:"type,omitempty"`
	Priority  []string `json:"priority,omitempty"`
	Creator   string   `json:"creator,omitempty"`
	QueueName string   `json:"queue_name,omitempty"`

	// 时间范围
	CreateAtStart *int64 `json:"create_at_start,omitempty"`
	CreateAtEnd   *int64 `json:"create_at_end,omitempty"`

	// 搜索关键词
	SearchKeyword string `json:"search_keyword,omitempty"`

	// 排序参数
	SortBy    string `json:"sort_by,omitempty" validate:"omitempty,oneof=name create_time start_time priority status"`
	SortOrder string `json:"sort_order,omitempty" validate:"omitempty,oneof=asc desc"`
}

// WorkloadResponse 工作负载响应
type WorkloadResponse struct {
	ID          string                     `json:"id"`
	Name        string                     `json:"name"`
	Namespace   string                     `json:"namespace"`
	Project     string                     `json:"project"`
	Type        string                     `json:"type"`
	Priority    int32                      `json:"priority"`
	Status      string                     `json:"status"`
	RunNode     string                     `json:"run_node"`
	QueueName   string                     `json:"queue_name,omitempty"`
	CreateAt    time.Time                  `json:"create_at"`
	StartAt     *time.Time                 `json:"start_at,omitempty"`
	EndAt       *time.Time                 `json:"end_at,omitempty"`
	Duration    int64                      `json:"duration,omitempty"` // 运行时长(秒)
	Creator     string                     `json:"creator"`
	Description string                     `json:"description,omitempty"`
	Labels      map[string]string          `json:"labels,omitempty"`
	Annotations map[string]string          `json:"annotations,omitempty"`
	Resources   *WorkloadResourcesResponse `json:"resources,omitempty"`
}

// WorkloadResourcesResponse 工作负载资源响应
type WorkloadResourcesResponse struct {
	Memory    *ResourceUsageResponse `json:"memory,omitempty"`
	CPU       *ResourceUsageResponse `json:"cpu,omitempty"`
	GpuMemory *ResourceUsageResponse `json:"gpu_memory,omitempty"`
	GpuCore   *ResourceUsageResponse `json:"gpu_core,omitempty"`
	PodCount  int32                  `json:"pod_count,omitempty"`
}

// ResourceUsageResponse 资源使用响应
type ResourceUsageResponse struct {
	Used            int64   `json:"used"`
	Allocated       int64   `json:"allocated"`
	Limit           int64   `json:"limit"`
	UsagePercentage float64 `json:"usage_percentage"`
}

// ListWorkloadResponse 列出工作负载响应
type ListWorkloadResponse struct {
	Items      []*WorkloadResponse `json:"items"`
	Total      int64               `json:"total"`
	Page       int32               `json:"page"`
	PageSize   int32               `json:"page_size"`
	TotalPages int32               `json:"total_pages"`
}

// WorkloadStatusRequest 工作负载状态请求
type WorkloadStatusRequest struct {
	Status string `json:"status" validate:"required,oneof=Pending Admitted Running Succeeded Failed Cancelled Suspended"`
}

func (this *WorkloadService) ListWorkload(request *restful.Request, response *restful.Response) {
}
