package workload

import (
	restfulspec "github.com/emicklei/go-restful-openapi/v2"
	restful "github.com/emicklei/go-restful/v3"
	"transwarp.io/mlops/pipeline/api/http/service"
)

type WorkloadService struct {
	ServiceName string
	rootPath    string
}

func init() {
	s := &WorkloadService{
		ServiceName: "workload",
		rootPath:    service.V3Workload(),
	}
	service.RegisterService(s)
}

func (s *WorkloadService) Name() string {
	return s.ServiceName
}

func (r *WorkloadService) NewWebService() *restful.WebService {
	tags := []string{"Workload"}
	metaK, metaV := restfulspec.KeyOpenAPITags, tags
	ws := new(restful.WebService)
	ws.Path(r.rootPath).
		Consumes(restful.MIME_JSON, restful.MIME_OCTET).
		Produces(restful.MIME_JSON, restful.MIME_OCTET)

	apiTags := tags

	ws.Route(ws.GET("").
		To(r.ListWorkload).
		Doc("任务列表").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Metadata(metaK, metaV).
		Reads(ListWorkloadRequest{}).
		Returns(200, "OK", ListWorkloadResponse{}))

	return ws
}
