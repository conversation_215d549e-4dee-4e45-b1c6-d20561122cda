package provider

import (
	"sync"

	"transwarp.io/mlops/pipeline/api/grpc/pipeline"
	"transwarp.io/mlops/pipeline/api/grpc/service"
)

type ServiceProvider interface {
	GetService(name string) (interface{}, bool)
	MustGetService(name string) interface{}
}

type serviceProvider struct {
	manager *service.ServiceServerManager
}

var (
	defaultProvider ServiceProvider
	once            sync.Once
)

func GetServiceProvider() ServiceProvider {
	once.Do(func() {
		defaultProvider = &serviceProvider{
			manager: service.GetServiceManager(),
		}
	})
	return defaultProvider
}

func (p *serviceProvider) GetService(name string) (interface{}, bool) {
	return p.manager.GetServiceServer(name)
}

func (p *serviceProvider) MustGetService(name string) interface{} {
	svc, exists := p.GetService(name)
	if !exists {
		panic("service server not found: " + name)
	}
	return svc
}

func MustGetPipelineService() pipeline.PipelineServiceServer {
	return GetServiceProvider().MustGetService(pipeline.PipelineGRPC).(pipeline.PipelineServiceServer)
}

func MustGetRunService() pipeline.RunServiceServer {
	return GetServiceProvider().MustGetService(pipeline.RunGRPC).(pipeline.RunServiceServer)
}

func MustGetActuatorService() pipeline.ActuatorServiceServer {
	return GetServiceProvider().MustGetService(pipeline.ActuatorGRPC).(pipeline.ActuatorServiceServer)
}
