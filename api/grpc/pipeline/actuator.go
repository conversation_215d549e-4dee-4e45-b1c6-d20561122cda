package pipeline

import (
	"context"
	"sync"

	"google.golang.org/grpc"
	"transwarp.io/aip/llmops-common/pb/common"
	"transwarp.io/mlops/pipeline/api/grpc/service"
)

var (
	ActuatorGRPCName = "ActuatorServiceServer"

	ActuatorGRPC *ActuatorServiceServer
	actuatorOnce sync.Once
)

type ActuatorServiceServer struct {
	common.UnsafeActuatorServiceServer

	ServiceServerName string
}

func init() {
	actuatorOnce.Do(func() {
		ActuatorGRPC = &ActuatorServiceServer{
			ServiceServerName: ActuatorGRPCName,
		}
	})

	service.RegisterServiceServer(ActuatorGRPC)
}

func (a ActuatorServiceServer) Name() string {
	return a.ServiceServerName
}

func (a ActuatorServiceServer) RegisterServiceServer(s grpc.ServiceRegistrar) {
	common.RegisterActuatorServiceServer(s, a)
}

func (a ActuatorServiceServer) CheckHealth(ctx context.Context, req *common.CheckHealthReq) (*common.HealthRsp, error) {
	healthRsp := &common.HealthRsp{
		Status: "UP",
		Code:   "UP",
	}

	return healthRsp, nil
}
