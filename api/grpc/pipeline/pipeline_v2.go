package pipeline

import (
	"context"

	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	"transwarp.io/mlops/pipeline/internal/core"
)

func (p PipelineServiceServer) SavePipelineVersionV2(ctx context.Context, req *pb.SavePipelineVersionReqV2) (*pb.PipelineVersionId, error) {
	version, err := core.PipelineVersionMgr.GetPipelineVersion(ctx, req.PipelineVersionId)
	if err != nil {
		return nil, err
	}
	pv2 := version.ToPbV2()
	pv2.TaskFlow = req.SavePipelineVersionBody.TaskFlow
	pv2.Style = pb.PipelineVersionV2_SchedulingStyle(req.SavePipelineVersionBody.Style)
	pv2.TimingConfig = req.SavePipelineVersionBody.TimingConfig
	pv2.Desc = req.SavePipelineVersionBody.Desc
	pv2.Name = req.SavePipelineVersionBody.Name

	id, err := core.PipelineVersionMgr.UpdatePipelineVersion(ctx, version.FromPbV2(pv2))
	if err != nil {
		return nil, err
	}
	return &pb.PipelineVersionId{PipelineVersionId: id}, nil
}

func (p PipelineServiceServer) GetPipelineVersionV2(ctx context.Context, req *pb.GetPipelineVersionReq) (*pb.PipelineVersionV2, error) {
	pipeline, err := core.PipelineVersionMgr.GetPipelineVersion(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return pipeline.ToPbV2(), nil
}
