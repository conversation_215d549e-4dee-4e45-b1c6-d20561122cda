package pipeline

import (
	"context"
	"io"
	"sync"

	"google.golang.org/grpc"
	common "transwarp.io/aip/llmops-common/pb/common"
	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	"transwarp.io/mlops/mlops-std/stdlog"
	"transwarp.io/mlops/pipeline/api/grpc/service"
	core "transwarp.io/mlops/pipeline/internal/core/manager"
	"transwarp.io/mlops/pipeline/internal/core/model"
)

// r RunService transwarp.io/aip/llmops-common/pb/pipeline.RunServiceServer

var (
	RunGRPCName = "RunServiceServer"
	RunGRPC     *RunServiceServer
	runOnce     sync.Once
)

type RunServiceServer struct {
	pb.UnsafeRunServiceServer
	ServiceServerName string
}

func init() {
	runOnce.Do(func() {
		RunGRPC = &RunServiceServer{
			ServiceServerName: RunGRPCName,
		}
	})

	service.RegisterServiceServer(RunGRPC)
}

func (r RunServiceServer) Name() string {
	return r.ServiceServerName
}

func (r RunServiceServer) RegisterServiceServer(s grpc.ServiceRegistrar) {
	pb.RegisterRunServiceServer(s, r)
}

func (r RunServiceServer) GetRunStepArtifact(req *pb.GetRunStepArtifactReq, server pb.RunService_GetRunStepArtifactServer) error {
	reader, fileName, err := core.RunMgr.GetRunStepArtifact(server.Context(), req.RunId, req.NodeId, req.Artifact)
	if err != nil {
		return err
	}
	buf := make([]byte, 1024) // 以1KB为单位分块读取

	for {
		n, err := reader.Read(buf)
		if err == io.EOF {
			break // 文件读取完毕
		}
		if err != nil {
			return err // 处理读取错误
		}

		// 发送文件内容的一个块
		if err := server.Send(&pb.GetRunStepArtifactRsp{FileName: fileName, File: buf[:n]}); err != nil {
			return err
		}
	}
	return nil
}

func (r RunServiceServer) SubmitRun(ctx context.Context, req *pb.SubmitRunReq) (*pb.RunId, error) {
	run := &model.Run{
		ProjectId:  req.ProjectId,
		TaskSource: req.SubmitRunBody.TaskSource,
		TaskFlow:   (&model.TaskFlow{}).FromPb(req.SubmitRunBody.TaskFlow),
	}

	runId, err := core.RunMgr.SubmitRun(ctx, run)
	if err != nil {
		return nil, err
	}
	return &pb.RunId{
		RunId: runId,
	}, nil
}

func (r RunServiceServer) RetryRun(ctx context.Context, req *pb.RetryRunReq) (*common.EmptyRsp, error) {
	err := core.RunMgr.RetryRun(ctx, req.RunId)
	return &common.EmptyRsp{}, err
}

func (r RunServiceServer) TerminateRun(ctx context.Context, req *pb.TerminateRunReq) (*common.EmptyRsp, error) {
	err := core.RunMgr.TerminateRun(ctx, req.RunId)
	return &common.EmptyRsp{}, err
}

func (r RunServiceServer) GetRun(ctx context.Context, req *pb.GetRunReq) (*pb.Run, error) {
	run, err := core.RunMgr.GetRun(ctx, req.RunId)
	if err != nil {
		return nil, err
	}
	return run.ToPb(), err
}

func (r RunServiceServer) GetRunStepLogs(req *pb.GetRunStepLogsReq, server pb.RunService_GetRunStepLogsServer) error {
	reader, err := core.RunMgr.GetRunStepLogs(server.Context(), req.RunId, req.NodeId)
	if err != nil {
		stdlog.Errorf("GetRunStepLogs grpc, err is %s", err.Error())
		return nil
	}
	buf := make([]byte, 1024) // 以1KB为单位分块读取

	for {
		n, err := reader.Read(buf)
		if err == io.EOF {
			if err := server.Send(&pb.GetRunStepLogsRsp{Log: buf[:n]}); err != nil {
				return err
			}
			break // 文件读取完毕
		}
		if err != nil {
			return err // 处理读取错误
		}

		// 发送文件内容的一个块
		if err := server.Send(&pb.GetRunStepLogsRsp{Log: buf[:n]}); err != nil {
			return err
		}
	}
	return nil
}

func (r RunServiceServer) GetRunStepPodInfo(ctx context.Context, req *pb.GetRunStepPodInfoReq) (*pb.GetRunStepPodInfoRsp, error) {
	podInfo, err := core.RunMgr.GetRunStepPodInfo(ctx, req.RunId, req.NodeId)
	if err != nil {
		return nil, err
	}
	return &pb.GetRunStepPodInfoRsp{
		Yaml: podInfo,
	}, err
}

func (r RunServiceServer) GetRunStepEvents(ctx context.Context, req *pb.GetRunStepEventsReq) (*pb.GetRunStepEventsRsp, error) {
	podEvents, err := core.RunMgr.GetRunStepEvents(ctx, req.RunId, req.NodeId, req.ProjectId, req.TenantId)
	if err != nil {
		return nil, err
	}
	return &pb.GetRunStepEventsRsp{
		Event: podEvents,
	}, err
}

func (r RunServiceServer) DeleteRun(ctx context.Context, req *pb.DeleteRunReq) (*pb.DeleteRunRsq, error) {
	err := core.RunMgr.DeleteRun(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return &pb.DeleteRunRsq{}, err
}

func (r RunServiceServer) DeleteRunBatch(ctx context.Context, req *pb.DeleteRunBatchReq) (*pb.DeleteRunBatchRsp, error) {
	deleteIds := make([]string, 0)
	for _, id := range req.Ids {
		err := core.RunMgr.DeleteRun(ctx, id)
		if err != nil {
			stdlog.Error(err)
		} else {
			deleteIds = append(deleteIds, id)
		}
	}
	return &pb.DeleteRunBatchRsp{
		Ids: deleteIds,
	}, nil
}

func (r RunServiceServer) ListRuns(ctx context.Context, req *pb.ListRunsReq) (*pb.RunsPage, error) {
	runList, size, err := core.RunMgr.ListRun(ctx, req.ProjectId, req.Filter, req.PageReq)
	if err != nil {
		return nil, err
	}
	runs := make([]*pb.Run, 0)

	for _, run := range runList {
		runs = append(runs, run.ToPb())
	}
	return &pb.RunsPage{
		Runs: runs,
		Size: size,
	}, nil
}

func (r RunServiceServer) GetEventsByLabels(ctx context.Context, req *pb.LabelEventsReq) (*pb.GetRunStepEventsRsp, error) {
	events, err := core.RunMgr.GetEventsByLabels(ctx, req.Labels)
	if err != nil {
		return nil, err
	}
	return &pb.GetRunStepEventsRsp{Event: events}, nil
}
