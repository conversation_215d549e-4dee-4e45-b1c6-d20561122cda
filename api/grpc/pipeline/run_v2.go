package pipeline

import (
	"context"

	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	core "transwarp.io/mlops/pipeline/internal/core/manager"
	"transwarp.io/mlops/pipeline/internal/core/model"
)

// r RunService transwarp.io/aip/llmops-common/pb/pipeline.RunServiceServer

func (r RunServiceServer) SubmitRunV2(ctx context.Context, req *pb.SubmitRunReqV2) (*pb.RunId, error) {
	run := &model.Run{
		ProjectId:  req.GetProjectId(),
		TaskSource: req.GetSubmitRunBody().GetTaskSource(),
		TaskFlow:   (&model.TaskFlow{}).FromPbV2(req.SubmitRunBody.TaskFlow),
	}

	runId, err := core.RunMgr.SubmitRun(ctx, run)
	if err != nil {
		return nil, err
	}
	return &pb.RunId{RunId: runId}, nil
}

func (r RunServiceServer) GetRunV2(ctx context.Context, req *pb.GetRunReq) (*pb.RunV2, error) {
	run, err := core.RunMgr.GetRun(ctx, req.RunId)
	if err != nil {
		return nil, err
	}
	return run.ToPbV2(), err
}

func (r RunServiceServer) ListRunsV2(ctx context.Context, req *pb.ListRunsReq) (*pb.RunsPageV2, error) {
	runList, size, err := core.RunMgr.ListRun(ctx, req.ProjectId, req.Filter, req.PageReq)
	if err != nil {
		return nil, err
	}
	runs := make([]*pb.RunV2, 0, len(runList))
	for _, run := range runList {
		runs = append(runs, run.ToPbV2())
	}
	return &pb.RunsPageV2{
		Runs: runs,
		Size: size,
	}, nil
}
