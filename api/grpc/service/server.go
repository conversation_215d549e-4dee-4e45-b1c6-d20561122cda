package service

import (
	"sync"

	"google.golang.org/grpc"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

type ServiceServer interface {
	Name() string
	RegisterServiceServer(s grpc.ServiceRegistrar)
}

type ServiceServerManager struct {
	services map[string]ServiceServer
	mu       sync.RWMutex
}

func NewServiceServerManager() *ServiceServerManager {
	return &ServiceServerManager{
		services: make(map[string]ServiceServer),
	}
}

var (
	DefaultServiceServerManager *ServiceServerManager
	once                        sync.Once
)

func GetServiceManager() *ServiceServerManager {
	once.Do(func() {
		DefaultServiceServerManager = NewServiceServerManager()
	})
	return DefaultServiceServerManager
}

func (m *ServiceServerManager) Register(s ServiceServer) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	name := s.Name()
	if _, exists := m.services[name]; exists {
		zlog.SugarWarnf("Service server %s already registered, skipping", name)
		return nil
	}

	m.services[name] = s
	zlog.SugarInfof("Service server %s registered successfully", name)
	return nil
}

func RegisterServiceServer(s ServiceServer) {
	GetServiceManager().Register(s)
}

func InitServiceServer(s grpc.ServiceRegistrar) {
	GetServiceManager().init(s)
}

func (m *ServiceServerManager) init(s grpc.ServiceRegistrar) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	for name, ss := range m.services {
		zlog.SugarInfof("Initializing service server: %s", name)
		ss.RegisterServiceServer(s)
	}
}

func (m *ServiceServerManager) GetServiceServer(name string) (ServiceServer, bool) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	service, exists := m.services[name]
	return service, exists
}

func (m *ServiceServerManager) GetAllServiceServers() []ServiceServer {
	m.mu.RLock()
	defer m.mu.RUnlock()

	services := make([]ServiceServer, 0, len(m.services))
	for _, service := range m.services {
		services = append(services, service)
	}
	return services
}
