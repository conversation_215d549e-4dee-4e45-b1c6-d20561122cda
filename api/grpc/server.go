package grpc

import (
	"context"
	"net"
	"strconv"

	"google.golang.org/grpc"
	"transwarp.io/mlops/mlops-std/stderr"
	_ "transwarp.io/mlops/pipeline/api/grpc/pipeline"
	"transwarp.io/mlops/pipeline/api/grpc/service"
	zlog "transwarp.io/mlops/pipeline/pkg/util/log"
)

const MAX_MESSAGE_LENGTH = 256 * 1024 * 1024

type GRPCServer struct {
	opts *Option
}

func NewGRPCServer(opts *Option) *GRPCServer {
	opts = completedOptions(opts)
	return &GRPCServer{
		opts: opts,
	}
}

func filter(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
	zlog.SugarInfof("grpc method: %s, params: %v", info.FullMethod, req)

	defer func() error {
		if r := recover(); r != nil {
			err = stderr.Internal.Error("panic: %v", r)
			zlog.SugarErrorf(err.(*stderr.StdErr).Msg)
			zlog.SugarErrorf(err.(*stderr.StdErr).StackMsg)

		}
		return err
	}()
	return handler(ctx, req)

}

func (server *GRPCServer) Start(ctx context.Context) chan error {
	listen, e := net.Listen("tcp", ":"+strconv.Itoa(server.opts.Port))
	if e != nil {
		zlog.SugarErrorf("failed to start grpc server", e)
	}

	serverOptions := []grpc.ServerOption{
		grpc.UnaryInterceptor(filter), grpc.MaxSendMsgSize(MAX_MESSAGE_LENGTH), grpc.MaxRecvMsgSize(MAX_MESSAGE_LENGTH),
	}
	s := grpc.NewServer(serverOptions...)

	service.InitServiceServer(s)

	err := make(chan error)
	go func() {
		zlog.SugarInfof("Starting GRPC server on port %d", server.opts.Port)
		if e := s.Serve(listen); e != nil {
			zlog.SugarFatalf("GRPC server Serve: %v", e)
			err <- e
		}
	}()

	return err
}

type Option struct {
	Port int
}

func completedOptions(opts *Option) *Option {
	if opts == nil {
		opts = &Option{}
	}
	if opts.Port == 0 {
		opts.Port = 50051
	}

	return opts
}
