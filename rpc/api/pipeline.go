package api

import (
	"context"

	common "transwarp.io/aip/llmops-common/pb/common"
	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	"transwarp.io/mlops/pipeline/core"
	"transwarp.io/mlops/pipeline/core/model"
)

// p PipelineService transwarp.io/aip/llmops-common/pb/pipeline.PipelineServiceServer

type PipelineService struct {
	pb.UnsafePipelineServiceServer
}

func (p PipelineService) DeletePipelines(ctx context.Context, req *pb.DeletePipelinesReq) (*common.DeleteRsp, error) {
	err := core.PipelineMgr.DeletePipelines(ctx, req.Ids)
	return &common.DeleteRsp{}, err
}

func (p PipelineService) DeletePipelineVersions(ctx context.Context, req *pb.DeletePipelineVersionsReq) (*common.DeleteRsp, error) {
	err := core.PipelineVersionMgr.DeletePipelineVersions(ctx, req.Ids)
	return &common.DeleteRsp{}, err
}

func (p PipelineService) CheckPipelineNameUnique(ctx context.Context, req *pb.CheckPipelineReq) (*pb.CheckPipelineRsp, error) {
	err := core.PipelineMgr.CheckPipelineNameUnique(ctx, req.Body.Name, req.ProjectId)
	return &pb.CheckPipelineRsp{}, err
}

func (p PipelineService) CheckPipelineVersionNameUnique(ctx context.Context, req *pb.CheckPipelineVersionReq) (*pb.CheckPipelineVersionRsp, error) {
	err := core.PipelineVersionMgr.CheckPipelineVersionNameUnique(ctx, req.Body.Name, req.Body.PipelineId)
	return &pb.CheckPipelineVersionRsp{}, err
}

func (p PipelineService) CreatePipeline(ctx context.Context, req *pb.CreatePipelineReq) (*pb.PipelineId, error) {
	pipeline := &pb.Pipeline{
		Name:      req.CreatePipelineBody.Name,
		Desc:      req.CreatePipelineBody.Desc,
		ProjectId: req.ProjectId,
	}
	id, err := core.PipelineMgr.CreatePipeline(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	return &pb.PipelineId{
		PipelineId: id,
	}, nil
}

func (p PipelineService) ModifyPipeline(ctx context.Context, req *pb.ModifyPipelineReq) (*pb.PipelineId, error) {
	pipeline := &pb.Pipeline{
		Id:   req.Id,
		Name: req.ModifyPipelineBody.Name,
		Desc: req.ModifyPipelineBody.Desc,
	}
	id, err := core.PipelineMgr.ModifyPipeline(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	return &pb.PipelineId{
		PipelineId: id,
	}, nil
}

func (p PipelineService) CreatePipelineVersion(ctx context.Context, req *pb.CreatePipelineVersionReq) (*pb.PipelineVersionId, error) {
	pipelineVersion := &model.PipelineVersion{
		Name:       req.CreatePipelineVersionBody.Name,
		Desc:       req.CreatePipelineVersionBody.Desc,
		PipelineId: req.CreatePipelineVersionBody.PipelineId,
	}
	id, err := core.PipelineVersionMgr.CreatePipelineVersion(ctx, pipelineVersion)
	if err != nil {
		return nil, err
	}
	return &pb.PipelineVersionId{
		PipelineVersionId: id,
	}, nil
}

func (p PipelineService) SavePipelineVersion(ctx context.Context, req *pb.SavePipelineVersionReq) (*pb.PipelineVersionId, error) {
	version, err := core.PipelineVersionMgr.GetPipelineVersion(ctx, req.PipelineVersionId)
	if err != nil {
		return nil, err
	}
	pipelineVersion := version.ToPb()
	pipelineVersion.TaskFlow = req.SavePipelineVersionBody.TaskFlow
	pipelineVersion.Style = req.SavePipelineVersionBody.Style
	pipelineVersion.TimingConfig = req.SavePipelineVersionBody.TimingConfig
	pipelineVersion.Desc = req.SavePipelineVersionBody.Desc
	pipelineVersion.Name = req.SavePipelineVersionBody.Name

	id, err := core.PipelineVersionMgr.UpdatePipelineVersion(ctx, version.FromPb(pipelineVersion))
	if err != nil {
		return nil, err
	}
	return &pb.PipelineVersionId{
		PipelineVersionId: id,
	}, nil
}

func (p PipelineService) GetPipeline(ctx context.Context, req *pb.GetPipelineReq) (*pb.Pipeline, error) {
	pipeline, err := core.PipelineMgr.GetPipeline(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	pipelinePb := pipeline.ToPb()
	pipelinePb.VersionCount, err = core.PipelineMgr.CountVersionInPipeline(ctx, pipeline.Id)
	if err != nil {
		return nil, err
	}
	return pipeline.ToPb(), nil
}

func (p PipelineService) GetPipelineVersion(ctx context.Context, req *pb.GetPipelineVersionReq) (*pb.PipelineVersion, error) {
	pipeline, err := core.PipelineVersionMgr.GetPipelineVersion(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return pipeline.ToPb(), nil
}

func (p PipelineService) DeletePipeline(ctx context.Context, req *pb.DeletePipelineReq) (*common.DeleteRsp, error) {
	err := core.PipelineMgr.DeletePipeline(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return &common.DeleteRsp{}, nil
}

func (p PipelineService) DeletePipelineVersion(ctx context.Context, req *pb.DeletePipelineVersionReq) (*common.DeleteRsp, error) {
	err := core.PipelineVersionMgr.DeletePipelineVersion(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return &common.DeleteRsp{}, nil
}

func (p PipelineService) ListPipelines(ctx context.Context, req *pb.ListPipelinesReq) (*pb.PipelinePage, error) {
	pipelines, err := core.PipelineMgr.ListPipelines(ctx, req.ProjectId)
	if err != nil {
		return nil, err
	}
	versionCountMap, err := core.PipelineMgr.CountVersionInPipelines(ctx)
	if err != nil {
		return nil, err
	}
	for _, pipeline := range pipelines {
		pipeline.VersionCount = versionCountMap[pipeline.Id]
	}
	pipelinePage := &pb.PipelinePage{
		Pipelines: pipelines,
		Size:      int32(len(pipelines)),
	}
	return pipelinePage, nil
}

func (p PipelineService) ListPipelineVersions(ctx context.Context, req *pb.ListPipelineVersionsReq) (*pb.PipelineVersionPage, error) {
	versions, err := core.PipelineVersionMgr.ListPipelineVersions(ctx, req.PipelineId)
	if err != nil {
		return nil, err
	}
	pipelinePage := &pb.PipelineVersionPage{
		PipelineVersions: versions,
		Size:             int32(len(versions)),
	}
	return pipelinePage, nil
}

func (p PipelineService) StartPipelineVersion(ctx context.Context, req *pb.StartPipelineVersionReq) (*pb.PipelineVersionId, error) {
	id, err := core.PipelineVersionMgr.StartPipelineVersion(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return &pb.PipelineVersionId{
		PipelineVersionId: id,
	}, nil
}

func (p PipelineService) StopPipelineVersion(ctx context.Context, req *pb.StopPipelineVersionReq) (*pb.PipelineVersionId, error) {
	err := core.PipelineVersionMgr.StopPipelineVersion(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return &pb.PipelineVersionId{
		PipelineVersionId: req.Id,
	}, nil
}

func (p PipelineService) OncePipelineVersion(ctx context.Context, req *pb.OncePipelineVersionReq) (*pb.OncePipelineVersionRsp, error) {
	id, err := core.PipelineVersionMgr.OncePipelineVersion(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return &pb.OncePipelineVersionRsp{
		RunId: id,
	}, nil
}

func (p PipelineService) CreatePipelineVersionByYaml(ctx context.Context, req *pb.CreatePipelineVersionByYamlReq) (*pb.PipelineVersionId, error) {
	id, err := core.PipelineVersionMgr.CreatePipelineVersionByYaml(ctx, req)
	if err != nil {
		return nil, err
	}
	return &pb.PipelineVersionId{
		PipelineVersionId: id,
	}, nil
}

func (p PipelineService) ExportPipelineVersion(ctx context.Context, req *pb.ExportPipelineVersionReq) (*pb.ExportPipelineVersionRsp, error) {
	return core.PipelineVersionMgr.ExportPipelineVersionYaml(ctx, req.Id)
}

func (p PipelineService) GetComponents(ctx context.Context, req *pb.GetComponentsReq) (*pb.ComponentPage, error) {
	components, err := core.ComponentMgr.GetComponents(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.ComponentPage{
		Components: components,
		Size:       int32(len(components)),
	}, nil
}
