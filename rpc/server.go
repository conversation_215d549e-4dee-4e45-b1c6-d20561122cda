package rpc

import (
	"context"
	"google.golang.org/grpc"
	"net"
	"strconv"
	"transwarp.io/aip/llmops-common/pb/common"
	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	"transwarp.io/mlops/mlops-std/conf"
	"transwarp.io/mlops/mlops-std/stderr"
	logger "transwarp.io/mlops/mlops-std/stdlog"
	"transwarp.io/mlops/pipeline/rpc/api"
)

const MAX_MESSAGE_LENGTH = 256 * 1024 * 1024

func filter(
	ctx context.Context, req interface{},
	info *grpc.UnaryServerInfo,
	handler grpc.UnaryHandler,
) (resp interface{}, err error) {
	logger.Infof("grpc method: %s, params: %v", info.FullMethod, req)

	defer func() error {
		if r := recover(); r != nil {
			err = stderr.Internal.Error("panic: %v", r)
			logger.Error(err.(*stderr.StdErr).Msg)
			logger.Error(err.(*stderr.StdErr).StackMsg)

		}
		return err
	}()
	return handler(ctx, req)

}

func StartGrpcServer(conf *conf.Config) {
	listen, err := net.Listen("tcp", ":"+strconv.Itoa(conf.MLOPS.GRPCServer.Port))
	if err != nil {
		logger.Error("failed to start grpc server", err)
	}

	serverOptions := []grpc.ServerOption{
		grpc.UnaryInterceptor(filter), grpc.MaxSendMsgSize(MAX_MESSAGE_LENGTH), grpc.MaxRecvMsgSize(MAX_MESSAGE_LENGTH),
	}
	s := grpc.NewServer(serverOptions...)

	common.RegisterActuatorServiceServer(s, api.ActuatorServiceRPC)
	pb.RegisterPipelineServiceServer(s, api.PipelineServiceRPC)
	pb.RegisterRunServiceServer(s, api.RunServiceRPC)

	s.Serve(listen)
}
