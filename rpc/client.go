package rpc

import (
	"google.golang.org/grpc"
	"strconv"
	"transwarp.io/aip/llmops-common/pb/common"
	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	"transwarp.io/mlops/mlops-std/conf"
	logger "transwarp.io/mlops/mlops-std/stdlog"
)

var (
	ActuatorClient common.ActuatorServiceClient
	PipelineClient pb.PipelineServiceClient
	RunClient      pb.RunServiceClient
)

func StartGRPCClient(conf *conf.Config) error {
	conn, err := grpc.Dial(":"+strconv.Itoa(conf.MLOPS.GRPCServer.Port), grpc.WithInsecure(),
		grpc.WithDefaultCallOptions(grpc.MaxCallRecvMsgSize(MAX_MESSAGE_LENGTH)))
	if err != nil {
		logger.Error("did not connect :", err)
		return err
	}

	ActuatorClient = common.NewActuatorServiceClient(conn)
	PipelineClient = pb.NewPipelineServiceClient(conn)
	RunClient = pb.NewRunServiceClient(conn)

	return nil
}
