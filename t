[1mdiff --git a/cmd/app/server.go b/cmd/app/server.go[m
[1mindex fc4ba2d..108fa37 100644[m
[1m--- a/cmd/app/server.go[m
[1m+++ b/cmd/app/server.go[m
[36m@@ -13,7 +13,6 @@[m [mimport ([m
 	"transwarp.io/mlops/pipeline/cmd/app/options"[m
 	"transwarp.io/mlops/pipeline/conf"[m
 	core "transwarp.io/mlops/pipeline/internal/core/manager"[m
[31m-	dao "transwarp.io/mlops/pipeline/internal/dao"[m
 	zlog "transwarp.io/mlops/pipeline/pkg/util/log"[m
 [m
 	"transwarp.io/mlops/pipeline/internal/core/manager/task/handlerframework"[m
[36m@@ -55,7 +54,7 @@[m [mfunc Run(ctx context.Context, opts options.CompletedOptions) error {[m
 		"The server will load its initial configuration from this file. "+[m
 			"Omit this flag to use the default configuration values. ")[m
 [m
[31m-	dao.Init()[m
[32m+[m	[32m// dao.Init()[m
 	core.Init()[m
 [m
 	// setup task handler[m
[1mdiff --git a/internal/core/manager/task/cache/task_snapshot.go b/internal/core/manager/task/cache/task_snapshot.go[m
[1mindex fbc1eaa..470d89d 100644[m
[1m--- a/internal/core/manager/task/cache/task_snapshot.go[m
[1m+++ b/internal/core/manager/task/cache/task_snapshot.go[m
[36m@@ -97,6 +97,7 @@[m [mfunc (ts *TaskSnapshot) Start(ctx context.Context) error {[m
 func (ts *TaskSnapshot) GetAllTasks() ([]*modeltask.Task, error) {[m
 	tasks := make([]*modeltask.Task, 0)[m
 	for _, task := range ts.tasks {[m
[32m+[m		[32mzlog.SugarInfof("To task: %s", task.root.refs[0].GetName())[m
 		tasks = append(tasks, task.ToTask())[m
 	}[m
 	return tasks, nil[m
[1mdiff --git a/internal/core/manager/task/cache/task_tree.go b/internal/core/manager/task/cache/task_tree.go[m
[1mindex f780d76..518a084 100644[m
[1m--- a/internal/core/manager/task/cache/task_tree.go[m
[1m+++ b/internal/core/manager/task/cache/task_tree.go[m
[36m@@ -39,8 +39,7 @@[m [mfunc (tt *TaskTree) ToTask() *modeltask.Task {[m
 		ProjectName: ref.GetAnnotations()[consts.AnnotationTaskProjectNameKey],[m
 		Priority:    modeltask.ToTaskPriority(kwl.Spec.Priority),[m
 [m
[31m-		QueueName:        string(kwl.Spec.QueueName),[m
[31m-		ClusterQueueName: string(kwl.Status.Admission.ClusterQueue),[m
[32m+[m		[32mQueueName: string(kwl.Spec.QueueName),[m
 [m
 		Resources: nil,[m
 		// TODO[m
[36m@@ -50,6 +49,10 @@[m [mfunc (tt *TaskTree) ToTask() *modeltask.Task {[m
 [m
 		Namespace: kwl.Namespace,[m
 	}[m
[32m+[m
[32m+[m	[32mif kwl.Status.Admission != nil {[m
[32m+[m		[32mtask.ClusterQueueName = string(kwl.Status.Admission.ClusterQueue)[m
[32m+[m	[32m}[m
 	// update task status[m
 	task.UpdateTaskStatus().UpdateTaskStartedAt().UpdateTaskEndedAt()[m
 [m
