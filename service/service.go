package service

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	restful "github.com/emicklei/go-restful/v3"

	"transwarp.io/mlops/mlops-std/stdlog"
	token "transwarp.io/mlops/mlops-std/token"
	"transwarp.io/mlops/pipeline/service/api/pipeline"
	"transwarp.io/mlops/pipeline/service/api/swagger"
)

const (
	API_ROOT = "/api/v1"
	PIPELINE = API_ROOT + "/pipeline"
	RUN      = API_ROOT + "/run"
	ACTUATOR = "/actuator"
)

const (
	v2_API_ROOT = "/api/v2"
	v2_PIPELINE = v2_API_ROOT + "/pipeline"
	v2_RUN      = v2_API_ROOT + "/run"
)

const (
	v3_API_ROOT = "/api/v3"
	v3_RUN      = v3_API_ROOT + "/run"
)

func MountCompletedAPIModule() {
	restful.Add(pipeline.NewPipelineApi(PIPELINE))
	restful.Add(pipeline.NewRunApi(RUN))
	restful.Add(pipeline.NewActuatorApi(ACTUATOR))
	restful.Add(swagger.NewSwaggerAPI("/apidocs"))

	// V2
	restful.Add(pipeline.NewPipelineApiV2(v2_PIPELINE))
	restful.Add(pipeline.NewRunApiV2(v2_RUN))

	for _, ws := range restful.RegisteredWebServices() {
		ws.Filter(NCSACommonLogFormatLogger).Filter(Authenticate)
	}
}

// if not token supplied, return 401 (only check api path startwith "/api")
func Authenticate(req *restful.Request, resp *restful.Response, chain *restful.FilterChain) {
	t := req.Request.Header.Get(token.AUTH_HEADER)
	path := req.Request.URL.Path
	if strings.HasPrefix(path, API_ROOT) && "" == t {
		stdlog.Error(fmt.Sprintf("no token supplied when request path: %s", path))
		resp.WriteErrorString(401, "401: Not Authorized")
		return
	}
	chain.ProcessFilter(req, resp)
}

var NCSACommonLogFormatLogger = func(req *restful.Request, resp *restful.Response, chain *restful.FilterChain) {
	username := "-"
	if req.Request.URL.User != nil {
		if name := req.Request.URL.User.Username(); name != "" {
			username = name
		}
	}

	now := time.Now()
	// TODO, 适配tdc的ingress跳转
	spaBasePath := req.HeaderParameter("/spa")
	if spaBasePath != "" {
		cookie := http.Cookie{
			Name:  "basename",
			Value: spaBasePath,
			Path:  "/",
		}
		resp.AddHeader("Set-Cookie", cookie.String())
	}
	chain.ProcessFilter(req, resp)
	if req.Request.URL.RequestURI() == "/actuator/health" && resp.StatusCode() == 200 {
		return
	}
	stdlog.Infof("%s %s %s %s %s %d %d %v",
		strings.Split(req.Request.RemoteAddr, ":")[0],
		username,
		req.Request.Method,
		req.Request.URL.RequestURI(),
		req.Request.Proto,
		resp.StatusCode(),
		resp.ContentLength(),
		time.Now().Sub(now),
	)
}
