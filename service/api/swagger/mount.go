package swagger

import (
	restfulspec "github.com/emicklei/go-restful-openapi/v2"
	restful "github.com/emicklei/go-restful/v3"
	"github.com/go-openapi/spec"
	"net/http"
	"path"
	config "transwarp.io/mlops/pipeline/conf"
)

var SwaggerUIRoot = "public/swagger-ui"

func NewSwaggerAPI(root string) *restful.WebService {
	api := new(restful.WebService)
	api.Path(root).Consumes(restful.MIME_JSON).Produces(restful.MIME_JSON)
	api.Route(api.GET("/swagger.json").To(GetSwagger))
	api.Route(api.GET("/").To(GetSwaggerUI))
	api.Route(api.GET("/{subpath:*}").To(GetSwaggerUIResources))
	return api
}

func GetSwagger(request *restful.Request, response *restful.Response) {
	config := restfulspec.Config{
		WebServices:                   restful.RegisteredWebServices(),
		PostBuildSwaggerObjectHandler: enrichSwaggerObject,
	}
	swagger := restfulspec.BuildSwagger(config)
	for index, d := range swagger.Definitions {
		i := 0
		for _, r := range d.SchemaProps.Required {
			if r != "unknownFields" && r != "sizeCache" && r != "state" { // 满足条件的元素向前移动
				swagger.Definitions[index].Required[i] = r
				i++
			}
		}
		definition := swagger.Definitions[index]
		definition.Required = swagger.Definitions[index].Required[:i]
		swagger.Definitions[index] = swagger.Definitions[index]

		for k, _ := range d.SchemaProps.Properties {
			if k == "unknownFields" || k == "sizeCache" || k == "state" { // 满足条件的元素向前移动
				delete(d.SchemaProps.Properties, k)
			}
		}
	}
	response.WriteEntity(swagger)
}

func GetSwaggerUI(request *restful.Request, response *restful.Response) {
	http.ServeFile(
		response.ResponseWriter,
		request.Request,
		SwaggerUIRoot+"/index.html")
}

func GetSwaggerUIResources(req *restful.Request, resp *restful.Response) {
	actual := path.Join(SwaggerUIRoot, req.PathParameter("subpath"))
	http.ServeFile(
		resp.ResponseWriter,
		req.Request, actual)
}

func enrichSwaggerObject(swo *spec.Swagger) {
	swo.Schemes = []string{"https"}
	basePrefix := config.PipeineConfig.MLOPS.HttpServer.ServicePrefix
	if len(basePrefix) == 0 || basePrefix == "" {
		basePrefix = "/gateway/pipeline"
	}
	swo.BasePath = basePrefix

	swo.Info = &spec.Info{
		InfoProps: spec.InfoProps{
			Title:       "Mlops-Pipeline API",
			Description: "API Document For Service(Mlops-Pipeline) of Sophon",

			Contact: &spec.ContactInfo{
				ContactInfoProps: spec.ContactInfoProps{
					Name:  "Sophon MLOps-Pipeline of Tranwarp backend document",
					Email: "<EMAIL>",
					URL:   "https://www.transwarp.cn",
				},
			},
			License: &spec.License{
				LicenseProps: spec.LicenseProps{
					Name: "Private Licence",
					URL:  "https://www.transwarp.cn",
				},
			},
			Version: "dev",
		},
	}

	swo.SecurityDefinitions = spec.SecurityDefinitions{
		"Authorization": spec.APIKeyAuth("Authorization", "header"),
		//TODO 修改提供登录和鉴权的地址
		"oauth2": spec.OAuth2AccessToken("https://tw-node128:8393/cas/login", "https://tw-node128:8393/cas/login"),
	}

}
