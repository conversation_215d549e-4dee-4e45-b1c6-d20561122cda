package workload

import (
	"github.com/emicklei/go-restful/v3"

	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	"transwarp.io/mlops/mlops-std/util"
	"transwarp.io/mlops/pipeline/rpc/api"
	"transwarp.io/mlops/pipeline/service/api/helper"
)



func (wl *WorkloadService) ListWorkload(request *restful.Request, response *restful.Response) {
	body := &pb.SubmitRunBody{}
	request.ReadEntity(body)
	req := &pb.SubmitRunReq{
		ProjectId:     request.QueryParameter("project_id"),
		SubmitRunBody: body,
	}
	res, err := api.RunServiceRPC.SubmitRun(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}
