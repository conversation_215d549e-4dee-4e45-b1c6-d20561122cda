package workload

import (
	"time"

	"github.com/emicklei/go-restful/v3"
	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	"transwarp.io/mlops/mlops-std/util"
	"transwarp.io/mlops/pipeline/rpc/api"
	"transwarp.io/mlops/pipeline/service/api/helper"
)

// WorkloadType 工作负载类型枚举
type WorkloadType string

const (
	WorkloadTypePipeline    WorkloadType = "PIPELINE"     // 流水线
	WorkloadTypeTraining    WorkloadType = "TRAINING"     // 模型训练
	WorkloadTypeInference   WorkloadType = "INFERENCE"    // 模型推理
	WorkloadTypeEvaluation  WorkloadType = "EVALUATION"   // 模型评估
	WorkloadTypeDataProcess WorkloadType = "DATA_PROCESS" // 数据处理
)

// WorkloadPriority 工作负载优先级枚举
type WorkloadPriority int32

const (
	PriorityLow    WorkloadPriority = 0 // 低优先级
	PriorityNormal WorkloadPriority = 1 // 普通优先级
	PriorityHigh   WorkloadPriority = 2 // 高优先级
	PriorityUrgent WorkloadPriority = 3 // 紧急优先级
)

// WorkloadStatus 工作负载状态枚举
type WorkloadStatus int32

const (
	StatusPending   WorkloadStatus = 0 // 等待中
	StatusRunning   WorkloadStatus = 1 // 运行中
	StatusSucceeded WorkloadStatus = 2 // 成功
	StatusFailed    WorkloadStatus = 3 // 失败
	StatusCancelled WorkloadStatus = 4 // 已取消
)

// ResourceUsage 资源使用情况
type ResourceUsage struct {
	Used      int64 `json:"used"`      // 已使用
	Allocated int64 `json:"allocated"` // 已分配
	Limit     int64 `json:"limit"`     // 限额
}

// WorkloadResources 工作负载资源信息
type WorkloadResources struct {
	Memory    *ResourceUsage `json:"memory"`     // 内存(MB)
	CPU       *ResourceUsage `json:"cpu"`        // CPU(毫核)
	GpuMemory *ResourceUsage `json:"gpu_memory"` // 显存(MB)
	GpuCore   *ResourceUsage `json:"gpu_core"`   // 算力(核数)
	PodCount  int32          `json:"pod_count"`  // Pod个数
}

// Workload 工作负载数据结构
// 包含任务名称、所属空间、类型、优先级、运行节点、开始时间、创建人、
// 创建时间、描述、内存（占用/分配/限额）、CPU（占用/分配）、显存（占用/分配）、算力（占用/分配）、Pod（个）
type Workload struct {
	ID          string             `json:"id"`          // 工作负载ID
	Name        string             `json:"name"`        // 任务名称
	Project     string             `json:"project"`     // 所属空间/项目
	Type        WorkloadType       `json:"type"`        // 类型
	Priority    WorkloadPriority   `json:"priority"`    // 优先级
	Status      WorkloadStatus     `json:"status"`      // 状态
	RunNode     string             `json:"run_node"`    // 运行节点
	StartTime   int64              `json:"start_time"`  // 开始时间(Unix时间戳)
	CreateTime  int64              `json:"create_time"` // 创建时间(Unix时间戳)
	CreateUser  string             `json:"create_user"` // 创建人
	Description string             `json:"description"` // 描述
	Resources   *WorkloadResources `json:"resources"`   // 资源信息

	// 扩展字段
	EndTime     int64             `json:"end_time,omitempty"`    // 结束时间(Unix时间戳)
	Duration    int64             `json:"duration,omitempty"`    // 运行时长(秒)
	Labels      map[string]string `json:"labels,omitempty"`      // 标签
	Annotations map[string]string `json:"annotations,omitempty"` // 注解
}

// String 返回工作负载类型的字符串表示
func (wt WorkloadType) String() string {
	return string(wt)
}

// String 返回优先级的字符串表示
func (wp WorkloadPriority) String() string {
	switch wp {
	case PriorityLow:
		return "Low"
	case PriorityNormal:
		return "Normal"
	case PriorityHigh:
		return "High"
	case PriorityUrgent:
		return "Urgent"
	default:
		return "Unknown"
	}
}

// String 返回状态的字符串表示
func (ws WorkloadStatus) String() string {
	switch ws {
	case StatusPending:
		return "Pending"
	case StatusRunning:
		return "Running"
	case StatusSucceeded:
		return "Succeeded"
	case StatusFailed:
		return "Failed"
	case StatusCancelled:
		return "Cancelled"
	default:
		return "Unknown"
	}
}

// IsTerminated 检查工作负载是否已终止
func (w *Workload) IsTerminated() bool {
	return w.Status == StatusSucceeded || w.Status == StatusFailed || w.Status == StatusCancelled
}

// IsRunning 检查工作负载是否正在运行
func (w *Workload) IsRunning() bool {
	return w.Status == StatusRunning
}

// CalculateDuration 计算运行时长
func (w *Workload) CalculateDuration() {
	if w.StartTime > 0 {
		if w.EndTime > 0 {
			w.Duration = w.EndTime - w.StartTime
		} else if w.IsRunning() {
			w.Duration = time.Now().Unix() - w.StartTime
		}
	}
}

// FromRun 从 Run 对象转换为 Workload
func (w *Workload) FromRun(run *pb.Run) *Workload {
	w.ID = run.Id
	w.Name = run.TaskSource.SourceName
	w.Project = run.ProjectId
	w.StartTime = run.StartTime
	w.CreateUser = run.CreateUser

	// 根据 TaskSource 类型设置工作负载类型
	switch run.TaskSource.SourceType {
	case pb.TaskSourceType_PIPELINE:
		w.Type = WorkloadTypePipeline
	case pb.TaskSourceType_MODEL_TRAINING:
		w.Type = WorkloadTypeTraining
	case pb.TaskSourceType_MODEL_EVALUATE:
		w.Type = WorkloadTypeEvaluation
	default:
		w.Type = WorkloadTypePipeline
	}

	// 转换状态
	switch run.Status {
	case pb.Run_Running:
		w.Status = StatusRunning
	case pb.Run_Succeeded:
		w.Status = StatusSucceeded
	case pb.Run_Failed:
		w.Status = StatusFailed
	case pb.Run_Error:
		w.Status = StatusFailed
	default:
		w.Status = StatusPending
	}

	w.EndTime = run.EndTime
	w.CalculateDuration()

	return w
}

// NewResourceUsage 创建新的资源使用情况
func NewResourceUsage(used, allocated, limit int64) *ResourceUsage {
	return &ResourceUsage{
		Used:      used,
		Allocated: allocated,
		Limit:     limit,
	}
}

// GetUsagePercentage 获取资源使用百分比
func (ru *ResourceUsage) GetUsagePercentage() float64 {
	if ru.Limit == 0 {
		return 0
	}
	return float64(ru.Used) / float64(ru.Limit) * 100
}

// GetAllocationPercentage 获取资源分配百分比
func (ru *ResourceUsage) GetAllocationPercentage() float64 {
	if ru.Limit == 0 {
		return 0
	}
	return float64(ru.Allocated) / float64(ru.Limit) * 100
}

// IsOverAllocated 检查是否超分配
func (ru *ResourceUsage) IsOverAllocated() bool {
	return ru.Allocated > ru.Limit
}

// IsOverUsed 检查是否超使用
func (ru *ResourceUsage) IsOverUsed() bool {
	return ru.Used > ru.Limit
}

// NewWorkloadResources 创建新的工作负载资源
func NewWorkloadResources() *WorkloadResources {
	return &WorkloadResources{
		Memory:    &ResourceUsage{},
		CPU:       &ResourceUsage{},
		GpuMemory: &ResourceUsage{},
		GpuCore:   &ResourceUsage{},
		PodCount:  0,
	}
}

// SetMemoryResources 设置内存资源
func (wr *WorkloadResources) SetMemoryResources(used, allocated, limit int64) {
	wr.Memory = NewResourceUsage(used, allocated, limit)
}

// SetCPUResources 设置CPU资源
func (wr *WorkloadResources) SetCPUResources(used, allocated, limit int64) {
	wr.CPU = NewResourceUsage(used, allocated, limit)
}

// SetGpuMemoryResources 设置GPU内存资源
func (wr *WorkloadResources) SetGpuMemoryResources(used, allocated, limit int64) {
	wr.GpuMemory = NewResourceUsage(used, allocated, limit)
}

// SetGpuCoreResources 设置GPU核心资源
func (wr *WorkloadResources) SetGpuCoreResources(used, allocated, limit int64) {
	wr.GpuCore = NewResourceUsage(used, allocated, limit)
}

// HasGpuResources 检查是否有GPU资源
func (wr *WorkloadResources) HasGpuResources() bool {
	return (wr.GpuMemory != nil && wr.GpuMemory.Allocated > 0) ||
		(wr.GpuCore != nil && wr.GpuCore.Allocated > 0)
}

// NewWorkload 创建新的工作负载
func NewWorkload(id, name, project string, workloadType WorkloadType) *Workload {
	return &Workload{
		ID:          id,
		Name:        name,
		Project:     project,
		Type:        workloadType,
		Priority:    PriorityNormal,
		Status:      StatusPending,
		CreateTime:  time.Now().Unix(),
		Resources:   NewWorkloadResources(),
		Labels:      make(map[string]string),
		Annotations: make(map[string]string),
	}
}

func (wl *WorkloadService) ListWorkload(request *restful.Request, response *restful.Response) {
	body := &pb.SubmitRunBody{}
	request.ReadEntity(body)
	req := &pb.SubmitRunReq{
		ProjectId:     request.QueryParameter("project_id"),
		SubmitRunBody: body,
	}
	res, err := api.RunServiceRPC.SubmitRun(helper.ContextWithToken(request), req)
	util.PostResponse(request, response, res, err)
}
