package workload

import (
	"testing"
	"time"
)

func TestWorkloadType_String(t *testing.T) {
	tests := []struct {
		workloadType WorkloadType
		expected     string
	}{
		{WorkloadTypePipeline, "PIPELINE"},
		{WorkloadTypeTraining, "TRAINING"},
		{WorkloadTypeInference, "INFERENCE"},
		{WorkloadTypeEvaluation, "EVALUATION"},
		{WorkloadTypeDataProcess, "DATA_PROCESS"},
	}

	for _, test := range tests {
		if test.workloadType.String() != test.expected {
			t.Errorf("WorkloadType.String() = %v, expected %v", test.workloadType.String(), test.expected)
		}
	}
}

func TestWorkloadPriority_String(t *testing.T) {
	tests := []struct {
		priority WorkloadPriority
		expected string
	}{
		{PriorityLow, "Low"},
		{PriorityNormal, "Normal"},
		{PriorityHigh, "High"},
		{PriorityUrgent, "Urgent"},
		{WorkloadPriority(999), "Unknown"},
	}

	for _, test := range tests {
		if test.priority.String() != test.expected {
			t.<PERSON>rrorf("WorkloadPriority.String() = %v, expected %v", test.priority.String(), test.expected)
		}
	}
}

func TestWorkloadStatus_String(t *testing.T) {
	tests := []struct {
		status   WorkloadStatus
		expected string
	}{
		{StatusPending, "Pending"},
		{StatusRunning, "Running"},
		{StatusSucceeded, "Succeeded"},
		{StatusFailed, "Failed"},
		{StatusCancelled, "Cancelled"},
		{WorkloadStatus(999), "Unknown"},
	}

	for _, test := range tests {
		if test.status.String() != test.expected {
			t.Errorf("WorkloadStatus.String() = %v, expected %v", test.status.String(), test.expected)
		}
	}
}

func TestWorkload_IsTerminated(t *testing.T) {
	workload := NewWorkload("test-id", "test-name", "test-project", WorkloadTypePipeline)

	// 测试非终止状态
	workload.Status = StatusPending
	if workload.IsTerminated() {
		t.Error("Expected IsTerminated() to return false for StatusPending")
	}

	workload.Status = StatusRunning
	if workload.IsTerminated() {
		t.Error("Expected IsTerminated() to return false for StatusRunning")
	}

	// 测试终止状态
	terminatedStatuses := []WorkloadStatus{StatusSucceeded, StatusFailed, StatusCancelled}
	for _, status := range terminatedStatuses {
		workload.Status = status
		if !workload.IsTerminated() {
			t.Errorf("Expected IsTerminated() to return true for %v", status)
		}
	}
}

func TestWorkload_IsRunning(t *testing.T) {
	workload := NewWorkload("test-id", "test-name", "test-project", WorkloadTypePipeline)

	// 测试运行状态
	workload.Status = StatusRunning
	if !workload.IsRunning() {
		t.Error("Expected IsRunning() to return true for StatusRunning")
	}

	// 测试非运行状态
	nonRunningStatuses := []WorkloadStatus{StatusPending, StatusSucceeded, StatusFailed, StatusCancelled}
	for _, status := range nonRunningStatuses {
		workload.Status = status
		if workload.IsRunning() {
			t.Errorf("Expected IsRunning() to return false for %v", status)
		}
	}
}

func TestWorkload_CalculateDuration(t *testing.T) {
	workload := NewWorkload("test-id", "test-name", "test-project", WorkloadTypePipeline)

	// 测试未开始的任务
	workload.StartTime = 0
	workload.CalculateDuration()
	if workload.Duration != 0 {
		t.Error("Expected Duration to be 0 when StartTime is 0")
	}

	// 测试已完成的任务
	startTime := time.Now().Unix() - 100
	endTime := time.Now().Unix()
	workload.StartTime = startTime
	workload.EndTime = endTime
	workload.CalculateDuration()
	expectedDuration := endTime - startTime
	if workload.Duration != expectedDuration {
		t.Errorf("Expected Duration to be %d, got %d", expectedDuration, workload.Duration)
	}

	// 测试正在运行的任务
	workload.EndTime = 0
	workload.Status = StatusRunning
	workload.CalculateDuration()
	if workload.Duration <= 0 {
		t.Error("Expected Duration to be positive for running workload")
	}
}

func TestResourceUsage_GetUsagePercentage(t *testing.T) {
	// 测试正常情况
	ru := NewResourceUsage(50, 80, 100)
	expected := 50.0
	if ru.GetUsagePercentage() != expected {
		t.Errorf("Expected GetUsagePercentage() to return %f, got %f", expected, ru.GetUsagePercentage())
	}

	// 测试限制为0的情况
	ru = NewResourceUsage(50, 80, 0)
	if ru.GetUsagePercentage() != 0 {
		t.Error("Expected GetUsagePercentage() to return 0 when limit is 0")
	}
}

func TestResourceUsage_IsOverAllocated(t *testing.T) {
	// 测试超分配
	ru := NewResourceUsage(50, 120, 100)
	if !ru.IsOverAllocated() {
		t.Error("Expected IsOverAllocated() to return true when allocated > limit")
	}

	// 测试未超分配
	ru = NewResourceUsage(50, 80, 100)
	if ru.IsOverAllocated() {
		t.Error("Expected IsOverAllocated() to return false when allocated <= limit")
	}
}

func TestWorkloadResources_HasGpuResources(t *testing.T) {
	wr := NewWorkloadResources()

	// 测试无GPU资源
	if wr.HasGpuResources() {
		t.Error("Expected HasGpuResources() to return false when no GPU resources allocated")
	}

	// 测试有GPU内存资源
	wr.SetGpuMemoryResources(0, 1024, 2048)
	if !wr.HasGpuResources() {
		t.Error("Expected HasGpuResources() to return true when GPU memory is allocated")
	}

	// 重置并测试有GPU核心资源
	wr = NewWorkloadResources()
	wr.SetGpuCoreResources(0, 1, 2)
	if !wr.HasGpuResources() {
		t.Error("Expected HasGpuResources() to return true when GPU cores are allocated")
	}
}

func TestNewWorkload(t *testing.T) {
	id := "test-id"
	name := "test-name"
	project := "test-project"
	workloadType := WorkloadTypePipeline

	workload := NewWorkload(id, name, project, workloadType)

	if workload.ID != id {
		t.Errorf("Expected ID to be %s, got %s", id, workload.ID)
	}
	if workload.Name != name {
		t.Errorf("Expected Name to be %s, got %s", name, workload.Name)
	}
	if workload.Project != project {
		t.Errorf("Expected Project to be %s, got %s", project, workload.Project)
	}
	if workload.Type != workloadType {
		t.Errorf("Expected Type to be %s, got %s", workloadType, workload.Type)
	}
	if workload.Priority != PriorityNormal {
		t.Errorf("Expected Priority to be %d, got %d", PriorityNormal, workload.Priority)
	}
	if workload.Status != StatusPending {
		t.Errorf("Expected Status to be %d, got %d", StatusPending, workload.Status)
	}
	if workload.Resources == nil {
		t.Error("Expected Resources to be initialized")
	}
	if workload.Labels == nil {
		t.Error("Expected Labels to be initialized")
	}
	if workload.Annotations == nil {
		t.Error("Expected Annotations to be initialized")
	}
}
