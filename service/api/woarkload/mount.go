package workload

import (
	restfulspec "github.com/emicklei/go-restful-openapi/v2"
	restful "github.com/emicklei/go-restful/v3"
)

func NewRunApi(root string) *restful.WebService {
	return (&WorkloadService{}).WebService(root, "workload")
}

type WorkloadService struct{}

func (r *WorkloadService) WebService(root string, apiTag string) *restful.WebService {
	ws := new(restful.WebService)
	ws.Path(root).Consumes(restful.MIME_JSON).Produces(restful.MIME_JSON)

	apiTags := []string{apiTag}

	ws.Route(ws.GET("").
		To(r.ListWorkload).
		Doc("提交run").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.QueryParameter("project_id", "project id")).
		Reads(pb.SubmitRunBody{}).
		Returns(200, "OK", pb.RunId{}))

	return ws
}
