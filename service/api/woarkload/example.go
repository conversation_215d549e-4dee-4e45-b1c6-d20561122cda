package workload

import (
	"fmt"
	"time"
)

// ExampleUsage 展示如何使用 Workload 数据结构
func ExampleUsage() {
	// 创建一个新的工作负载
	workload := NewWorkload(
		"workload-001",
		"模型训练任务",
		"project-ai",
		WorkloadTypeTraining,
	)

	// 设置基本信息
	workload.Priority = PriorityHigh
	workload.Status = StatusRunning
	workload.RunNode = "node-gpu-01"
	workload.StartTime = time.Now().Unix()
	workload.CreateUser = "<EMAIL>"
	workload.Description = "使用深度学习模型进行图像分类训练"

	// 设置资源信息
	workload.Resources.SetMemoryResources(8192, 16384, 32768)    // 8GB使用, 16GB分配, 32GB限制
	workload.Resources.SetCPUResources(4000, 8000, 16000)       // 4核使用, 8核分配, 16核限制
	workload.Resources.SetGpuMemoryResources(12288, 24576, 24576) // 12GB使用, 24GB分配, 24GB限制
	workload.Resources.SetGpuCoreResources(1, 2, 4)             // 1核使用, 2核分配, 4核限制
	workload.Resources.PodCount = 2

	// 添加标签和注解
	workload.Labels["environment"] = "production"
	workload.Labels["team"] = "ai-research"
	workload.Annotations["scheduler"] = "gpu-scheduler"
	workload.Annotations["cost-center"] = "research-dept"

	// 打印工作负载信息
	fmt.Printf("工作负载信息:\n")
	fmt.Printf("ID: %s\n", workload.ID)
	fmt.Printf("名称: %s\n", workload.Name)
	fmt.Printf("项目: %s\n", workload.Project)
	fmt.Printf("类型: %s\n", workload.Type.String())
	fmt.Printf("优先级: %s\n", workload.Priority.String())
	fmt.Printf("状态: %s\n", workload.Status.String())
	fmt.Printf("运行节点: %s\n", workload.RunNode)
	fmt.Printf("创建用户: %s\n", workload.CreateUser)
	fmt.Printf("描述: %s\n", workload.Description)

	// 打印资源信息
	fmt.Printf("\n资源使用情况:\n")
	if workload.Resources.Memory != nil {
		fmt.Printf("内存: %dMB/%dMB (%.1f%%)\n",
			workload.Resources.Memory.Used,
			workload.Resources.Memory.Limit,
			workload.Resources.Memory.GetUsagePercentage())
	}

	if workload.Resources.CPU != nil {
		fmt.Printf("CPU: %dm/%dm (%.1f%%)\n",
			workload.Resources.CPU.Used,
			workload.Resources.CPU.Limit,
			workload.Resources.CPU.GetUsagePercentage())
	}

	if workload.Resources.GpuMemory != nil {
		fmt.Printf("GPU内存: %dMB/%dMB (%.1f%%)\n",
			workload.Resources.GpuMemory.Used,
			workload.Resources.GpuMemory.Limit,
			workload.Resources.GpuMemory.GetUsagePercentage())
	}

	if workload.Resources.GpuCore != nil {
		fmt.Printf("GPU核心: %d/%d (%.1f%%)\n",
			workload.Resources.GpuCore.Used,
			workload.Resources.GpuCore.Limit,
			workload.Resources.GpuCore.GetUsagePercentage())
	}

	fmt.Printf("Pod数量: %d\n", workload.Resources.PodCount)

	// 检查状态
	fmt.Printf("\n状态检查:\n")
	fmt.Printf("是否正在运行: %t\n", workload.IsRunning())
	fmt.Printf("是否已终止: %t\n", workload.IsTerminated())
	fmt.Printf("是否有GPU资源: %t\n", workload.Resources.HasGpuResources())

	// 检查资源使用情况
	fmt.Printf("\n资源警告:\n")
	if workload.Resources.Memory.IsOverUsed() {
		fmt.Printf("警告: 内存使用超限!\n")
	}
	if workload.Resources.CPU.IsOverUsed() {
		fmt.Printf("警告: CPU使用超限!\n")
	}
	if workload.Resources.GpuMemory.IsOverUsed() {
		fmt.Printf("警告: GPU内存使用超限!\n")
	}

	// 模拟任务完成
	time.Sleep(1 * time.Second)
	workload.Status = StatusSucceeded
	workload.EndTime = time.Now().Unix()
	workload.CalculateDuration()

	fmt.Printf("\n任务完成:\n")
	fmt.Printf("状态: %s\n", workload.Status.String())
	fmt.Printf("运行时长: %d秒\n", workload.Duration)
}

// ExampleFromRun 展示如何从 Run 对象创建 Workload
func ExampleFromRun() {
	// 这里只是一个示例，实际使用时需要真实的 pb.Run 对象
	fmt.Printf("从 Run 对象创建 Workload 的示例请参考 FromRun 方法\n")
}
