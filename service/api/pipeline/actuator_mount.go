package pipeline

import (
	restfulspec "github.com/emicklei/go-restful-openapi/v2"
	restful "github.com/emicklei/go-restful/v3"
	pb "transwarp.io/aip/llmops-common/pb/common"
	"transwarp.io/mlops/pipeline/rpc"
)

func NewActuatorApi(root string) *restful.WebService {
	return (&ActuatorResource{Client: rpc.ActuatorClient}).WebService(root, "actuator")
}

type ActuatorResource struct {
	Client pb.ActuatorServiceClient
}

func (this *ActuatorResource) WebService(root string, apiTag string) *restful.WebService {
	ws := new(restful.WebService)
	ws.Path(root).Consumes(restful.MIME_JSON).Produces(restful.MIME_JSON)

	apiTags := []string{apiTag}

	ws.Route(ws.GET("/health").
		To(this.CheckHeath).
		Doc("get all services").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Reads(pb.CheckHealthReq{}).
		Returns(200, "OK", pb.HealthRsp{}))

	return ws
}
