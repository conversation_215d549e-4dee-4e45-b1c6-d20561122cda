package pipeline

import (
	restfulspec "github.com/emicklei/go-restful-openapi/v2"
	"github.com/emicklei/go-restful/v3"
	pb "transwarp.io/aip/llmops-common/pb/pipeline"
)

func NewRunApiV2(root string) *restful.WebService {
	return (&RunResource{}).WebServiceV2(root, "runV2")
}

func (r *RunResource) WebServiceV2(root string, apiTag string) *restful.WebService {
	ws := new(restful.WebService)
	ws.Path(root).Consumes(restful.MIME_JSON).Produces(restful.MIME_JSON)

	apiTags := []string{apiTag}

	ws.Route(ws.POST("").
		To(r.SubmitRunV2).
		Doc("提交run").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.QueryParameter("project_id", "project id")).
		Reads(pb.SubmitRunBody{}).
		Returns(200, "OK", pb.RunId{}))

	ws.Route(ws.GET("/{run_id}").
		To(r.GetRunByIdV2).
		Doc("根据id获取run").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.PathParameter("run_id", "run id")).
		Returns(200, "OK", pb.Run{}))

	ws.Route(ws.GET("").
		To(r.ListRunsV2).
		Doc("获取所有run").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.QueryParameter("project_id", "筛选字段，根据项目id筛选")).
		Param(ws.QueryParameter("page_size", "分页字段,每页多少条数据，为0则不分页，默认值为0")).
		Param(ws.QueryParameter("page", "分页字段,代表第几页数据，默认值为1")).
		Param(ws.QueryParameter("sort_by", "分页字段,指定排序字段，支持create_time;默认值为create_time")).
		Param(ws.QueryParameter("is_desc", "分页字段,是否降序排列，默认值为true")).
		Param(ws.QueryParameter("source_id", "筛选字段，源任务id,可多选，用逗号分隔")).
		Param(ws.QueryParameter("source_name", "筛选字段，源任务名称,模糊匹配")).
		Param(ws.QueryParameter("source_type", "筛选字段，源任务类型 1:PIPELINE 2:MODEL_TRAINING 3:MODEL_EVALUATE")).
		Param(ws.QueryParameter("state", "筛选字段，run状态，0:失败 1：错误 2：成功 3：运行中")).
		Param(ws.QueryParameter("from", "筛选字段，根据start_time筛选，from<start_time")).
		Param(ws.QueryParameter("to", "筛选字段，根据start_time筛选,start_time<to")).
		Param(ws.QueryParameter("create_user", "筛选字段，根据用户筛选")).
		Returns(200, "OK", pb.RunsPage{}))

	return ws
}
