package pipeline

import (
	restfulspec "github.com/emicklei/go-restful-openapi/v2"
	restful "github.com/emicklei/go-restful/v3"
	pb "transwarp.io/aip/llmops-common/pb/pipeline"
	"transwarp.io/mlops/pipeline/rpc"
)

func NewPipelineApiV2(root string) *restful.WebService {
	return (&PipelineResource{Client: rpc.PipelineClient}).WebServiceV2(root, "pipelineV2")
}

func (r *PipelineResource) WebServiceV2(root string, apiTag string) *restful.WebService {
	ws := new(restful.WebService)
	ws.Path(root).Consumes(restful.MIME_JSON).Produces(restful.MIME_JSON)

	apiTags := []string{apiTag}

	ws.Route(ws.POST("/version/{version_id}/save").
		To(r.SavePipelineVersionV2).
		Doc("修改工作流版本的流程图和调度信息").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.PathParameter("version_id", "工作流版本id")).
		Reads(pb.SavePipelineVersionBody{}).
		Returns(200, "OK", pb.PipelineVersion{}))

	ws.Route(ws.GET("/version/{id}").
		To(r.GetPipelineVersionV2).
		Doc("根据id获取工作流版本").
		Metadata(restfulspec.KeyOpenAPITags, apiTags).
		Param(ws.PathParameter("id", "工作流版本id")).
		Returns(200, "OK", pb.PipelineVersion{}))

	return ws
}
